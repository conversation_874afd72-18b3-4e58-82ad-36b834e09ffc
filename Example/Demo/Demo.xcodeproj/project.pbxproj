// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		6930F2D62DAF7EBC0091B618 /* Sharing in Frameworks */ = {isa = PBXBuildFile; productRef = 6930F2D52DAF7EBC0091B618 /* Sharing */; };
		6930F3672DAF813D0091B618 /* KLineLib in Frameworks */ = {isa = PBXBuildFile; productRef = 6930F3662DAF813D0091B618 /* KLineLib */; };
		6930F3A72DAF85E80091B618 /* SwiftUIIntrospect in Frameworks */ = {isa = PBXBuildFile; productRef = 6930F3A62DAF85E80091B618 /* SwiftUIIntrospect */; };
		6930F5462DAFB22D0091B618 /* FLEX in Frameworks */ = {isa = PBXBuildFile; productRef = 6930F5452DAFB22D0091B618 /* FLEX */; };
		69AAC6272DB10D10006A9050 /* WebSocketClient in Frameworks */ = {isa = PBXBuildFile; productRef = 69AAC6262DB10D10006A9050 /* WebSocketClient */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		6930F3DC2DAF86580091B618 /* WebSocketClient */ = {isa = PBXFileReference; lastKnownFileType = wrapper; name = WebSocketClient; path = ../WebSocketClient; sourceTree = "<group>"; };
		69D2B7EC2DA8C7F900AF224A /* Demo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Demo.app; sourceTree = BUILT_PRODUCTS_DIR; };
		69D2B8152DA8D39800AF224A /* ChiefAPIClient */ = {isa = PBXFileReference; lastKnownFileType = wrapper; name = ChiefAPIClient; path = ../..; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		69D2B7EE2DA8C7F900AF224A /* Demo */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Demo;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		69D2B7E92DA8C7F900AF224A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6930F5462DAFB22D0091B618 /* FLEX in Frameworks */,
				69AAC6272DB10D10006A9050 /* WebSocketClient in Frameworks */,
				6930F3A72DAF85E80091B618 /* SwiftUIIntrospect in Frameworks */,
				6930F3672DAF813D0091B618 /* KLineLib in Frameworks */,
				6930F2D62DAF7EBC0091B618 /* Sharing in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		69D2B7E32DA8C7F900AF224A = {
			isa = PBXGroup;
			children = (
				69D2B7EE2DA8C7F900AF224A /* Demo */,
				69D2B8142DA8D39800AF224A /* Frameworks */,
				69D2B7ED2DA8C7F900AF224A /* Products */,
			);
			sourceTree = "<group>";
		};
		69D2B7ED2DA8C7F900AF224A /* Products */ = {
			isa = PBXGroup;
			children = (
				69D2B7EC2DA8C7F900AF224A /* Demo.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		69D2B8142DA8D39800AF224A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				6930F3DC2DAF86580091B618 /* WebSocketClient */,
				69D2B8152DA8D39800AF224A /* ChiefAPIClient */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		69D2B7EB2DA8C7F900AF224A /* Demo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 69D2B7FA2DA8C7FA00AF224A /* Build configuration list for PBXNativeTarget "Demo" */;
			buildPhases = (
				69D2B7E82DA8C7F900AF224A /* Sources */,
				69D2B7E92DA8C7F900AF224A /* Frameworks */,
				69D2B7EA2DA8C7F900AF224A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				69D2B7EE2DA8C7F900AF224A /* Demo */,
			);
			name = Demo;
			packageProductDependencies = (
				6930F2D52DAF7EBC0091B618 /* Sharing */,
				6930F3662DAF813D0091B618 /* KLineLib */,
				6930F3A62DAF85E80091B618 /* SwiftUIIntrospect */,
				6930F5452DAFB22D0091B618 /* FLEX */,
				69AAC6262DB10D10006A9050 /* WebSocketClient */,
			);
			productName = Demo;
			productReference = 69D2B7EC2DA8C7F900AF224A /* Demo.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		69D2B7E42DA8C7F900AF224A /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					69D2B7EB2DA8C7F900AF224A = {
						CreatedOnToolsVersion = 16.2;
					};
				};
			};
			buildConfigurationList = 69D2B7E72DA8C7F900AF224A /* Build configuration list for PBXProject "Demo" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 69D2B7E32DA8C7F900AF224A;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				6930F2D42DAF7EBC0091B618 /* XCRemoteSwiftPackageReference "swift-sharing" */,
				6930F3A52DAF85E80091B618 /* XCRemoteSwiftPackageReference "swiftui-introspect" */,
				6930F5442DAFB22D0091B618 /* XCRemoteSwiftPackageReference "FLEX" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 69D2B7ED2DA8C7F900AF224A /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				69D2B7EB2DA8C7F900AF224A /* Demo */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		69D2B7EA2DA8C7F900AF224A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		69D2B7E82DA8C7F900AF224A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		69D2B7F82DA8C7FA00AF224A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		69D2B7F92DA8C7FA00AF224A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		69D2B7FB2DA8C7FA00AF224A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"Demo/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 15;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.stx.Demo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		69D2B7FC2DA8C7FA00AF224A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"Demo/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				ENABLE_PREVIEWS = YES;
				EXCLUDED_SOURCE_FILE_NAMES = "FLEX*";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 15;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.stx.Demo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		69D2B7E72DA8C7F900AF224A /* Build configuration list for PBXProject "Demo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				69D2B7F82DA8C7FA00AF224A /* Debug */,
				69D2B7F92DA8C7FA00AF224A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		69D2B7FA2DA8C7FA00AF224A /* Build configuration list for PBXNativeTarget "Demo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				69D2B7FB2DA8C7FA00AF224A /* Debug */,
				69D2B7FC2DA8C7FA00AF224A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		6930F2D42DAF7EBC0091B618 /* XCRemoteSwiftPackageReference "swift-sharing" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/pointfreeco/swift-sharing.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.4.0;
			};
		};
		6930F3A52DAF85E80091B618 /* XCRemoteSwiftPackageReference "swiftui-introspect" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/siteline/swiftui-introspect.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.3.0;
			};
		};
		6930F5442DAFB22D0091B618 /* XCRemoteSwiftPackageReference "FLEX" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/FLEXTool/FLEX.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.22.10;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		6930F2D52DAF7EBC0091B618 /* Sharing */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6930F2D42DAF7EBC0091B618 /* XCRemoteSwiftPackageReference "swift-sharing" */;
			productName = Sharing;
		};
		6930F3662DAF813D0091B618 /* KLineLib */ = {
			isa = XCSwiftPackageProductDependency;
			productName = KLineLib;
		};
		6930F3A62DAF85E80091B618 /* SwiftUIIntrospect */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6930F3A52DAF85E80091B618 /* XCRemoteSwiftPackageReference "swiftui-introspect" */;
			productName = SwiftUIIntrospect;
		};
		6930F5452DAFB22D0091B618 /* FLEX */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6930F5442DAFB22D0091B618 /* XCRemoteSwiftPackageReference "FLEX" */;
			productName = FLEX;
		};
		69AAC6262DB10D10006A9050 /* WebSocketClient */ = {
			isa = XCSwiftPackageProductDependency;
			productName = WebSocketClient;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 69D2B7E42DA8C7F900AF224A /* Project object */;
}
