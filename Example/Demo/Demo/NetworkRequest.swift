//
//  NetworkRequest.swift
//  Demo
//
//  Created by stx80501 on 2025/4/17.
//

import Foundation

typealias HTTPHeaders = [String: String]

struct KLineData: Decodable {
    enum CodingKeys: String, CodingKey {
        case symbol = "s"
        case timestamp = "t"
        case close = "c"
        case low = "l"
        case high = "h"
        case volume = "v"
        case open = "o"
    }
    var symbol: String
    var timestamp: Int
    var close: Double
    var low: Double
    var high: Double
    var volume: Double
    var open: Double
    init(from decoder: any Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.symbol = try container.decode(String.self, forKey: .symbol)
        self.timestamp = try container.decode(Int.self, forKey: .timestamp)
        self.close = try container.decode(Double.self, forKey: .close)
        self.low = try container.decode(Double.self, forKey: .low)
        self.high = try container.decode(Double.self, forKey: .high)
        self.volume = try container.decode(Double.self, forKey: .volume)
        self.open = try container.decode(Double.self, forKey: .open)
    }
}

enum MethodType {
    case get
    case post

    var httpMethod: String {
        switch self {
        case .get: "GET"
        case .post: "POSt"
        }
    }
}

enum ParameterEncoding {
    case jsonEncoding
    case urlEncoding
}

enum ChiefRequest {
    case klineHistory
    
    var path: String {
        switch self {
        case .klineHistory: "/quote/api/v1/klines"
        }
    }
    var url: URL? {
        var components = URLComponents()
        components.scheme = "https"
        components.host = ChiefRequest.host
        components.path = path
        return components.url
    }
    private static let host = "api-test.stx365.com"
    
}

struct ChiefResponse<ResponseData: Decodable>: Decodable {
    var traceId: String
    var data: ResponseData
    var msg: String?
    var code: Int
}

private let chiefHeaders = [
    "Content-Type": "application/json",
    "_t": "eyJhbGciOiJIUzI1NiJ9.**********************************************************************************.KSpBAWXCxBZZ2xAYCn2g9L8Bxhud9JFpQLdInbFyPFU",
    "lang": "zh_CN",
    "version": "0.5.1",
    "_s": "ios",
]

actor NetworkRequest {
    
    static let shared = NetworkRequest()
    private init() {}

    func post<T: Decodable, U: Encodable>(
        url: URL?,
        headers: HTTPHeaders = chiefHeaders,
        params: U?
    ) async throws -> T {
        guard let requestURL = url
        else {
            throw URLError(.badURL)
        }
        var request = URLRequest(url: requestURL)
        request.httpMethod = MethodType.post.httpMethod
        request.timeoutInterval = 60
        
        headers.forEach { key, value in
            request.addValue(value, forHTTPHeaderField: key)
        }
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        if let params {
            request.httpBody = try JSONEncoder().encode(params)
        }
        let session = URLSession(configuration: .ephemeral)
        let (data, _) = try await session.data(for: request)
        return try JSONDecoder().decode(ChiefResponse<T>.self, from: data).data
    }
    
    func get<T: Decodable>(
        url: String,
        headers: HTTPHeaders,
        params: [String: Any]? = nil
    ) async throws -> T {
        guard
            let encodedURL = url.addingPercentEncoding(
                withAllowedCharacters: .urlQueryAllowed),
            let requestURL = URL(string: encodedURL)
        else {
            throw URLError(.badURL)
        }
        var request = URLRequest(url: requestURL)
        request.httpMethod = MethodType.post.httpMethod
        request.timeoutInterval = 60
        
        headers.forEach { key, value in
            request.addValue(value, forHTTPHeaderField: key)
        }
        request.httpBody = params?
                            .map { "\($0.key)=\($0.value)" }
                            .joined(separator: "&")
                            .data(using: .utf8)
        request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
        let session = URLSession(configuration: .ephemeral)
        let (data, _) = try await session.data(for: request)
        return try JSONDecoder().decode(ChiefResponse<T>.self, from: data).data
    }
}
