import SwiftUI
import UIKit

public class AlwaysPopoverContentViewController<V>: UIHostingController<V>,
    UIPopoverPresentationControllerDelegate
where V: View {
    var isPresented: Binding<Bool>

    public init(rootView: V, isPresented: Binding<Bool>) {
        self.isPresented = isPresented
        super.init(rootView: rootView)
    }

    public required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    public override func viewDidLoad() {
        super.viewDidLoad()

        let size = sizeThatFits(in: UIView.layoutFittingExpandedSize)
        preferredContentSize = size
    }

    public func presentationControllerDidDismiss(
        _ presentationController: UIPresentationController
    ) {
        self.isPresented.wrappedValue = false
    }

    public func adaptivePresentationStyle(
        for controller: UIPresentationController,
        traitCollection: UITraitCollection
    ) -> UIModalPresentationStyle {
        .none
    }
}

struct AlwaysPopoverModifier<PopoverContent>: ViewModifier
where PopoverContent: View {

    let isPresented: Binding<Bool>
    let arrowDirection: UIPopoverArrowDirection
    let contentBlock: () -> PopoverContent
    private struct Store {
        var anchorView = UIView()
    }
    @State private var store = Store()
    func body(content: Content) -> some View {
        if isPresented.wrappedValue {
            presentPopover()
        }
        return
            content
            .background(InternalAnchorView(uiView: store.anchorView))
            .onChange(of: isPresented.wrappedValue) {
                if !$0 {
                    dismissPopover()
                }
            }
    }
    
    private func dismissPopover() {
        if let controller = store.anchorView.closestController() {
            controller.dismiss(animated: true)
        }
    }

    private func presentPopover() {
        let contentController = AlwaysPopoverContentViewController(
            rootView: contentBlock(),
            isPresented: isPresented
        )
        contentController.modalPresentationStyle = .popover

        let view = store.anchorView
        guard let popover = contentController.popoverPresentationController
        else { return }
        popover.sourceView = view
        popover.delegate = contentController
        switch arrowDirection {
        case .left:
            popover.sourceRect = CGRect(
                x: view.bounds.maxX + 6,
                y: view.bounds.midY,
                width: 0,
                height: 0
            )
        case .right:
            popover.sourceRect = CGRect(
                x: view.bounds.minX - 6,
                y: view.bounds.midY,
                width: 0,
                height: 0
            )
        case .up:
            popover.sourceRect = CGRect(
                x: view.bounds.midX,
                y: view.bounds.maxY + 6,
                width: 0,
                height: 0
            )
        case .down:
            popover.sourceRect = CGRect(
                x: view.bounds.midX,
                y: view.bounds.minY - 6,
                width: 0,
                height: 0
            )
        default: popover.sourceRect = view.bounds
        }

        guard let sourceVC = view.closestController() else { return }
        if let presentedVC = sourceVC.presentedViewController {
            presentedVC.dismiss(animated: true) {
                sourceVC.present(contentController, animated: true)
            }
        } else {
            sourceVC.present(contentController, animated: true)
        }
    }
}

private struct InternalAnchorView: UIViewRepresentable {
    typealias UIViewType = UIView
    let uiView: UIView

    func makeUIView(context: Self.Context) -> Self.UIViewType {
        uiView
    }

    func updateUIView(_ uiView: Self.UIViewType, context: Self.Context) {}
}

extension View {
    public func alwaysPopover<Content>(
        isPresented: Binding<Bool>,
        arrowDirection: UIPopoverArrowDirection = .any,
        @ViewBuilder content: @escaping () -> Content
    ) -> some View where Content: View {
        self.modifier(
            AlwaysPopoverModifier(
                isPresented: isPresented,
                arrowDirection: arrowDirection,
                contentBlock: content
            )
        )
    }
}
