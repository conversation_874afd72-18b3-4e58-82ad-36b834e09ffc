//
//  DemoApp.swift
//  Demo
//
//  Created by stx80501 on 2025/4/11.
//

import SwiftUI
import Dependencies
import WebSocketClient

#if DEBUG
import FLEX
import UIKit

extension UIWindow{
    open override func motionEnded(_ motion: UIEvent.EventSubtype, with event: UIEvent?) {
        FLEXManager.shared.showExplorer()
    }
}
#endif

@main
struct DemoApp: App {
    init() {
        prepareDependencies {
            $0.webSocket = WebSocketKey.liveValue
            $0.webSocketCache = DefaultWebSocketCacheKey.liveValue
        }
    }
    @State private var webSocketLoaded = false
    @State private var isConnecting = true
    @State private var scale: CGFloat = 1.0
    var body: some Scene {
        WindowGroup {
            //KLineContentView()
          KlineTestView()
//            if webSocketLoaded {
//                KLineContentView()
//            } else {
//                VStack(spacing: 20) {
//                    Circle()
//                        .fill(.primary)
//                        .frame(width: 40, height: 40)
//                        .scaleEffect(scale)
//                        .onAppear {
//                            scale = 1.2
//                        }
//                        .animation(
//                            .easeInOut(duration: 0.3)
//                            .repeatForever(autoreverses: true),
//                            value: scale
//                        )
//                        .task {
//                            @Dependency(\.webSocket) var webSocket
//                            await webSocket.startConnect()
//                            for await isConnected in await webSocket.isConnected() {
//                                if isConnected {
//                                    isConnecting = false
//                                    webSocketLoaded = true
//                                }
//                            }
//                        }
//                    Text("Connecting...")
//                        .font(.title)
//                        .fontWeight(.semibold)
//                }
//            }
        }
    }
}
