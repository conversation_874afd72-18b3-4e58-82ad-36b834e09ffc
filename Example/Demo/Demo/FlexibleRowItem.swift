import SwiftUI

func HexColor(_ hex: Int, _ alpha: Double = 1.0) -> Color {
    return Color(
        red: Double(((hex & 0xFF0000) >> 16)) / 255.0,
        green: Double(((hex & 0xFF00) >> 8)) / 255.0,
        blue: Double((hex & 0xFF)) / 255.0, opacity: alpha)
}

public enum FlexibleRowItem {
    case flexible(flex: CGFloat, view: AnyView)
    case fixed(width: CGFloat, view: AnyView)
    var fixedWidth: CGFloat? {
        switch self {
        case .flexible: nil
        case let .fixed(width, _): width
        }
    }
    var flex: CGFloat? {
        switch self {
        case let .flexible(flex, _): flex
        case .fixed: nil
        }
    }
}

public struct FlexibleRowView: View {
    let items: [FlexibleRowItem]
    private let spacing: CGFloat

    public init(items: [FlexibleRowItem], spacing: CGFloat = 8) {
        self.items = items
        self.spacing = spacing
    }

    public var body: some View {
        GeometryReader { geometry in
            // 计算子视图总数
            let n = items.count
            // 计算总间距（若少于2个子视图，则无间距）
            let totalSpacing = CGFloat(max(0, n - 1)) * spacing
            // 计算所有固定子视图的总宽度
            let totalFixedWidth = items.compactMap { $0.fixedWidth }.reduce(
                0, +)
            // 计算所有灵活子视图的总 flex 值
            let totalFlex = items.compactMap { $0.flex }.reduce(0, +)
            // 计算灵活子视图的可用宽度
            let availableWidth =
                geometry.size.width - totalSpacing - totalFixedWidth
            // 计算每单位 flex 的宽度（若无灵活子视图，则为 0）
            let widthPerFlex = totalFlex > 0 ? availableWidth / totalFlex : 0
            // 为每个子视图计算宽度
            let widths = items.map { item in
                switch item {
                case .flexible(let flex, _):
                    return flex * widthPerFlex
                case .fixed(let width, _):
                    return width
                }
            }

            // 使用 HStack 布局子视图
            HStack(spacing: spacing) {
                ForEach(0..<n, id: \.self) { index in
                    switch items[index] {
                    case .flexible(_, let view):
                        view.frame(width: max(0, widths[index]))
                    case .fixed(_, let view):
                        view.frame(width: max(0, widths[index]))
                    }
                }
            }
            .frame(maxHeight: .infinity)
        }
    }
}

extension View {
    public func flexible(_ flex: CGFloat) -> FlexibleRowItem {
        .flexible(flex: flex, view: AnyView(self))
    }

    public func fixed(_ width: CGFloat) -> FlexibleRowItem {
        .fixed(width: width, view: AnyView(self))
    }
}
