//
//  TransactionRecordSheet.swift
//  Demo
//
//  Created by stx80501 on 2025/4/17.
//

import SwiftUI
import KLineLib

struct TransactionRecordSheet: View {
    var kLineData: [KLineModel]
    init(kLineData: [KLineModel]) {
        self.kLineData = kLineData
    }
    var body: some View {
        Text(/*@START_MENU_TOKEN@*/"Hello, World!"/*@END_MENU_TOKEN@*/)
    }
}

#Preview {
    TransactionRecordSheet(kLineData: [])
}
