//
//  CodableArray.swift
//  Demo
//
//  Created by <PERSON> on 2025/6/30.
//


/// This is a test view to show the basic usage of KLineSwiftUIView,
/// please do not use it in production.
///
///
import Combine
import DependenciesAdditions
import Sharing
import SwiftUI
import SwiftUIIntrospect

extension SharedKey {
    public static func codableArray<T: Codable>(key: String) -> Self
    where Self == CodableArray<T>.Default {
        Self[CodableArray(key: key), default: []]
    }
}
public struct CodableArray<T: Codable>: SharedKey {
    public typealias Value = [T]
    private let key: String
    public init(key: String) {
        self.key = key
    }
    public var id: String {
        "com.chief-app.new.shared-array.\(key)"
    }
    @Dependency(\.defaultAppStorage) private var appStorage
    @Dependency(\.decode) private var decode
    @Dependency(\.encode) private var encode
    public func load(
        context: LoadContext<Value>,
        continuation: LoadContinuation<Value>
    ) {
        guard let jsonData = appStorage.string(forKey: id)?.data(using: .utf8)
        else {
            continuation.resume(returning: [])
            return
        }
        continuation.resume(
            with: Result {
                try decode([T].self, from: jsonData)
            }
        )
    }

    public func subscribe(
        context: LoadContext<[T]>,
        subscriber: SharedSubscriber<[T]>
    ) -> SharedSubscription {
        SharedSubscription {}
    }

    public func save(
        _ value: [T],
        context: SaveContext,
        continuation: SaveContinuation
    ) {
        do {
            let jsonData = try encode(value)
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                appStorage.set(jsonString, forKey: id)
            }
            continuation.resume()
        } catch {
            continuation.resume(throwing: error)
        }
    }
}
