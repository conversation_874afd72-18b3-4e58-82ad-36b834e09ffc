import Foundation
import SwiftUI
import KLineLib

public struct KLineDrawSettingsSwiftUIView: View {
    @Binding var drawShapeType: KLineDrawShapeType?
    @Binding var showDrawSettingsView: Bool
    @State private var fillColor = KLineDrawViewModel.shared.drawParam.drawThemColor
    @State private var lineWidth = KLineDrawViewModel.shared.drawParam.drawLineWidth
    @State private var lineDashes = KLineDrawViewModel.shared.drawParam.drawLineDashes
    @State private var showColorPicker = false
    @State private var showLineWidthPicker = false
    init(drawShapeType: Binding<KLineDrawShapeType?>, showDrawSettingsView: Binding<Bool>) {
        self._drawShapeType = drawShapeType
        self._showDrawSettingsView = showDrawSettingsView
    }
    public var body: some View {
        HStack(spacing: 20) {
            Button {
                showColorPicker.toggle()
            } label: {
                Rectangle()
                    .fill(Color(uiColor: fillColor.hexColor()))
                    .frame(width: 20, height: 20)
                    .cornerRadius(4)
                    .contentShape(.rect)
            }
            .buttonStyle(.plain)
            .alwaysPopover(isPresented: $showColorPicker, arrowDirection: .down) {
                LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible()), GridItem(.flexible())]) {
                    ForEach(Array(drawLineColorList().enumerated()), id: \.offset) { _, color in
                        Button {
                            KLineDrawViewModel.shared.drawParam.drawThemColor = UIColor(color).toHex()
                            fillColor = KLineDrawViewModel.shared.drawParam.drawThemColor
                            showColorPicker = false
                        } label: {
                            Rectangle()
                                .fill(color)
                                .frame(width: 20, height: 20)
                                .cornerRadius(4)
                        }
                        .buttonStyle(.plain)
                    }
                }
                .padding()
                .fixedSize()
            }
            
            Button {
                showLineWidthPicker.toggle()
            } label: {
                ZStack {
                    Rectangle()
                        .fill(Color(uiColor: fillColor.hexColor()))
                        .frame(width: 20, height: lineWidth)
                }
                .frame(width: 20, height: 20)
                .contentShape(.rect)
            }
            .buttonStyle(.plain)
            .alwaysPopover(isPresented: $showLineWidthPicker, arrowDirection: .down) {
                VStack {
                    ForEach([0.5, 1, 1.5, 2], id: \.self) { lineHeight in
                        Button {
                            
                        } label: {
                            ZStack {
                                Color.clear
                                    .frame(width: 20, height: 20)
                                Rectangle()
                                    .fill(Color(uiColor: fillColor.hexColor()))
                                    .frame(width: 20, height: lineHeight)
                            }
                            .contentShape(.rect)
                        }
                        .buttonStyle(.plain)
                    }
                }
                .padding()
                .fixedSize()
            }
            Button {
                
            } label: {
                ZStack {
                    Color.clear
                        .frame(width: 20, height: 20)
                    Rectangle()
                        .fill(Color(uiColor: fillColor.hexColor()))
                        .frame(width: 20, height: 1)
                }
                .contentShape(.rect)
            }
            .buttonStyle(.plain)
            
            Divider()
                .frame(width: 1, height: 16)
            Button {
                drawShapeType = nil
                withAnimation {
                    showDrawSettingsView = false
                }
            } label: {
                Image(systemName: "xmark.circle.fill")
                    .resizable()
                    .scaledToFit()
                    .frame(width: 20, height: 20)
                    .foregroundStyle(Color.primary)
                    .contentShape(.rect)
            }
            .buttonStyle(.plain)
        }
        .padding(.horizontal)
        .background(
            Capsule()
                .fill(Color(uiColor: KLineConfig.themeManager.dividerColor()))
                .frame(height: 44)
        )
        .padding()
    }
}
