//
//  TransactionRecordList.swift
//  Demo
//
//  Created by stx80501 on 2025/4/17.
//

import SwiftUI
import KLineLib
import SwiftUIIntrospect

struct TransactionRecordList: View {
    var kLineData: [KLineModel]
    init(kLineData: [KLineModel]) {
        self.kLineData = kLineData
    }
    var body: some View {
        VStack {
            FlexibleRowView(
                items: [
                    Text("时间")
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .flexible(3),
                    Text("价格")
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .flexible(4),
                    Text("股数")
                        .frame(maxWidth: .infinity, alignment: .trailing)
                        .flexible(3)
                ]
            )
            .fixedSize(horizontal: false, vertical: true)
            List {
                ForEach(kLineData) { model in
                    FlexibleRowView(
                        items: [
                            Text(Date(timeIntervalSince1970: model.timestamp), style: .time)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .flexible(3),
                            Text(model.close, format: .currency(code: "$"))
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .flexible(4),
                            Text(String(format: "%.2f", model.amount))
                                .frame(maxWidth: .infinity, alignment: .trailing)
                                .flexible(3)
                        ]
                    )
                    .frame(height: 20)
                }
                .listRowInsets(EdgeInsets(top: -10, leading: 0, bottom: -10, trailing: 0))
                .listRowSeparator(.hidden)
            }
            .listStyle(.plain)
            .environment(\.defaultMinListRowHeight, 20)
            
            Spacer()
        }
        .foregroundStyle(.secondary)
        .font(.caption2)
        .padding(.horizontal, 4)
    }
}

extension KLineModel: @retroactive Identifiable {
    public var id: UUID {
        UUID()
    }
}

#Preview {
    TransactionRecordList(kLineData: [])
}
