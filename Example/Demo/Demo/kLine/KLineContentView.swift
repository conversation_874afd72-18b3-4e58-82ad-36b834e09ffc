import Combine
import DependenciesAdditions
import KLineLib
import Sharing
import SwiftUI
import SwiftUI<PERSON>nt<PERSON>pect
import WebSocketClient



struct KLineContentView: View {
    @Shared(.appStorage("kLineViewType")) private var kLineViewType:
        KLineViewType = .timeLine
    @Shared(.appStorage("mainViewTypesExpanded")) private
        var mainViewTypesExpanded: Bool = true
    @Shared(.appStorage("subViewTypesExpanded")) private
        var subViewTypesExpanded: Bool = true
    @Shared(.codableArray(key: "mainViewTypes")) private var mainViewTypes:
        [KLineMainViewType]
    @Shared(.codableArray(key: "subViewTypes")) private var subViewTypes:
        [KLineSubViewType]
    @Shared(.appStorage("kLineTheme")) private var theme: Theme = KlineLibThemeManager
        .currentTheme
    @State.SharedReader(value: nil) private var kLineRealData:
        WebSocketMessage<KLineTime, KLineChartData>?
    @State private var kLineData: [KLineModel] = []
    @State private var updatedLineData: [KLineModel] = []
    @State private var showConfiguration = false
    @State private var headerTips: KLineDrawTips.TitleDetail?
    @State private var centerTips: KLineDrawTips.TitleDetail?
    @State private var kLineHeight: CGFloat = 180
    private var kLineRealDataPublisher: AnyPublisher<KLineChartData, Never> {
        $kLineRealData.publisher
            .compactMap { $0?.data.last }
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }

    private var currentPriceString: String {
        let currentLatest = kLineData.last
        let currentLatestString =
            currentLatest == nil ? "--" : "\(currentLatest!.close)"
        return currentLatestString
    }

    private var currentPriceForegroundColor: Color {
        if let currentLatest = kLineData.last {
            let uiColor =
                currentLatest.close > currentLatest.open
                ? KLineConfig.themeManager.mainRedColor()
                : KLineConfig.themeManager.mainGreenColor()
            return Color(uiColor: uiColor)
        }
        return Color(.systemGray4)
    }

    // MARK: - Time Range Picker
    @State private var timeRange: TimeRangeOption = .timePeriod(.intraday)
    @State private var showTimeRange = false
    @State private var webSocketSubscribeInterval: String?
    var body: some View {
        NavigationView {
            ScrollView(showsIndicators: false) {
                VStack {
                    VStack {
                        Text(currentPriceString)
                            .foregroundColor(currentPriceForegroundColor)
                            .font(.title)
                            .fontWeight(.semibold)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .padding(.leading)
                        kLineHeaderView()
                    }
                    .padding(.bottom, 20)
                    kLineContentView()
                }
                .sheet(isPresented: $showTimeRange) {
                    if #available(iOS 16.0, *) {
                        KLineTimeRangePicker(selectedOption: $timeRange)
                            .presentationDetents([.fraction(0.8)])
                            .presentationDragIndicator(.hidden)
                    } else {
                        KLineTimeRangePicker(selectedOption: $timeRange)
                            .introspect(.sheet, on: .iOS(.v15)) {
                                $0.detents = [.large()]
                                $0.prefersGrabberVisible = false
                                $0.preferredCornerRadius = 40
                            }
                    }
                }
                .onReceive(
                    $subViewTypes.publisher.receive(on: DispatchQueue.main)
                ) { subViewTypes in
                    kLineHeight =
                        180
                        + subViewTypes.map { _ -> CGFloat in 70 }.reduce(0, +)
                }
                .onReceive(kLineRealDataPublisher) { newItem in
                    let updatedModel = KLineModel()
                    updatedModel.open = newItem.openPrice
                    updatedModel.close = newItem.closePrice
                    updatedModel.low = newItem.lowPrice
                    updatedModel.high = newItem.highPrice
                    updatedModel.volume = newItem.volume
                    updatedModel.timestamp = TimeInterval(newItem.time) * 1000
                    withAnimation {
                        if kLineData.count > 0,
                            let last = kLineData.last
                        {
                            if last.timestamp == updatedModel.timestamp {
                                kLineData[kLineData.count - 1] = updatedModel
                            } else if updatedModel.timestamp > last.timestamp {
                                kLineData.append(updatedModel)
                            }
                        } else {
                            kLineData.append(updatedModel)
                        }
                    }
                }
                .sheet(isPresented: $showConfiguration) {
                    if #available(iOS 16.0, *) {
                        configurationsForm()
                            .presentationDetents([.medium, .large])
                            .presentationDragIndicator(.visible)
                    } else {
                        configurationsForm()
                            .introspect(.sheet, on: .iOS(.v15)) {
                                $0.detents = [.medium(), .large()]
                                $0.prefersGrabberVisible = true
                            }
                            .interactiveDismissDisabled()
                    }
                }
            }
            .refreshable {
                Task {
                    await loadData()
                }
            }
            .toolbar {
                ToolbarItem(placement: .primaryAction) {
                    Button("Configuration") {
                        showConfiguration.toggle()
                    }
                }
            }
            .task(id: timeRange) {
                await loadData()
                // TODO: need to request kline data and resubscirbe
                switch timeRange {
                case .timePeriod(let timePeriod):
                    $kLineViewType.withLock {
                        $0 = .timeLine
                    }
                case .kLineInterval(let kLineInterval):
                    $kLineViewType.withLock {
                        $0 = .kline
                    }
                }
            }
            .onDisappear {
                @Dependency(\.webSocket) var webSocket
                Task {
                    if let webSocketSubscribeInterval {
                        await webSocket.cancelSubscribe(
                            .kline(params: webSocketSubscribeInterval),
                            "BTCUSD",
                            "",
                            nil
                        )
                    }
                }
            }
            .kLineDrawHeaderTipsView(tips: $headerTips)
            .kLineDrawCenterTipsView(tips: $centerTips)
            .safeAreaInset(edge: .bottom) {
                // MARK: - draw settings. e.g. color, line style, etc...
                if showDrawSettingsView {
                    KLineDrawSettingsSwiftUIView(
                        drawShapeType: $drawShapeType,
                        showDrawSettingsView: $showDrawSettingsView
                    )
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                }
            }
            .navigationTitle("KLine")
        }
    }

    private func loadData() async {
        @Dependency(\.webSocket) var webSocket

        struct KLineHistory: Encodable {
            let limit: Int?
            let interval: String
            let symbols: String
        }
        let interval: String =
            switch timeRange {
            case .timePeriod(let timePeriod): timePeriod.interval
            case .kLineInterval(let kLineInterval): kLineInterval.interval
            }

        let kLineHistory = KLineHistory(
            limit: 100,
            interval: interval,
            symbols: "305.BTCUSD"
        )
        do {
            let kLineModels: [KLineData] = try await NetworkRequest.shared.post(
                url: ChiefRequest.klineHistory.url,
                params: kLineHistory
            )
            self.kLineData = kLineModels.map {
                let updatedModel = KLineModel()
                updatedModel.open = $0.open
                updatedModel.close = $0.close
                updatedModel.low = $0.low
                updatedModel.high = $0.high
                updatedModel.volume = $0.volume
                updatedModel.timestamp = TimeInterval($0.timestamp)
                return updatedModel
            }
            if let webSocketSubscribeInterval,
                webSocketSubscribeInterval != interval
            {
                await webSocket.subscribe(
                    .kline(params: interval),
                    "BTCUSD",
                    "",
                    nil
                )
                self.webSocketSubscribeInterval = interval
                await webSocket.subscribe(
                    .kline(params: interval),
                    "BTCUSD",
                    "",
                    nil
                )
                await withErrorReporting {
                    try await $kLineRealData.load(
                        .subscribeKey("kline_\(interval).BTCUSD")
                    )
                }
            }
        } catch {
            debugPrint(error)
        }
    }

    // MARK: - KLineGesturePop

    @State private var popKLineModel: KLineModel?
    @State private var popKLinePrice: String?
    @State private var popKLineIsShow = true
    @State private var popKLinePosition: CGPoint?
    @State private var recordListWidth: CGFloat = 0

    @State private var showDrawSettingsView = false

    @ViewBuilder
    private func kLineContentView() -> some View {
        HStack(alignment: .top, spacing: 0) {
            KLineSwiftUIView(
                kLineModels: kLineData,
                headerTips: $headerTips,
                centerTips: $centerTips,
                showDrawSettingsView: $showDrawSettingsView,
                drawShape: $drawShapeType,
                updatedModels: $updatedLineData,
                klineViewType: kLineViewType,
                mainViewTypes: mainViewTypes,
                subViewTypes: subViewTypes,
                timeRange: timeRange,
                theme: theme,
                updatePopData: { kLineModel, price in
                    self.popKLineModel = kLineModel
                    self.popKLinePrice = price
                },
                showPopIndicator: { show, position in
                    self.popKLineIsShow = show
                    self.popKLinePosition = position
                }
            )
            .overlay {
                KLineGesturePopSwiftUIView(
                    kLineModel: popKLineModel,
                    price: popKLinePrice,
                    isShowing: popKLineIsShow,
                    position: popKLinePosition
                )
                .allowsHitTesting(false)
            }
            Divider()
                .foregroundColor(.secondary)
                .frame(width: 1)
            TransactionRecordList(kLineData: kLineData)
                .frame(width: recordListWidth)
                .animation(.spring(), value: recordListWidth)
        }
        .frame(height: kLineHeight)
    }

    @ViewBuilder
    private func kLineHeaderView() -> some View {
        HStack(spacing: 20) {
            // MARK: - kline time range
            Button {
                showTimeRange = true
            } label: {
                Text(timeRange.displayText + "▾")
                    .foregroundColor(HexColor(0x1b61a9))
                    .font(.caption2)
                    .frame(width: 80, height: 20)
                    .background(HexColor(0xf3f8fb))
                    .overlay(
                        Capsule()
                            .stroke(
                                HexColor(0x1b61a9),
                                lineWidth: 1
                            )
                    )
                    .contentShape(.rect)
            }
            .clipShape(.capsule)
            .padding(.leading)
            Divider()
                .background(Color(.systemGray6))
                .padding(.vertical, 4)
                .frame(width: 1)

            // MARK: - ???
            Button {

            } label: {
                Image(systemName: "chart.line.uptrend.xyaxis")
                    .resizable()
                    .scaledToFit()
                    .frame(width: 16, height: 16)
                    .foregroundColor(Color(.systemGray3))
            }

            // MARK: - toggle record list shrink
            Button {
                withAnimation {
                    if recordListWidth == 0 {
                        recordListWidth = 160
                    } else {
                        recordListWidth = 0
                    }
                }
            } label: {
                Image(
                    systemName: recordListWidth == 160
                        ? "arrow.left.and.line.vertical.and.arrow.right"
                        : "arrow.left.and.right"
                )
                .resizable()
                .scaledToFit()
                .frame(width: 16, height: 16)
                .foregroundColor(Color(.systemGray3))
            }
            Spacer()
            drawLineItem()
        }
        .padding(.vertical, 8)
        .overlay(alignment: .top) {
            Divider()
                .background(Color(.systemGray6))
                .frame(maxWidth: .infinity)
                .frame(height: 1)
        }
        .overlay(alignment: .bottom) {
            Divider()
                .background(Color(.systemGray6))
                .frame(maxWidth: .infinity)
                .frame(height: 1)
        }
    }

    @ViewBuilder
    private func configurationsForm() -> some View {
        NavigationView {
            Form {
                kLineView()
                themeView()
                mainView()
                subView()
            }
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Dismiss") {
                        showConfiguration = false
                    }
                }
            }
        }
    }

    @ViewBuilder
    private func kLineView() -> some View {
        Section {
            Picker("KlineViewType", selection: Binding($kLineViewType)) {
                ForEach(KLineViewType.allCases, id: \.self) {
                    viewType in
                    Button(viewType.title) {
                        $kLineViewType.withLock {
                            $0 = viewType
                        }
                    }
                }
            }
            .pickerStyle(.segmented)
        } header: {
            Text("KLineViewType")
                .textCase(.none)
                .font(.title2)
        }
    }

    @ViewBuilder
    private func mainView() -> some View {
        Section {
            if mainViewTypesExpanded {
                ForEach(KLineMainViewType.allCases, id: \.self) {
                    mainViewType in
                    Button {
                        if mainViewTypes.contains(mainViewType) {
                            $mainViewTypes.withLock {
                                $0.removeAll(where: { $0 == mainViewType })
                            }

                        } else {
                            $mainViewTypes.withLock {
                                $0.append(mainViewType)
                            }
                        }
                    } label: {
                        HStack {
                            Text(mainViewType.title)
                            Spacer()
                            if mainViewTypes.contains(mainViewType) {
                                Image(systemName: "checkmark")
                            }
                        }
                        .contentShape(.rect)
                    }
                    .buttonStyle(.plain)
                }
            }
        } header: {
            VStack {
                Button {
                    withAnimation {
                        $mainViewTypesExpanded.withLock {
                            $0.toggle()
                        }
                    }
                } label: {
                    HStack {
                        Text("KLineMainViewType")
                            .textCase(.none)
                        Image(
                            systemName: mainViewTypesExpanded
                                ? "chevron.down" : "chevron.right"
                        )
                        Spacer()
                    }
                    .font(.title2)
                }
                .buttonStyle(.plain)
                ScrollView(.horizontal) {
                    HStack {
                        ForEach(mainViewTypes, id: \.self) {
                            selectedMainViewType in
                            Button {
                                withAnimation {
                                    $mainViewTypes.withLock {
                                        $0.removeAll(where: {
                                            $0 == selectedMainViewType
                                        })
                                    }

                                }
                            } label: {
                                Text(selectedMainViewType.title)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)
                                    .padding(6)
                                    .background(
                                        RoundedRectangle(cornerRadius: 4)
                                    )
                            }
                        }
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
            }
        }
    }

    @ViewBuilder
    private func subView() -> some View {
        Section {
            if subViewTypesExpanded {
                ForEach(KLineSubViewType.allCases, id: \.self) { subViewType in
                    Button {
                        if subViewTypes.contains(subViewType) {
                            $subViewTypes.withLock {
                                $0.removeAll(where: { $0 == subViewType })
                            }
                        } else {
                            $subViewTypes.withLock {
                                $0.append(subViewType)
                            }
                        }
                    } label: {
                        HStack {
                            Text(subViewType.title)
                            Spacer()
                            if subViewTypes.contains(subViewType) {
                                Image(systemName: "checkmark")
                            }
                        }
                        .contentShape(.rect)
                    }
                    .buttonStyle(.plain)
                }
            }
        } header: {
            VStack {
                Button {
                    withAnimation {
                        $subViewTypesExpanded.withLock {
                            $0.toggle()
                        }
                    }
                } label: {
                    HStack {
                        Text("KLineSubViewType")
                            .textCase(.none)
                        Image(
                            systemName: subViewTypesExpanded
                                ? "chevron.down" : "chevron.right"
                        )
                        Spacer()
                    }
                    .font(.title2)
                }
                .buttonStyle(.plain)
                ScrollView(.horizontal) {
                    HStack {
                        ForEach(subViewTypes, id: \.self) {
                            selectedSubViewType in
                            Button {
                                withAnimation {
                                    $subViewTypes.withLock {
                                        $0.removeAll(where: {
                                            $0 == selectedSubViewType
                                        })
                                    }
                                }
                            } label: {
                                Text(selectedSubViewType.title)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)
                                    .padding(6)
                                    .background(
                                        RoundedRectangle(cornerRadius: 4)
                                    )
                            }
                            .id(selectedSubViewType)
                        }
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
            }
        }
    }

    @ViewBuilder
    private func themeView() -> some View {
        Section {
            Picker("Theme", selection: Binding($theme)) {
                ForEach(Theme.allCases, id: \.self) { theme in
                    Button(theme.title) {
                        $theme.withLock {
                            $0.toggle()
                        }
                    }
                }
            }
            .pickerStyle(.segmented)

        } header: {
            Text("Theme")
                .textCase(.none)
                .font(.title2)
        }
    }
    
    // MARK: - Draw Line menus
    @State private var drawToolBoxType: KLineDrawToolBoxType?
    @State private var drawShapeType: KLineDrawShapeType?
    @State private var showDrawTool = false
    @ViewBuilder
    private func drawLineItem() -> some View {
        Menu {
            ForEach(KLineDrawToolBoxType.allCases) { toolBox in
                if toolBox.shapes.isEmpty {
                    Button {
                        if toolBox == .clean {
                            withAnimation {
                                drawShapeType = nil
                                showDrawTool = false
                            }
                        }
                    } label: {
                        Label(toolBox.title, systemImage: toolBox.sfSymbol)
                    }
                } else {
                    Menu {
                        Picker(selection: $drawShapeType, label: Text("")) {
                            ForEach(toolBox.shapes, id: \.rawValue) { shape in
                                Label(
                                    shape.titleAndIcon.0,
                                    systemImage: shape.titleAndIcon.1
                                )
                                .tag(shape as KLineDrawShapeType?)
                            }
                        }
                    } label: {
                        Label(toolBox.title, systemImage: toolBox.sfSymbol)
                    }
                }
            }
        } label: {
            Image(systemName: "pencil.line")
                .resizable()
                .scaledToFit()
                .frame(width: 16, height: 16)
                .tint(.primary)

        }
        .padding(.trailing)
        .onChange(of: showDrawSettingsView) {
            if !$0 {
                headerTips = nil
                centerTips = nil
            }
        }
        .onChange(of: drawShapeType) { value in
            // MARK: - update when draw shape type did change
            withAnimation {
                showDrawSettingsView = value != nil
            }
            if let value {
                let drawViewModel = KLineDrawViewModel.shared
                drawViewModel.hiddenDraw = false
                drawViewModel.createNewDrawModel(value)
            }
        }
    }

}

struct ColorPickerView: View {
    let colors: [Color]
    var body: some View {
        VStack {
            LazyVGrid(columns: [GridItem(.adaptive(minimum: 25))], spacing: 5) {
                ForEach(colors, id: \.self) { color in
                    Button {

                    } label: {
                        Rectangle()
                            .fill(color)
                            .frame(width: 20, height: 20)
                            .cornerRadius(4)
                    }
                }
            }
        }
    }
}

#Preview {
    let _ = prepareDependencies {
        $0.webSocket = WebSocketKey.liveValue
        $0.webSocketCache = DefaultWebSocketCacheKey.liveValue
    }
    KLineContentView()
}
