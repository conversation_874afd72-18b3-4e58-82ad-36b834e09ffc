//
//  KLineTimeRangePicker.swift
//  Demo
//
//  Created by stx80501 on 2025/4/17.
//

import SwiftUI

public struct ChartDateRange: Equatable {
    let from: Date
    let to: Date
}

public struct ChartIntraDayMode: Equatable {
    let timeFormat: String
    let fromDate: Date
    let toDate: Date
    let xAxisDates: [Date]
    var businessTimeRagne: [ChartDateRange] = []
    var closeTimePoints: [ChartDateRange] = []
}

// MARK: - 核心数据结构
public enum TimeRangeOption: Hashable {
    // 时段选项
    case timePeriod(TimePeriod)
    // K线区间选项
    case kLineInterval(KLineInterval)

    // 时段类型（分时）
    public enum TimePeriod: String, CaseIterable, Identifiable {
        case intraday = "當天分時"
        case fiveDays = "5日分時"
        
        public var id: Self { self }
        public var interval: String {
            switch self {
            case .intraday: "1m"
            case .fiveDays: "5m"
            }
        }
    }

    // K线类型（带关联值）
    public enum KLineInterval: Hashable {
        case minute(Int)  // 分钟级（1,5,15...）
        case hour(Int)  // 小时级（1,4...）
        case range(KLineRange)  // 范围级

        // 范围类型
        public enum KLineRange: String, CaseIterable {
            case day = "天"
            case week = "週"
            case month = "月"
            case year = "1年"
        }
        public var interval: String {
            switch self {
            case .minute(let int): "\(int)m"
            case .hour(let int): "\(int)h"
            case .range(let kLineRange):
                switch kLineRange {
                case .day: "1d"
                case .week: "1w"
                case .month: "1M"
                case .year: "1y"
                }
            }
        }
    }
}

// MARK: - 数据扩展（方便UI使用）
extension TimeRangeOption {
    // 选项显示文本
    public var displayText: String {
        switch self {
        case .timePeriod(let period):
            return period.rawValue
        case .kLineInterval(let interval):
            switch interval {
            case .minute(let value):
                return "\(value)分鐘"
            case .hour(let value):
                return "\(value)小時"
            case .range(let range):
                return range.rawValue
            }
        }
    }

    // 分组信息（用于UI分段显示）
    public var group: GroupType {
        switch self {
        case .timePeriod: return .timePeriod
        case .kLineInterval(let interval):
            switch interval {
            case .minute: return .minute
            case .hour: return .hour
            case .range: return .range
            }
        }
    }

    // 分组类型（与UI结构对应）
    public enum GroupType: String, CaseIterable, Identifiable {
        case timePeriod = "時段"
        case minute = "分鐘"
        case hour = "小時"
        case range = "範圍"
        
        public var id: Self { self }
    }
}

// MARK: - 数据源（示例数据）
extension TimeRangeOption {
    // 完整的选项列表（按实际需求调整）
    public static var allOptions: [Self] {
        [
            // 时段组
            .timePeriod(.intraday),
            .timePeriod(.fiveDays),

            // 分钟组
            .kLineInterval(.minute(1)),
            .kLineInterval(.minute(2)),
            .kLineInterval(.minute(3)),
            .kLineInterval(.minute(5)),
            .kLineInterval(.minute(10)),
            .kLineInterval(.minute(15)),
            .kLineInterval(.minute(30)),

            // 小时组
            .kLineInterval(.hour(1)),
            .kLineInterval(.hour(2)),
            .kLineInterval(.hour(4)),

            // 范围组
            .kLineInterval(.range(.day)),
            .kLineInterval(.range(.week)),
            .kLineInterval(.range(.month)),
            .kLineInterval(.range(.year)),
        ]
    }

    // 分组后的数据（用于ForEach显示）
    public static var groupedOptions: [GroupType: [Self]] {
        Dictionary(grouping: allOptions, by: { $0.group })
    }
}

public struct KLineTimeRangePicker: View {
    @Binding var selectedOption: TimeRangeOption
    @Environment(\.dismiss) var dismiss
    public init(selectedOption: Binding<TimeRangeOption>) {
        self._selectedOption = selectedOption
    }
    public var body: some View {
        VStack(spacing: 30) {
            Text("时间范围")
                .font(.body)
                .frame(maxWidth: .infinity, alignment: .center)
                .padding(.vertical, 20)
            VStack(alignment: .leading) {
                Text("时段")
                    .foregroundColor(HexColor(0x3287c6))
                    .font(.callout)
                    .padding(.bottom, 20)
                Text("分时")
                    .font(.caption)
                    .foregroundColor(.secondary)
                optionGrid(for: [TimeRangeOption.timePeriod(.intraday), TimeRangeOption.timePeriod(.fiveDays)])
            }
            VStack(alignment: .leading) {
                Text("K线区间")
                    .foregroundColor(HexColor(0x3287c6))
                    .font(.callout)
                    .padding(.bottom, 20)
                Text("分钟")
                    .font(.caption)
                    .foregroundColor(.secondary)
                optionGrid(for: [
                    .kLineInterval(.minute(1)),
                    .kLineInterval(.minute(2)),
                    .kLineInterval(.minute(3)),
                    .kLineInterval(.minute(5)),
                    .kLineInterval(.minute(10)),
                    .kLineInterval(.minute(15)),
                    .kLineInterval(.minute(30))
                ])
                Text("小时")
                    .font(.caption)
                    .foregroundColor(.secondary)
                optionGrid(for: [
                    .kLineInterval(.hour(1)),
                    .kLineInterval(.hour(2)),
                    .kLineInterval(.hour(4))
                ])
                Text("范围")
                    .font(.caption)
                    .foregroundColor(.secondary)
                optionGrid(for: [
                    .kLineInterval(.range(.day)),
                    .kLineInterval(.range(.week)),
                    .kLineInterval(.range(.month)),
                    .kLineInterval(.range(.year))
                ])
            }
            
            Spacer()
        }
        .padding()
    }

    private func optionGrid(for options: [TimeRangeOption]) -> some View {
        LazyVGrid(
            columns: Array(repeating: GridItem(.flexible(), spacing: 10), count: 4),
            spacing: 10
        ) {
            ForEach(options, id: \.self) { option in
                Button {
                    selectedOption = option
                    dismiss()
                } label: {
                    Text(option.displayText)
                        .font(.caption)
                        .foregroundColor(selctionForegroundColor(for: option))
                        .padding(8)
                        .frame(maxWidth: .infinity)
                        .frame(height: 48)
                        .background(selectionColor(for: option))
                        .overlay {
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(
                                    selectedOption == option ? HexColor(0x3287c6) : Color(.systemGray6),
                                    lineWidth: 1.5
                                )
                        }
                        .clipShape(.rect(cornerRadius: 8))
                }
            }
        }
    }
    // 选中状态样式
    private func selectionColor(for option: TimeRangeOption) -> Color {
        selectedOption == option ? HexColor(0xf3f8fb) : .clear
    }
    
    private func selctionForegroundColor(for option: TimeRangeOption) -> Color {
        selectedOption == option ? HexColor(0x3287c6) : .secondary
    }
}

#Preview {
    KLineTimeRangePicker(selectedOption: .constant(.timePeriod(.intraday)))
}
