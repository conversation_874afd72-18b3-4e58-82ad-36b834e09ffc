import SwiftUI
import KLineLib

public struct KLineSwiftUIView: UIViewRepresentable {
    public typealias UIViewType = KLineView

    // --- Input Data and Configuration ---
    // Use @Binding if the KLineView needs to modify these values and
    // communicate the change back to SwiftUI via the Coordinator.
    // Use simple `let` or `var` if data flows only one way (SwiftUI -> KLineView).

    var kLineModels: [KLineModel] // Data source
    var liteKLineStyle: Bool                 // Configuration
    var enableGestureEvent: Bool             // Configuration
    var klineViewType: KLineViewType
    var mainViewTypes: [KLineMainViewType]
    var subViewTypes: [KLineSubViewType]
    var theme: Theme
    var timeRange: TimeRangeOption
    
    // MARK: - draw view
    @Binding var headerTips: KLineDrawTips.TitleDetail?
    @Binding var centerTips: KLineDrawTips.TitleDetail?
    @Binding var showDrawSettingsView: Bool
    @Binding var drawShape: KLineDrawShapeType?
    
    @Binding var updatedModels: [KLineModel]

    // --- Callbacks ---
    // Closures to handle events from KLineView, triggered by the Coordinator
    var onModelTapped: ((KLineModel) -> Void)?
    var onHideInfoView: ((Bool) -> Void)?
    var onScrollToStart: (() -> Void)?
    
    // MARK: - KLineViewDelegate Callbacks
    var updatePopData: ((_ kLineModel: KLineModel, _ price: String) -> Void)?
    var showPopIndicator: ((_ show: Bool, _ position: CGPoint?) -> Void)?
    
    public init(
        kLineModels: [KLineModel],
        headerTips: Binding<KLineDrawTips.TitleDetail?>,
        centerTips: Binding<KLineDrawTips.TitleDetail?>,
        showDrawSettingsView: Binding<Bool>,
        drawShape: Binding<KLineDrawShapeType?>,
        updatedModels: Binding<[KLineModel]>,
        liteKLineStyle: Bool = false,
        enableGestureEvent: Bool = true,
        klineViewType: KLineViewType,
        mainViewTypes: [KLineMainViewType] = [],
        subViewTypes: [KLineSubViewType] = [],
        timeRange: TimeRangeOption,
        theme: Theme,
        onModelTapped: ((KLineModel) -> Void)? = nil,
        onHideInfoView: ((Bool) -> Void)? = nil,
        onScrollToStart: (() -> Void)? = nil,
        updatePopData: ((_ kLineModel: KLineModel, _ price: String) -> Void)? = nil,
        showPopIndicator: ((_ show: Bool, _ position: CGPoint?) -> Void)? = nil
    ) {
        self.kLineModels = kLineModels
        self._headerTips = headerTips
        self._centerTips = centerTips
        self._showDrawSettingsView = showDrawSettingsView
        self._drawShape = drawShape
        self._updatedModels = updatedModels
        self.liteKLineStyle = liteKLineStyle
        self.enableGestureEvent = enableGestureEvent
        self.klineViewType = klineViewType
        self.mainViewTypes = mainViewTypes
        self.subViewTypes = subViewTypes
        self.timeRange = timeRange
        self.theme = theme
        self.onModelTapped = onModelTapped
        self.onHideInfoView = onHideInfoView
        self.onScrollToStart = onScrollToStart
        self.updatePopData = updatePopData
        self.showPopIndicator = showPopIndicator
    }

    // --- Coordinator ---
    // Creates the Coordinator instance.
    public func makeCoordinator() -> Coordinator {
        Coordinator(
            self,
            klineViewType: klineViewType,
            mainViewTypes: mainViewTypes,
            subViewTypes: subViewTypes,
            timeRange: timeRange
        )
    }

    // --- makeUIView ---
    // Creates the initial KLineView instance.
    public func makeUIView(context: Context) -> KLineView {
        // Use the delegate initializer
        let kLineView = KLineView(delegate: context.coordinator)
        KLineConfig.type = klineViewType
        KLineConfig.mainViewType = mainViewTypes
        KLineConfig.subViewType = subViewTypes
        kLineView.enableGestureEvent = enableGestureEvent

        // Assign Coordinator to handle callbacks
        kLineView.delegate = context.coordinator // Assign KlineViewDelegate
        kLineView.tapedBlock = context.coordinator.handleModelTapped
        kLineView.hiddenInfoViewBlock = context.coordinator.handleHideInfoView

        // Set initial data (will also be updated in updateUIView)
        // Pass true for needReDraw on initial creation if desired
        kLineView.setKLineModels(kLineModels, true)
//        KLineConfig.themeManager = KLineThemeConfig()
        KLineConfig.tapType = .pop
        KLineConfig.kLineWidth = 4                                  // 柱状图间隙
        KLineConfig.kLineGap = 1.2                                  // 柱状图间隙
        KLineConfig.subVeiwHeaderHeight = 14.0                      // 副图header的高度
        KLineConfig.kLineSubViewHeight = 50.0                       // 副图的高度
        KLineConfig.kLineTimeViewWithBottomView = .mainViewBottom   // 时间轴位置：主图下
        KLineConfig.pricePosition = .left(offset: 0)                // Y轴位置：左对齐
        KLineConfig.displayindexValueOnView = false                 // 指标值是否显示在header上
        KLineDrawViewModel.shared.updateDrawSymbol("hha")
        return kLineView
    }

    // --- updateUIView ---
    // Updates the existing KLineView when SwiftUI state changes.
    public func updateUIView(_ uiView: KLineView, context: Context) {
        // Check if specific properties changed to avoid unnecessary updates if performance is critical.
        // For simplicity here, we update most things.

        // Update configuration if changed
        var redraw = false
        var changeTheme = false
        if uiView.enableGestureEvent != enableGestureEvent {
             uiView.enableGestureEvent = enableGestureEvent
        }
        
        redraw = redraw || context.coordinator.updateKLineViewType(to: klineViewType)
        redraw = redraw || context.coordinator.updateMainViewTypes(to: mainViewTypes)
        redraw = redraw || context.coordinator.updateSubViewTypes(to: subViewTypes)
        
        if context.coordinator.timeRange != timeRange {
            context.coordinator.updateTimeRange(timeRange)
            redraw = true
        }
        
        if KlineLibThemeManager.currentTheme != theme {
            changeTheme = true
        }
        
        context.coordinator.shouldRedraw = redraw
        if uiView.kLineModels != kLineModels {
            uiView.setKLineModels(kLineModels, context.coordinator.shouldRedraw)
            context.coordinator.shouldRedraw = false
        }
        if context.coordinator.shouldRedraw {
            uiView.updateUI()
        }
        if changeTheme {
            KlineLibThemeManager.currentTheme = theme
            uiView.changeTheme()
        }
        
        // Update data source
        // A more sophisticated check might compare model arrays for actual changes
        // Using equality check assumes KLineModel is Equatable, otherwise compare counts or IDs
        // For simplicity, let's just pass the current models. Decide if redraw is needed.
        // Often, you only need a full redraw if the identity or count changes drastically.
        // Let KLineView's internal logic decide if redraw is needed if possible.
        // Pass `context.coordinator.shouldRedraw` if you add logic there, or just `false`
        // if `setKLineModels` is smart enough. Let's assume false is usually ok here
        // unless the array reference itself changed.

        // Basic check: If the array reference is different, pass it down.
        // KLineView's `setKLineModels` already seems to handle some logic around redraw.
        // We might need a more robust way to trigger redraw if the array content changes
        // without the reference changing (e.g., modifying a model within the array).
        // A common pattern is to add a separate `redrawID: UUID` binding.
        
        context.coordinator.shouldRedraw = false // Reset redraw flag


        // Ensure the coordinator's parent reference is current
        context.coordinator.parent = self
        
    }

    // --- Coordinator Class ---
    // Acts as the delegate and handles callbacks.
  public class Coordinator: NSObject, KDCDelegate, KlineViewDelegate, KLineDrawManagerDelegate {
    public func leftmostModelWith(klineView: KLineLib.KLineView, klineModel: KLineLib.KLineModel) {
      
    }
    
    public func closingPriceOfThePreviousDay() -> Double? {
      return nil
    }
    
        
        var parent: KLineSwiftUIView
        var shouldRedraw: Bool = false // Flag to trigger redraw in updateUIView
        var klineViewType: KLineViewType?
        var mainViewTypes: [KLineMainViewType]
        var subViewTypes: [KLineSubViewType]
        var timeRange: TimeRangeOption
        init(
            _ parent: KLineSwiftUIView,
            klineViewType: KLineViewType? = nil,
            mainViewTypes: [KLineMainViewType] = [],
            subViewTypes: [KLineSubViewType] = [],
            timeRange: TimeRangeOption
        ) {
            self.parent = parent
            self.klineViewType = klineViewType
            self.mainViewTypes = mainViewTypes
            self.subViewTypes = subViewTypes
            self.timeRange = timeRange
            super.init() // Call super.init() for NSObject subclasses
            KLineDrawViewModel.shared.notificationDelegate = self
        }
        
        // MARK: - Methods
        func updateKLineViewType(to newType: KLineViewType) -> Bool {
            guard klineViewType != newType else {
                return false
            }
            KLineConfig.type = newType
            KLineDrawViewModel.shared.reloadKlineTimeData()
            klineViewType = newType
            return true
        }
        
        func updateMainViewTypes(to newTypes: [KLineMainViewType]) -> Bool {
            guard mainViewTypes != newTypes else {
                return false
            }
            KLineConfig.mainViewType = newTypes
            mainViewTypes = newTypes
            return true
        }
        
        func updateSubViewTypes(to newTypes: [KLineSubViewType]) -> Bool {
            guard subViewTypes != newTypes else {
                return false
            }
            KLineConfig.subViewType = newTypes
            subViewTypes = newTypes
            return true
        }
        
        func updateTimeRange(_ timeRange: TimeRangeOption) {
            self.timeRange = timeRange
            switch timeRange {
            case .timePeriod(let timePeriod):
                KLineConfig.customGridModel = nil
                KLineConfig.displayAllData = false
            case .kLineInterval(let kLineInterval):
                break
            }
        }

        // --- Callback Handlers ---
        func handleModelTapped(model: KLineModel) {
            parent.onModelTapped?(model)
        }

        func handleHideInfoView(isHidden: Bool) {
            parent.onHideInfoView?(isHidden)
        }
        
        // MARK: - KDCDelegate
        public func notifySymbolSerialData() {}
        public func notifySpotInfo() {}
        public func notifyOpenTimeUpdate() {}
        public func kLineListDidUpdate(needRewDraw:Bool) {}

        // MARK: - KlineViewDelegate
        /// 用于通知外部当前K线滚动到起点了 用于需要加载之前的数据的通知
        public func scrollToStartPosition() {}
        
        /// 当前选中的KlineModel 里面包含所以的指标值
        /// - Parameters:
        ///   - klineView: -
        ///   - klineModel: 当前选中的KlineModel
        public func currentSelectModelWith(klineView:KLineView,klineModel:KLineModel) {}
        
        
        /// 更新info View 中的信息
        /// - Parameters:
        ///   - klineView: -
        ///   - kLineModel: 当前选中的klineModel
        ///   - price: 当前十字线对应的y轴值
        public func updatePopDataWith(klineView:KLineView,kLineModel: KLineModel, price: String) {
            parent.updatePopData?(kLineModel, price)
        }
        
        
        /// 更新当前infoView是否显示 及 当前点击的point
        /// - Parameters:
        ///   - klineView: -
        ///   - isShow: 是否应该显示
        ///   - point: 当前点击的point
//        public func showIndicatorViewsWith(klineView:KLineView, isShow: Bool, point: CGPoint?) {
//            debugPrint(#function, isShow, point)
//            parent.showPopIndicator?(isShow, point)
//        }
        
        public func showIndicatorViewsWith(klineView: KLineLib.KLineView, isShow: Bool, horizontalStatus: Bool, point: CGPoint?) {
            debugPrint(#function, isShow, point)
            parent.showPopIndicator?(isShow, point)
        }

        // MARK: - KLineDrawManagerDelegate
        
        ///通知drawSettingView ReloadDrawModel
        public func drawSettingViewReloadDrawModel() {
            debugPrint(#function, KLineDrawViewModel.shared.drawStatus)
        }
        
        /// 用于通知关闭提示popup
        public func closeHeaderTipsView() {
            parent.headerTips = nil
        }
        
        /// 用于通知显示提示popup
        public func showHeaderTipsView(title:String,detail:String) {
            parent.headerTips = .init(title: title, detail: detail)
        }
        
        /// 用于显示居中的提示popup
        public func showCenterTipsView(title:String) {
            parent.centerTips = .init(title: title, detail: "")
        }
        
        //关闭
        public func closeCenterTipsView() {
            parent.centerTips = nil
        }
        
        public func didClearCurrentDrawModel() {
            parent.showDrawSettingsView = false
            parent.drawShape = nil
        }
    }
}
