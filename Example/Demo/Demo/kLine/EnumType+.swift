//
//  EnumType+.swift
//  Demo
//
//  Created by stx80501 on 2025/4/24.
//

import Foundation
import KLineLib

extension KLineDrawShapeType {
    public var titleAndIcon: (String, String) {
        switch self {
        case .segmentLine:
            return ("Segment Line", "line.diagonal")
        case .radialLine:
            return ("Radial Line", "arrow.up.right")
        case .verticalLine:
            return ("Vertical Line", "line.vertical")
        case .priceLine:
            return ("Price Line", "chart.line.uptrend.xyaxis")
        case .horizontalLine:
            return ("Horizontal Line", "line.horizontal")
        case .parallelChannel:
            return ("Parallel Channel", "rectangle.3.group")
        case .rectangle:
            return ("Rectangle", "rectangle")
        case .parallelogram:
            return ("Parallelogram", "diamond.fill")
        case .fibonacci:
            return ("Fibonacci", "chart.pie")
        case .threeWaves:
            return ("Three Waves", "waveform.path")
        case .fiveWaves:
            return ("Five Waves", "waveform.path")
        }
    }
}

extension KLineDrawToolBoxType: @retroactive CaseIterable, Codable, @retroactive
    Identifiable
{
    public static var allCases: [KLineDrawToolBoxType] = [
        .drawLine, .drawShape, .repeatDraw, .divider, .showDraw, .hideDraw,
        .clean, .share, .exit, .fibonacci, .drawWave,
    ]
    public var id: Int { rawValue }
    public var title: String {
        switch self {
        case .drawLine: "Line"
        case .drawShape: "Shape"
        case .repeatDraw: "Repeat Draw"
        case .divider: "Divider"
        case .showDraw: "Show Draw"
        case .hideDraw: "Hide Draw"
        case .clean: "Clean"
        case .share: "Share"
        case .exit: "Exit"
        case .fibonacci: "Fibonacci"
        case .drawWave: "Draw Wave"
        }
    }
    public var sfSymbol: String {
        switch self {
        case .drawLine: "line.diagonal"
        case .drawShape: "square.and.pencil"
        case .repeatDraw: "arrow.clockwise"
        case .divider: "line.horizontal.3"
        case .showDraw: "eye"
        case .hideDraw: "eye.slash"
        case .clean: "trash"
        case .share: "square.and.arrow.up"
        case .exit: "xmark.circle"
        case .fibonacci: "chart.line.uptrend.xyaxis"
        case .drawWave: "waveform.path"
        }
    }
    public var shapes: [KLineDrawShapeType] {
        switch self {
        case .drawLine:
            [
                .segmentLine,
                .radialLine,
                .verticalLine,
                .priceLine,
                .horizontalLine,
                .parallelChannel,
            ]
        case .drawShape:
            [
                .rectangle,
                .parallelogram,
            ]
        case .repeatDraw: []
        case .divider: []
        case .showDraw: []
        case .hideDraw: []
        case .clean: []
        case .share: []
        case .exit: []
        case .fibonacci: []
        case .drawWave:
            [
                .threeWaves,
                .fiveWaves,
            ]
        }
    }
}

extension KLineViewType: @retroactive CaseIterable, Codable {
    public static var allCases: [KLineViewType] = [
         .kline,.hollowCandle, .brokenLine,.americanLine,.timeLine,
    ]
    public var title: String {
        switch self {
        case .timeLine: "山形图"
        case .kline: "实心蜡烛"
        case .americanLine: "美国线"
        case .hollowCandle: "空心蜡烛"
        case .brokenLine: "线型图"
        case .fiveDayLine: "五日"
        }
    }
  public var iconName:String{
    switch self {
    case .timeLine:
      return "chart_type_area"
    case .kline:
      return "chart_type_candle"
    case .americanLine:
      return "chart_type_bars"
    case .hollowCandle:
      return "chart_type_hollow"
    case .brokenLine:
      return "chart_type_line"
    case .fiveDayLine:
      return ""
    }
  }
}

extension KLineMainViewType: @retroactive CaseIterable, Codable {
    public static var allCases: [KLineMainViewType] = [
        .ma,
        .ema,
        .wma,
        .boll,
        .sar,
    ]
}

extension KLineSubViewType: @retroactive CaseIterable, Codable {
    public static var allCases: [KLineSubViewType] = [
        .vol,
        .macd,
//        .kdj,
        .rsi,
//        .wr,
//        .obv,
//        .roc,
//        .cci,
//        .stochRSI,
//        .trix,
//        .dmi,
    ]
}
extension Theme: @retroactive CaseIterable {
    public static var allCases: [Theme] = [.dark, .light]
    mutating func toggle() {
        switch self {
        case .dark: self = .light
        case .light: self = .dark
        }
    }
    public var title: String {
        switch self {
        case .dark: "深色"
        case .light: "浅色"
        }
    }
}
