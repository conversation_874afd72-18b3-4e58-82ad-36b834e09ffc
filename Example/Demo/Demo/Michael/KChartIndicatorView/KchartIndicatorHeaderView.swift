//
//  KchartIndicatorHeaderView.swift
//  Demo
//
//  Created by <PERSON> on 2025/7/1.
//


import SwiftUI
import KLineLib
import Combine
import Sharing

struct KchartIndicatorHeaderView:View {
  var mainIndicatorType:KLineMainViewType?
  var subIndicatorType:KLineSubViewType?
  var klineModel:KLineModel
  var value:AttributedString{
    let font:Font = .system(size: 10.0)
    var attributedString = AttributedString()
    if let mainIndicatorType = mainIndicatorType{
      attributedString.createAttributedStringWith(text: mainIndicatorType.title, font: font, color: .black)
      var colors:[Color] = [.black]
      switch mainIndicatorType {
      case .ma:
        colors = KLineConfig.themeManager.maIndicatorColors().map({Color(uiColor: $0)})
        var index = 0
        KLineConfig.MAArray.forEach { indicator in
          let str = String(indicator)
          let value = String(" \(str):\(keepDecimalsWith(value: klineModel.maDictionary[str]?.asDouble()))")
          attributedString.createAttributedStringWith(text: value, font: font, color: colors[index])
          index += 1
          
        }
      case .ema:
        colors = KLineConfig.themeManager.emaIndicatorColors().map({Color(uiColor: $0)})
        var index = 0
        KLineConfig.EMAArray.forEach { indicator in
          let str = String(indicator)
          let value = String(" \(str):\(keepDecimalsWith(value: klineModel.emaDictionary[str]?.asDouble()))")
          attributedString.createAttributedStringWith(text: value, font: font, color: colors[index])
          index += 1
        }
      case .wma:
        colors = KLineConfig.themeManager.wmaIndicatorColors().map({Color(uiColor: $0)})
        var index = 0
        KLineConfig.WMAArray.forEach { indicator in
          let str = String(indicator)
          let value = String(" \(str):\(keepDecimalsWith(value: klineModel.wmaDictionary[str]?.asDouble()))")
          attributedString.createAttributedStringWith(text: value, font: font, color: colors[index])
          index += 1
        }
      case .boll:
        colors = KLineConfig.themeManager.bollIndicatorColors().map({Color(uiColor: $0)})
        attributedString.createAttributedStringWith(text: " MID:\(keepDecimalsWith(value: klineModel.mb?.asDouble()))", font: font, color: colors[0])
        attributedString.createAttributedStringWith(text: " UPPER:\(keepDecimalsWith(value: klineModel.up?.asDouble()))", font: font, color: colors[1])
        attributedString.createAttributedStringWith(text: " LOWER:\(keepDecimalsWith(value: klineModel.dn?.asDouble()))", font: font, color: colors[2])
      case .close:
        colors = KLineConfig.themeManager.closeIndicatorColors().map({Color(uiColor: $0)})
      case .sar:
        colors = KLineConfig.themeManager.sarIndicatorColors().map({Color(uiColor: $0)})
        attributedString.createAttributedStringWith(text: " \(keepDecimalsWith(value: klineModel.sar?.asDouble()))", font: font, color: colors[0])
      case .vwap:
        attributedString.createAttributedStringWith(text: " \(keepDecimalsWith(value: klineModel.averagePrice))", font: font, color: colors.first!)
      }
      
      return attributedString
      
    }else if let subIndicatorType = subIndicatorType{
      attributedString.createAttributedStringWith(text: subIndicatorType.title, font: font, color: .black)
      
      var colors:[Color] = [.black]
      
      switch subIndicatorType {
      case .vol:
        colors = KLineConfig.themeManager.volIndicatorColors().map({Color(uiColor: $0)})
        attributedString.createAttributedStringWith(text: " \(keepDecimalsWith(value: klineModel.volume))", font: font, color: colors.first!)
      case .macd:
        colors = KLineConfig.themeManager.macdIndicatorColors().map({Color(uiColor: $0)})
        attributedString.createAttributedStringWith(text: " MACD:\(keepDecimalsWith(value: klineModel.macd))", font: font, color: colors[0])
        attributedString.createAttributedStringWith(text: " DIF:\(keepDecimalsWith(value: klineModel.dif))", font: font, color: colors[1])
        attributedString.createAttributedStringWith(text: " DEA:\(keepDecimalsWith(value: klineModel.dea))", font: font, color: colors[2])
      case .kdj:
        colors = KLineConfig.themeManager.kdjIndicatorColors().map({Color(uiColor: $0)})
      case .rsi:
        colors = KLineConfig.themeManager.rsiIndicatorColors().map({Color(uiColor: $0)})
        var index = 0
        KLineConfig.rsiArray.forEach { indicator in
          let str = String(indicator)
          let value = String(" \(str):\(keepDecimalsWith(value: klineModel.rsiDictionary[str]?.asDouble()))")
          attributedString.createAttributedStringWith(text: value, font: font, color: colors[index])
          index += 1
        }
      case .wr:
        colors = KLineConfig.themeManager.wrIndicatorColors().map({Color(uiColor: $0)})
      case .obv:
        colors = KLineConfig.themeManager.obvIndicatorColors().map({Color(uiColor: $0)})
      case .roc:
        colors = KLineConfig.themeManager.rocIndicatorColors().map({Color(uiColor: $0)})
      case .cci:
        colors = KLineConfig.themeManager.cciIndicatorColors().map({Color(uiColor: $0)})
      case .stochRSI:
        colors = KLineConfig.themeManager.stochRSIIndicatorColors().map({Color(uiColor: $0)})
      case .trix:
        colors = KLineConfig.themeManager.trixIndicatorColors().map({Color(uiColor: $0)})
      case .dmi:
        colors = KLineConfig.themeManager.dmiIndicatorColors().map({Color(uiColor: $0)})
      case .close:
        colors = KLineConfig.themeManager.closeIndicatorColors().map({Color(uiColor: $0)})
      }
      return attributedString
    }
    return attributedString
  }
  
  var body: some View {
    HStack{
      Text(value)
      Spacer()
      
    }
    .padding(EdgeInsets(top: 0, leading: 5, bottom: 0, trailing: 5))
    .frame(height: KLineConfig.subVeiwHeaderHeight)
  }
  
  /// 保留3位小数
  /// - Parameters:
  ///   - value: valu
  ///   - completion: 是否以0 补齐
  /// - Returns: -
  func keepDecimalsWith(value:Double?,completion:Bool = true)->String{
    let formatter = NumberFormatter()
    formatter.numberStyle = .decimal
    formatter.minimumFractionDigits = completion ? 3 : 0
    formatter.maximumFractionDigits = 3
    return formatter.string(from: NSNumber(value: value ?? 0.0)) ?? "\(value ?? 0.0)"
  }
}
