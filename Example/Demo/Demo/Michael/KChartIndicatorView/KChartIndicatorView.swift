//
//  KChartIndicatorView.swift
//  Demo
//
//  Created by <PERSON> on 2025/6/30.
//

import SwiftUI
import KLineLib
struct KChartIndicatorView: View {
  @Binding var timeRangType:KChartTimeRangeType
  @Binding var mainSelectIndicators:[KLineMainViewType]
  @Binding var subSelectIndicators:[KLineSubViewType]
  var mainIndicatorsISSingle:Bool = true
    var body: some View {
      GeometryReader { geometry in
        HStack(alignment: .center){
          ScrollView(.horizontal,showsIndicators: false){
            HStack(alignment: .center){
              ForEach(timeRangType.mainIndicators,id:\.self) { mainType in
                Button {
                  //MARK: Michael: 主视图指标仅单选
                  if mainIndicatorsISSingle{
                    if mainType == .vwap{
                      if mainSelectIndicators.contains(mainType){
                        mainSelectIndicators.removeAll(where: {$0 == mainType})
                      }else{
                        mainSelectIndicators.append(mainType)
                      }
                    }else{
                      if mainSelectIndicators.contains(mainType){
                        mainSelectIndicators.removeAll(where: {$0 == mainType})
                      }else{
                        mainSelectIndicators.removeAll(where: {$0 != .vwap})
                        mainSelectIndicators.append(mainType)
                      }
                        
                    }
                    //MARK: Michael: 主视图指标可多选
                  }else{
                    if mainSelectIndicators.contains(mainType){
                      mainSelectIndicators.removeAll(where: {$0 == mainType})
                    }else{
                      mainSelectIndicators.append(mainType)
                    }
                  }
                } label: {
                  Text(mainType.title)
                    .foregroundColor(mainSelectIndicators.contains(mainType) ? Color.black : Color.gray)
                    .font(.system(size: 14.0,weight: mainSelectIndicators.contains(mainType) ? .semibold : .regular))
                }
                .frame(minWidth:mainType.title.textWidth(font: .systemFont(ofSize: 14.0), maxHeight: 40.0),idealHeight: geometry.size.height)
                
              }
            }
            .frame(maxHeight: geometry.size.height)
          }
          .frame(maxWidth: geometry.size.width * 0.6)
          .fixedSize()
          .layoutPriority(1)
          //
          Divider()
            //.padding(EdgeInsets(top: 5, leading: 0, bottom: 5, trailing: 0))
          //
          ScrollView(.horizontal,showsIndicators: false){
            HStack(alignment: .center){
              ForEach(timeRangType.subIndicators,id:\.self) { subType in
                Button {
                  if subSelectIndicators.contains(subType){
                    subSelectIndicators.removeAll(where: {$0 == subType})
                  }else{
                    subSelectIndicators.append(subType)
                  }
                } label: {
                  Text(subType.title)
                    .foregroundColor(subSelectIndicators.contains(subType) ? Color.black : Color.gray)
                    .font(.system(size: 14.0,weight: subSelectIndicators.contains(subType) ? .semibold : .regular))
                }
                .frame(minWidth:subType.title.textWidth(font: .systemFont(ofSize: 14.0), maxHeight: 40.0),idealHeight: geometry.size.height)
              }
            }
          }
        }
        .padding()
        .frame(maxWidth: .infinity,maxHeight: 40.0)
        
      }
    }
}


