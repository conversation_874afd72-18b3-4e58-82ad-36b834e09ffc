//
//  KlineViewForSwiftUI.swift
//  Demo
//
//  Created by xianjin<PERSON>_<PERSON> on 2025/4/29.
//

import Foundation
import SwiftUI
import KLineLib
import Sharing
public struct KlineViewForSwiftUI:UIViewRepresentable{
  
  
  public typealias UIViewType = KLineView
  private let klineView:KLineView = KLineView(frame: .zero)
  @ObservedObject var viewModel:KlineViewForSwiftUIViewModel
  // MARK: - KLineViewDelegate Callbacks
  var onScrollToStart: (() -> Void)?
  var updatePopData: ((_ kLineModel: KLineModel, _ price: String?) -> Void)?
  var showPopIndicator: ((_ show: Bool, _ position: CGPoint?) -> Void)?
  
  
//  @ObservedObject var themeManager = ThemeManager.shared
//  var theme:Themeable {themeManager.currentTheme}
  //@Shared(.themeSettings) var settings = ThemeSettings.default
  
  
  public func makeUIView(context: Context) -> KLineLib.KLineView {
    klineView.delegate = context.coordinator
    klineView.contextDelegate = context.coordinator
    KLineConfig.autoHiddeninfoViewTime = 2.0
    KLineConfig.clickToDisplayInfoView = true
    KLineConfig.kLineMainViewMinY = 20.0
    KLineConfig.kLineMainViewMaxY = 20.0
    KLineConfig.kLineTimeViewWithBottomView = .mainViewBottom
    KLineConfig.displayindexValueOnView = false
    KLineConfig.subVeiwHeaderHeight = 20.0
    KLineConfig.pricePosition = .left(offset: 5.0)
    KLineConfig.references = [
      SubReferenceLineModel(type: .rsi, maxValue: 100.0, minValue: 0.0, referenceList: [20,50,80])
    ]
    //KLineConfig.themeManager = ChiefKlineThemeColor()
    return klineView
  }
  
  public func updateUIView(_ uiView: KLineLib.KLineView, context: Context) {
    var needUpdateUI = false
    needUpdateUI = KLineConfig.type != viewModel.klineType
    needUpdateUI = needUpdateUI || KLineConfig.mainViewType != viewModel.mainViewTypes
    needUpdateUI = needUpdateUI || KLineConfig.subViewType != viewModel.subViewTypes
    
    KLineConfig.displayAllData = viewModel.displayAllData
    KLineConfig.expectedTotalDataCount = viewModel.expectedTotalDataCount
    KLineConfig.latestPriceColorWithPreviousDay = viewModel.displayAllData
    KLineConfig.mainViewYaxisValueUseColor = viewModel.displayAllData
    KLineConfig.displayMainViewYaxisChangeRate = viewModel.displayAllData
    
    KLineConfig.type = viewModel.klineType


    KLineConfig.mainViewType = viewModel.mainViewTypes
    KLineConfig.subViewType = viewModel.subViewTypes
    if uiView.kLineModels != viewModel.rawDataList{
      needUpdateUI = true
      uiView.setKLineModels(viewModel.rawDataList, false)
    }
    
    if  let needUpdateModel = viewModel.needUpdateModel {
      needUpdateUI = true
      DispatchQueue.main.async {
        uiView.updateLastTimeKLineModels(lastTimeModel: needUpdateModel)
        viewModel.updateLastModel(model: nil)
      }
    }
    if needUpdateUI{
      uiView.reloadKLineIndicator()
    }
    if viewModel.needUpdateKChart{
      DispatchQueue.main.async {
        uiView.updateUI()
        viewModel.updateKChart(need: false)
      }
    }
//    switch settings.darkMode{
//    case .dark:
//      KlineLibThemeManager.currentTheme = .dark
//    case .light:
//      KlineLibThemeManager.currentTheme = .light
//    case .system:
//      break
//    }
  }
  
  public func makeCoordinator() -> Coordinator {
    Coordinator(parent: self,viewModel: viewModel)
  }
  public class Coordinator:NSObject,KDCDelegate,@preconcurrency KlineViewDelegate{
    public func leftmostModelWith(klineView: KLineLib.KLineView, klineModel: KLineLib.KLineModel) {
      
    }
    
    @MainActor public func closingPriceOfThePreviousDay() -> Double? {
      return viewModel.closingPriceOfThePreviousDay
    }
    
    var parent:KlineViewForSwiftUI
    var viewModel:KlineViewForSwiftUIViewModel
    
    init(parent: KlineViewForSwiftUI,viewModel:KlineViewForSwiftUIViewModel) {
      self.parent = parent
      self.viewModel = viewModel
    }
    public func scrollToStartPosition() {
      parent.onScrollToStart?()
    }
    
    public func currentSelectModelWith(klineView: KLineLib.KLineView, klineModel: KLineLib.KLineModel) {
      parent.updatePopData?(klineModel, nil)
    }
    
    public func updatePopDataWith(klineView: KLineLib.KLineView, kLineModel: KLineLib.KLineModel, price: String) {
      parent.updatePopData?(kLineModel, price)
    }
    
    public func showIndicatorViewsWith(klineView: KLineLib.KLineView, isShow: Bool, horizontalStatus: Bool, point: CGPoint?) {
      parent.showPopIndicator?(isShow, point)
    }
    
    public func kLineListDidUpdate(needRewDraw: Bool) {
      parent.klineView.context?.readKlineData(complettion: { list in
        parent.klineView.setKLineModels(list, needRewDraw)
      })
    }
    
    
  }
  
  
}

//@StateObject private var klineView = KlineViewWrapperHolder()
//class KlineViewWrapperHolder:ObservableObject{
//  lazy var view:KlineViewForSwiftUI = {
//   return KlineViewForSwiftUI(rawDataList: self.rawDataList, onScrollToStart: self.onScrollToStart, updatePopData: self.updatePopData, showPopIndicator: self.showPopIndicator)
//  }()
//}
