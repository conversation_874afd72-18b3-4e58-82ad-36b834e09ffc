//
//  KlineViewForSwiftUIViewModel.swift
//  ChiefApp
//
//  Created by <PERSON> on 2025/4/30.
//

import Foundation
import KLineLib
import Combine

enum TimeSharingType:Int,CaseIterable{
  
  case oneDay //分时
  case fiveDay //五日分时
  case other //其他
  var title:String{
    switch self {
    case .oneDay:
      return "分时"
    case .fiveDay:
      return "五日分时"
    case .other:
      return "其他K"
    }
  }
}

@MainActor
class KlineViewForSwiftUIViewModel:ObservableObject{
  
  @Published var needUpdateModel:KLineModel? = nil
  @Published var rawDataList:[KLineModel] = []
  @Published var klineType:KLineViewType = .timeLine
  
  
  /// 是否将所有点全部显示
  @Published var displayAllData:Bool = false
  
  /// 全部显示时 预估点数量
  @Published var expectedTotalDataCount:Int? = nil
  
  @Published var mainViewTypes: [KLineMainViewType] = []
  @Published var subViewTypes: [KLineSubViewType] = []
  
  @Published var needUpdateKChart:Bool = false
  private var  timeSharType:TimeSharingType = .oneDay
  
  /// 用于从五日或者分时 回到其他K时重新显示原本的K线类型
  private var normalKlineType:KLineViewType = .timeLine
  
  private var specialMainViewTypes:[KLineMainViewType] = []
  
  private var normalMainViewTypes:[KLineMainViewType] = []
  
  //MARK: Michael: 上一个收市价
  @Published var closingPriceOfThePreviousDay:Double? = nil
  
  init(rawDataList: [KLineModel] = [], needReDraw: Bool = true, klineType: KLineViewType = .timeLine) {
    self.rawDataList = rawDataList
    self.klineType = klineType
    self.normalKlineType = klineType
  }
  //MARK: Michael: 更新数据
  func updateRawDataList(klineModels:[KLineModel]){
    let tempKlineModels = klineModels
    tempKlineModels.forEach({$0.needAnimation = false})
    rawDataList = tempKlineModels
  }
  //MARK: Michael: 设置K线图类型
  @MainActor
  func updateKLineViewType(type:KLineViewType){
    normalKlineType = type
    guard timeSharType == .other else{return}
    displayAllData = false
    expectedTotalDataCount = nil
    klineType = type
    
    
  }
  
  /// 仅用于 分时/五日分时
  /// - Parameters:
  ///   - type: 数据类型
  ///   - klineModels:数据
  ///   - expectedTotalDataCount: 预估数据数量
  func setTimeSharingTypeWith(type:KChartTimeRangeType,klineModels:[KLineModel],expectedCount:Int){
    
    var tempType:TimeSharingType = .other
    switch type{
    case .minuteHour:
      tempType = .oneDay
    case .fiveDays:
      tempType = .fiveDay
    default:
      tempType = .other
    }
    
    
    timeSharType = tempType
    
    if timeSharType == .other{
      mainViewTypes = normalMainViewTypes
    }else{
      mainViewTypes = specialMainViewTypes
    }
    switch tempType {
    case .oneDay:
      displayAllData = true
      expectedTotalDataCount = expectedCount
      klineType = .timeLine
    case .fiveDay:
      displayAllData = true
      expectedTotalDataCount = expectedCount
      klineType = .fiveDayLine
    case .other:
      displayAllData = false
      expectedTotalDataCount = nil
      klineType = normalKlineType
      
    }
    //MARK: Michael: 配置好后再更新数据
    updateRawDataList(klineModels: klineModels)
  }
  //MARK: Michael: 更新主视图 指标
  func updateMainIndicatorWith(types:[KLineMainViewType]){
    normalMainViewTypes = types.filter({$0 != .vwap})
    specialMainViewTypes = types.filter({$0 == .vwap})
    if timeSharType == .other{
      mainViewTypes = normalMainViewTypes
    }else{
      mainViewTypes = specialMainViewTypes
    }
    
  }
  //MARK: Michael: 更新副视图指标
  func updateSubIndicatorWith(types:[KLineSubViewType]){
    subViewTypes = types.sorted(by: {$0.rawValue < $1.rawValue})
  }
  //MARK: Michael: 追加最后一条数据
  func addKlineData(appendModels: [KLineModel]){
    var tempList = rawDataList
    tempList.forEach({$0.needAnimation = false})
    tempList.append(contentsOf: appendModels)
    rawDataList = tempList
  }
  //MARK: Michael: 动态更新最后一条数据
  func updateLastModel(model:KLineModel?){
    needUpdateModel = model
  }
  //MARK: Michael: 更新前收价
  func updateClosingPriceOfThePreviousDay(closePrice:Double?){
    closingPriceOfThePreviousDay =  closePrice
  }
  func updateKChart(need:Bool = true){
    needUpdateKChart = need
  }
}
