//
//  KChartSetPopUpView.swift
//  Demo
//
//  Created by <PERSON> on 2025/7/2.
//

import SwiftUI
import KLineLib
struct KChartSetPopUpView: View {
  @Binding var restorationType:KChartRestorationType
  @Binding var chartType:KLineViewType
    var body: some View {
      VStack{
        KChartSetPopUpSectionView(restorationItems: KChartRestorationType.allCases, restorationType: $restorationType, klineTypeItems:KLineViewType.allCases,chartType: $chartType)
      }
      .padding(10)
    }
}
fileprivate struct KChartSetPopUpSectionView:View {
  @Environment(\.dismiss) private var dismiss
  
  var restorationItems:[KChartRestorationType]
  @Binding var restorationType:KChartRestorationType
  
  var klineTypeItems:[KLineViewType] = []
  @Binding var chartType:KLineViewType
  
  private let contenWidth = min(UIScreen.main.bounds.size.width, UIScreen.main.bounds.size.height)
  private var restorationColumns:[GridItem]{
    [
      GridItem(.fixed((contenWidth - 40.0)/3.0),spacing: 10.0),
      GridItem(.fixed((contenWidth - 40.0)/3.0),spacing: 10.0),
      GridItem(.fixed((contenWidth - 40.0)/3.0),spacing: 10.0),
    ]
  }
  
  private var klineTypeColumns:[GridItem]{
    [
      GridItem(.fixed((contenWidth - 30.0)/2.0),spacing: 10.0),
      GridItem(.fixed((contenWidth - 30.0)/2.0),spacing: 10.0),
    ]
  }
  
  var body: some View{
    ScrollView{
      LazyVGrid(columns: restorationColumns, spacing: 10.0) {
        Section(header: sectionHeader(title: "复权")) {
          ForEach(restorationItems, id: \.self) { type in
            RestorationView(type: type)
          }
        }
      }
      .padding(10)
      LazyVGrid(columns: klineTypeColumns, spacing: 10.0) {
        Section(header: sectionHeader(title: "主图类别")) {
          ForEach(klineTypeItems, id: \.self) { type in
            ChartTypeView(type: type)
          }
        }
      }
      .padding(10)
    }
    .padding()
  }
  
  //MARK: Michael: 分组标题视图
    func sectionHeader(title: String) -> some View {
      HStack{
        Text(title)
          .foregroundColor(.gray)
          .font(.system(size: 14.0))
        Spacer()
      }
    }
  //MARK: Michael: 复权视图
  func RestorationView(type:KChartRestorationType) -> some View{
    Button {
      restorationType = type
      dismiss()
    } label: {
      Text(type.title)
          .padding() // 添加内边距
          .frame(maxWidth: (contenWidth - 40.0)/3.0,idealHeight: 40.0,maxHeight: 40.0)
          .background(Color.gray.opacity(0.1)) // 灰色背景
          .foregroundColor(.black) // 文字颜色
          .cornerRadius(10) // 圆角半径
          .overlay(
              RoundedRectangle(cornerRadius: 10) // 与背景相同的圆角半径
                  .stroke(restorationType == type ? Color.black : Color.clear, lineWidth: 1.0) // 黑色边框，线宽1
          )
    }
  }
  //MARK: Michael: 主图类别视图
  func ChartTypeView(type:KLineViewType) -> some View{
    Button {
      chartType = type
      dismiss()
    } label: {
      HStack{
        Text(type.title)
          .padding() // 添加内边距
        Spacer()
        Image(type.iconName)
      }
      .padding()
      .frame(maxWidth: (contenWidth - 30.0)/2.0,idealHeight: 40.0,maxHeight: 40.0)
      .background(Color.gray.opacity(0.1)) // 灰色背景
      .foregroundColor(.black) // 文字颜色
      .cornerRadius(10) // 圆角半径
      .overlay(
        RoundedRectangle(cornerRadius: 10) // 与背景相同的圆角半径
          .stroke(chartType == type ? Color.black : Color.clear, lineWidth: 1.0) // 黑色边框，线宽1
      )

    }
  }
}
