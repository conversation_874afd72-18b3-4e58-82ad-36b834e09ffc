//
//  KlineTestView.swift
//  Demo
//
//  Created by <PERSON> on 2025/6/27.
//

import SwiftUI
import KLineLib
import Combine
import Sharing

struct KlineTestView: View {
  @State var showConfiguration:Bool = false
  @StateObject private var klineViewModel = KlineViewForSwiftUIViewModel()
  /*
  let kLineTimeTypePublisher = CurrentValueSubject<TimeSharingType,Never>(.other)
  let kLineTypePublisher = CurrentValueSubject<KLineViewType,Never>(.timeLine)
  let kLineMainTypePublisher = CurrentValueSubject<[KLineMainViewType],Never>([])
  let kLineSubTypePublisher = CurrentValueSubject<[KLineSubViewType],Never>([])
   */
  //MARK: Michael: infoView
  @State private var showInfoView:Bool = false
  @State private var currentModel:KLineModel = KLineModel()
  @State private var price:String = ""
  @State private var postion:CGPoint = .zero
  //MARK: Michael: 默认选中时间K 类型
  @State var selectTimeRang:KChartTimeRangeType = .minuteHour
  //MARK: Michael: 复权类型
  @State var restorationType:KChartRestorationType = .backWard
  //MARK: Michael: 主图类别
  @State var chartType:KLineViewType = .kline
  //MARK: Michael: 主视图指标
  @Shared(.codableArray(key: "mainViewTypes")) var mainSelectIndicators:[KLineMainViewType] = []
  //MARK: Michael: 副视图指标
  @Shared(.codableArray(key: "subViewTypes")) var subSelectIndicators:[KLineSubViewType] = []
  //MARK: Michael: 标记主视图是否有显示指标
  @State private var klineMainViewHaveIndicators:Bool = false
  //MARK: Michael: 主图高度
  private let KlineMainViewHeight:CGFloat = 200.0
  //MARK: Michael: 主图宽度
  @State private var KlineMainViewWidth:CGFloat = UIScreen.main.bounds.size.width
  //MARK: Michael: 右侧交易记录视图宽度
  private let recordListViewWidth:CGFloat = 100.0
  
  //MARK: Michael: 是否显示RecordListView
  @State private var displayRecord:Bool = true
  //MARK: Michael: 是否显示 RecordListView 的button
  @State private var displayExpandBtn:Bool = true
  //MARK: Michael: button 选中状态
  @State private var expandBtnIsSelect:Bool = true
  //MARK: Michael: 展开视图button的offset
  @State private var expandBtnOffst:CGFloat = 0
  
  var testMainSelectIndicators: Binding<[KLineMainViewType]> {
    Binding(
      get: { mainSelectIndicators },
      set: { newValue in
        $mainSelectIndicators.withLock({$0 = newValue})
      }
    )
  }
  
  var testSubSelectIndicators: Binding<[KLineSubViewType]> {
    Binding(
      get: { subSelectIndicators },
      set: { newValue in
        $subSelectIndicators.withLock({$0 = newValue})
      }
    )
  }

  //MARK: Michael: =================================
  var body: some View {
    NavigationView {
      VStack(alignment: .leading){
        //kLineChartType()
        KChartTimeRangeView(selectTimeRang: $selectTimeRang, restorationType: $restorationType, chartType: $chartType)
          .frame(maxWidth: .infinity,maxHeight: 40.0)
        Divider()
        Spacer()
          .frame(height: klineMainViewHaveIndicators ? KLineConfig.subVeiwHeaderHeight : 0)
        HStack{
          ZStack(alignment: .bottomTrailing){
            klineView()
            if displayExpandBtn{
              Button {
                expandBtnIsSelect.toggle()
                displayRecord.toggle()
                klineViewModel.updateKChart(need: true)
                calculationKlineMainViewWidth()
              } label: {
                Image(expandBtnIsSelect ? "chart_collapse" :"chart_expand")
                  .frame(width: 16.0, height: 24.0)
              }
              .offset(y: expandBtnOffst)
            }
          }
          if displayExpandBtn,displayRecord{
            KChartTransactionRecordView()
              .frame(maxWidth: 130.0,maxHeight: KlineMainViewHeight + CGFloat(subSelectIndicators.count)  * (KLineConfig.kLineSubViewHeight + KLineConfig.subVeiwHeaderHeight))
              //.frame(width: recordListViewWidth)
          }
          
        }
        .frame(width: UIScreen.main.bounds.size.width)
        KChartIndicatorView(timeRangType:$selectTimeRang,mainSelectIndicators: testMainSelectIndicators, subSelectIndicators: testSubSelectIndicators)
          .frame(maxWidth: .infinity,maxHeight: 40.0)
        //测试
        ForTestextractedFunc()
        Spacer()
      }
      .onAppear() {
        loadData()
      }
      .onChange(of: chartType, perform: { newValue in
        klineViewModel.updateKLineViewType(type: newValue)
      })
      .onChange(of: selectTimeRang, perform: { newValue in
        DispatchQueue.main.async {
          let list = MockDataModel.createMockData()
          klineViewModel.setTimeSharingTypeWith(type: newValue, klineModels: list, expectedCount: list.count)
          displayExpandBtn = newValue == .minuteHour
          calculationKlineMainViewWidth()
        }
        
      })
      .onChange(of: mainSelectIndicators, perform: { newValue in
        judgeMainViewNeedOffset()
        klineViewModel.updateMainIndicatorWith(types: newValue)
      })
      .onChange(of: subSelectIndicators, perform: { newValue in
        klineViewModel.updateSubIndicatorWith(types: newValue)
        calculationExpandBtnOffst()
      })
      //.navigationTitle("KLine")
    }
  }
}
extension KlineTestView{
  
  func loadData(){
    let list = MockDataModel.createMockData()
    self.klineViewModel.updateRawDataList(klineModels: list)
    klineViewModel.updateKLineViewType(type: chartType)
    klineViewModel.setTimeSharingTypeWith(type: selectTimeRang, klineModels: list, expectedCount: 0)
    klineViewModel.updateMainIndicatorWith(types: mainSelectIndicators)
    klineViewModel.updateSubIndicatorWith(types: subSelectIndicators)
    
    klineViewModel.updateClosingPriceOfThePreviousDay(closePrice: list.last?.close)
    judgeMainViewNeedOffset()
    
    displayExpandBtn = selectTimeRang == .minuteHour
    calculationExpandBtnOffst()
  }
  private func judgeMainViewNeedOffset(){
    if selectTimeRang == .minuteHour || selectTimeRang == .fiveDays{
      klineMainViewHaveIndicators = mainSelectIndicators.filter({$0 == .vwap}).count > 0
    }else{
      klineMainViewHaveIndicators = mainSelectIndicators.filter({$0 != .vwap}).count > 0
    }
  }
  private func calculationExpandBtnOffst(){
    if subSelectIndicators.count > 0{
      expandBtnOffst = CGFloat(-subSelectIndicators.count) * (KLineConfig.subVeiwHeaderHeight +  KLineConfig.kLineSubViewHeight) +  KLineConfig.subVeiwHeaderHeight
    }else{
      expandBtnOffst = -50.0
    }
  }
  private func calculationKlineMainViewWidth(){
    if displayExpandBtn{
      if displayRecord{
        KlineMainViewWidth = UIScreen.main.bounds.size.width - recordListViewWidth
      }else{
        KlineMainViewWidth = UIScreen.main.bounds.size.width
      }
    }else{
      KlineMainViewWidth = UIScreen.main.bounds.size.width
    }
    
  }
  private func kLineChartType() -> some View {
    Picker("KlineViewType", selection: $chartType) {
      ForEach(KLineViewType.allCases, id: \.self) {
        viewType in
        Text(viewType.title)
      }
    }
    .pickerStyle(.segmented)
  }
  
  
  @ViewBuilder
  private func klineView()-> some View{
    ZStack(alignment: .topLeading) {
      KlineViewForSwiftUI(viewModel: klineViewModel, onScrollToStart: {
        print("请求加载之前的数据")
      }, updatePopData: {  kLineModel, price in
        //currentKlineModel = (kLineModel,price)
        currentModel = kLineModel
        self.price = price ?? ""
      }, showPopIndicator: { show, position in
        self.postion = position ?? .zero
        showInfoView = show
      })
      .frame(maxHeight: KlineMainViewHeight + CGFloat(subSelectIndicators.count)  * (KLineConfig.kLineSubViewHeight + KLineConfig.subVeiwHeaderHeight))
      .overlay(content: {
        if showInfoView{
          KLineGesturePopSwiftUIView(kLineModel: currentModel, price: price, isShowing: showInfoView, position: postion)
            .allowsHitTesting(false)
        }
      })
      //主视图指标Header View
      ForEach(Binding(get: {
        if selectTimeRang == .minuteHour || selectTimeRang == .fiveDays{
          return mainSelectIndicators.filter({$0 == .vwap})
        }else{
          return mainSelectIndicators.filter({$0 != .vwap})
        }
        
      }, set: { newValue in
        $mainSelectIndicators.withLock({$0 = newValue})
      }).indices, id: \.self) { index in
        if selectTimeRang == .minuteHour || selectTimeRang == .fiveDays{
          if index < mainSelectIndicators.filter({$0 == .vwap}).count {
            let type = mainSelectIndicators.filter({$0 == .vwap})[index]
            KchartIndicatorHeaderView(mainIndicatorType: type, klineModel: currentModel)
              .offset(y:-KLineConfig.subVeiwHeaderHeight)
          }
        }else{
          if index < mainSelectIndicators.filter({$0 != .vwap}).count {
            let type = mainSelectIndicators.filter({$0 != .vwap})[index]
            KchartIndicatorHeaderView(mainIndicatorType: type, klineModel: currentModel)
              .offset(y:-KLineConfig.subVeiwHeaderHeight)
          }
        }
      }
      //副视图指标Header View
      ForEach(Binding(get: {
        subSelectIndicators
      }, set: { newValue in
        $subSelectIndicators.withLock({$0 = newValue})
      }).indices, id: \.self) { index in
        if index < subSelectIndicators.count {
          let type = subSelectIndicators.sorted(by: {$0.rawValue < $1.rawValue})[index]
          KchartIndicatorHeaderView(subIndicatorType: type, klineModel: currentModel)
            .offset(x:0,y:KlineMainViewHeight + CGFloat(index) * (KLineConfig.subVeiwHeaderHeight + KLineConfig.kLineSubViewHeight))
        }
      }
    }
    
    
    
    
    
  }
  
  @ViewBuilder
  private func ForTestextractedFunc() -> some View{
    HStack{
      Button("添加数据") {
        var list:[KLineModel] = []
        let model = KLineModel()
        model.open = 83913.97 + Double.random(in: 0...500)
        model.close = 83870.78 + Double.random(in: 0...500)
        model.low = 83867.83 + Double.random(in: 0...500)
        model.high = 83981.71 + Double.random(in: 0...500)
        model.amount = 11.637195349326772 + Double.random(in: 0...5)
        model.volume = 976428.4183398 + Double.random(in: 0...50)
        let timestamp = Date().timeIntervalSince1970
        print("date is \(timestamp)")
        model.timestamp = timestamp
        model.isShowTime = Bool.random()
        //model.needAnimation = true
        list.append(model)
        
        klineViewModel.addKlineData(appendModels: list)
      }
      Button("更新最后一条数据") {
        if let last = klineViewModel.rawDataList.last{
          last.close = 83870.78 + Double.random(in: 0...500)
          klineViewModel.updateLastModel(model: last)
        }
        
      }
    }
  }
}
#Preview {
  KlineTestView()
}



