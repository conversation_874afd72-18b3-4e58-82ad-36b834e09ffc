//
//  KChartTransactionRecordPopUpView.swift
//  Demo
//
//  Created by <PERSON> on 2025/7/2.
//

import SwiftUI

struct KChartTransactionRecordPopUpView: View {
  @State private var currentPage = 0
  
  
  var body: some View {
    Spacer()
      .frame(height: 20.0)
    SegmentView()
    
    TabView(selection: $currentPage) {
      KChartTransactionRecordListView()
        .tag(0)
      KChartTransactionRecordDistributionView()
        .tag(1)
    }
    .tabViewStyle(.page(indexDisplayMode: .never)) // 启用页面样式，支持滑动
    .animation(.easeInOut, value: currentPage)
    
  }
  //MARK: Michael: SegmentView
  fileprivate func SegmentView() -> some View {
    return HStack {
      Button {
        currentPage = 0
      } label: {
        Text("成交记录")
          .foregroundColor( Color.black.opacity(currentPage == 0 ? 1 : 0.5) )
      }
      Button {
        currentPage = 1
      } label: {
        Text("成交分布")
          .foregroundColor( Color.black.opacity(currentPage == 1 ? 1 : 0.5) )
      }
      
      
      Spacer()
      Button {
        
      } label: {
        Image(systemName: "plus.rectangle.on.folder.fill")
      }
      
    }
    .padding()
  }
  
}
//MARK: Michael: 左侧《成交记录》View
struct KChartTransactionRecordListView:View {
  @State var items:[Int] = Array(0...100)
  var body: some View {
    VStack{
      VStack(spacing: 0){
        Divider()
        HStack{
          TextViewWith(value:"时间")
          TextViewWith(value:"价格")
          TextViewWith(value:"股数")
          TextViewWith(value:"金额")
          TextViewWith(value:"B/S")
        }
        .frame(maxWidth: .infinity, maxHeight: 30.0)
        Divider()
      }
      ScrollView{
        LazyVStack{
          ForEach($items, id: \.self) { item in
            KChartTransactionRecordListCell()
          }
        }
      }
      HStack{
        Text("实时资料")
        Spacer()
        Button {
          
        } label: {
          Text("下一页")
        }
        
      }
      .padding()
    }
  }
  private func KChartTransactionRecordListCell()-> some View{
    HStack{
      TextViewWith(value: "15:59:59")
      TextViewWith(value: "500.5")
      TextViewWith(value: "888")
      TextViewWith(value: "888888")
      TextViewWith(value: "Buy")
    }
  }
  
  private func TextViewWith(value:String,alignment:TextAlignment = .center) ->some View{
    Text(value)
      .multilineTextAlignment(alignment)
      .allowsTightening(true) // 允许字符间距紧缩
      .font(.system(size: 12.0))
      .minimumScaleFactor(0.5)
      .lineLimit(1) // 单行文本
      .truncationMode(.tail) // 截断模式（尾部显示...）
      .frame(maxWidth: .infinity)
    
  }
}
//MARK: Michael: 右侧《成交分布》View
struct KChartTransactionRecordDistributionView:View {
  @State var dataList:[Int] = Array(0...10)
  var body: some View {
    ScrollView{
      LazyVStack{
        ForEach(dataList, id: \.self) { item in
          KChartTransactionRecordDistributionCell()
        }
      }
    }
    
  }
  
  private func KChartTransactionRecordDistributionCell() -> some View{
    HStack{
      Text("510.500")
      SquareViewWithOverlay(squareColor: .red, overlayColor: .blue, overlayPercentage: CGFloat.random(in: 0...100)/100.0)
        .frame(maxWidth: .infinity,maxHeight: 20.0)
      Text("4.6M")
      Text("51.20K")
      Spacer()
    }
    .padding(EdgeInsets(top: 0, leading: 10, bottom: 0, trailing: 10))
  }
  
  struct SquareViewWithOverlay: View {
    let squareColor: Color
    let overlayColor: Color
    let overlayPercentage: CGFloat // 0.0 - 1.0
    
    var body: some View {
      GeometryReader { geometry in
        let geometryWidth = geometry.size.width
        let geometryHeight = geometry.size.height
        
        ZStack(alignment: .leading) {
          Rectangle()
            .fill(squareColor)
            .frame(width: geometryWidth, height: geometryHeight)
          
          // 覆盖视图
          Rectangle()
            .fill(overlayColor)
            .frame(width: geometryWidth * overlayPercentage, height: geometryHeight )
        }
      }
    }
  }
}
#Preview {
  KChartTransactionRecordPopUpView()
}
