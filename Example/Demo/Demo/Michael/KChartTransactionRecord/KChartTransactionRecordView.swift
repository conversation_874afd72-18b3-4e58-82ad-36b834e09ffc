//
//  KChartTransactionRecord.swift
//  Demo
//
//  Created by <PERSON> on 2025/7/2.
//

import SwiftUI

struct KChartTransactionRecordView: View {
  @State var items:[Int] = Array(0...100)
  @State private var display = false
    var body: some View {
      ScrollView(.vertical) {
        LazyVStack(alignment: .leading,spacing: 5.0){
          ForEach(items, id: \.self) { item in
            KChartTransactionRecordCell()
          }
        }
        .padding(.zero)
      }
      .padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 5))
      .onTapGesture {
        display = true
      }
      .sheet(isPresented: $display) {
        display = false
      } content: {
        if #available(iOS 16.0, *) {
          KChartTransactionRecordPopUpView()
            .presentationDetents([.large])
            .presentationDragIndicator(.visible)
        } else {
          KChartTransactionRecordPopUpView()
          .introspect(.sheet, on: .iOS(.v15)) {
            $0.detents = [.large()]
            $0.prefersGrabberVisible = true
          }
          .interactiveDismissDisabled()
        }
      }
    }
}
struct KChartTransactionRecordCell:View {
  var body: some View {
    HStack(spacing: 5.0){
      TextViewWith(value:"88:88")
      TextViewWith(value:"888:888")
      TextViewWith(value:"8888")
      Image("trade_direction_up")
    }
    .padding(.zero)
  }
  private func TextViewWith(value:String) ->some View{
    Text(value)
      .multilineTextAlignment(.leading)
      .allowsTightening(true) // 允许字符间距紧缩
      .font(.system(size: 10.0))
      .minimumScaleFactor(0.5)
      .lineLimit(1) // 单行文本
      .truncationMode(.tail) // 截断模式（尾部显示...）
      
  }
}
#Preview {
    KChartTransactionRecordView()
}
