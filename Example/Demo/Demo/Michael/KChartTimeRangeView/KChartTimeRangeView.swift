//
//  KChartTimeRangeView.swift
//  Demo
//
//  Created by <PERSON> on 2025/6/30.
//

import SwiftUI
import KLineLib

struct KChartTimeRangeView: View {
  @Binding var selectTimeRang:KChartTimeRangeType
  private var customBinding:Binding<KChartTimeRangeType?>{
    Binding {
      selectTimeRang
    } set: { newValue in
      selectTimeRang = newValue!
    }

  }
  private var lastArrowItmeBind:Binding<KChartTimeRangeType?>{
    Binding {
      lastArrowItme
    } set: { newValue in
      if let newValue = newValue{
        if items.contains(newValue){
          //MARK: Michael: items 中已包含 就不设置
        }else{
          lastArrowItme = newValue
        }
      }
      
      
    }

  }
  @State var items:[KChartTimeRangeType] = KChartTimeRangeType.allCases
  @State var lastArrowItme:KChartTimeRangeType = .twoMinutes
  //MARK: Michael: 复权
  @Binding var restorationType:KChartRestorationType
  //MARK: Michael: 主图类别
  @Binding var chartType:KLineViewType
  
  
  @State private var showMore:Bool = false
  @State private var showSetPopUp  = false
  
  
  private let font = Font.system(size: 14.0)
    var body: some View {
      HStack(alignment: .center,spacing: 5.0) {
        //MARK: Michael: 除开最后一个可显示的所以btn
        ForEach($items, id: \.self) { item in
          Button {
              selectTimeRang = item.wrappedValue
          } label: {
              Text("\(item.wrappedValue.title)")
                .font(font)
                .foregroundColor(selectTimeRang == item.wrappedValue ? .blue : .black)
          }
          .frame(minWidth: item.wrappedValue.width ,maxHeight: .infinity)
          .background(selectTimeRang == item.wrappedValue ? .blue.opacity(0.4) : .clear)
          .cornerRadius(5)

        }
        
        //MARK: Michael: 最后一个有下拉箭头的按钮
        Button {
            if lastArrowItme == selectTimeRang{
              showMore = true
            }else{
              selectTimeRang = lastArrowItme
            }
          
        } label: {
          HStack(alignment: .center, spacing: 5){
            Text("\(lastArrowItme.title)")
              .font(font)
              .foregroundColor(selectTimeRang == lastArrowItme ? .blue : .black)
            Image("chart_arrow_down")
          }
        }
        .frame(minWidth: lastArrowItme.width + 11,maxHeight: .infinity)
        .background(selectTimeRang == lastArrowItme ? .blue.opacity(0.4) : .clear)
        .cornerRadius(5)
        
        //Spacer()
        //MARK: Michael: 设置按钮
        Button {
          showSetPopUp = true
        } label: {
          Image("chart_set")
            .padding() // 增加内边距
            .frame(width: 40.0,height: 40.0)
            .background(Color.clear)
            .contentShape(Rectangle()) // 使整个区域可点击
        }
        
      }
      .padding(EdgeInsets(top: 5, leading: 5, bottom: 5, trailing: 5))
      .onAppear(perform: {
        var tempList = calculationMaxCount()
        lastArrowItme = tempList.last!
        tempList.removeLast()
        items = tempList
      })
      .sheet(isPresented: $showMore) {
        showMore = false
      } content: {
        if #available(iOS 16.0, *) {
          KChartTimeRangePopUpView(selectTimeRang: customBinding,lastArrowItme: lastArrowItmeBind)
            .presentationDetents([.height(350),.medium, .large])
            .presentationDragIndicator(.visible)
        } else {
          KChartTimeRangePopUpView(selectTimeRang: customBinding,lastArrowItme: lastArrowItmeBind)
          .introspect(.sheet, on: .iOS(.v15)) {
            $0.detents = [.medium(), .large()]
            $0.prefersGrabberVisible = true
          }
          .interactiveDismissDisabled()
        }
      }
      .sheet(isPresented: $showSetPopUp) {
        showSetPopUp = false
      } content: {
        if #available(iOS 16.0, *) {
          KChartSetPopUpView(restorationType: $restorationType, chartType: $chartType)
            .presentationDetents([.height(350),.medium, .large])
            .presentationDragIndicator(.visible)
        } else {
          KChartSetPopUpView(restorationType: $restorationType, chartType: $chartType)
          .introspect(.sheet, on: .iOS(.v15)) {
            $0.detents = [.medium(), .large()]
            $0.prefersGrabberVisible = true
          }
          .interactiveDismissDisabled()
        }
      }

    }
  
  private func calculationMaxCount()-> [KChartTimeRangeType]{
    let all = KChartTimeRangeType.allCases
    var tempList:[KChartTimeRangeType] = []
    let contenWidth = min(UIScreen.main.bounds.size.width, UIScreen.main.bounds.size.height) - 60
    var tempWidth:CGFloat = 10 + 11// 10为左右两侧间距 11 为下拉箭头
    all.forEach { type in
      tempWidth += type.width
      if tempWidth <= contenWidth{
        tempList.append(type)
      }
      
    }
    return Array(tempList.prefix(12))

  }
}
