//
//  KChartTimeRangePopUpView.swift
//  Demo
//
//  Created by <PERSON> on 2025/6/30.
//

import SwiftUI

struct KChartTimeRangePopUpView: View {
  @Binding var selectTimeRang:KChartTimeRangeType?
  @Binding var lastArrowItme:KChartTimeRangeType?
    var body: some View {
      VStack(alignment: .leading){
        KChartTimeRangePopUpSectionView(minuteItmes: KChartTimeRangeType.minuteItmes,
                                        hoursItmes: KChartTimeRangeType.hoursItmes,
                                        selectTimeRang: $selectTimeRang,
                                        lastArrowItme: $lastArrowItme)
      }
      .padding(10)
    }
}

struct KChartTimeRangePopUpSectionView:View {
  @Environment(\.dismiss) private var dismiss
  var minuteItmes:[KChartTimeRangeType]
  var hoursItmes:[KChartTimeRangeType]
  @Binding var selectTimeRang:KChartTimeRangeType?
  @Binding var lastArrowItme:KChartTimeRangeType?
  
  private let contenWidth = min(UIScreen.main.bounds.size.width, UIScreen.main.bounds.size.height)
  private var columns:[GridItem]{
    [
      GridItem(.fixed((contenWidth - 50.0)/4.0),spacing: 10.0),
      GridItem(.fixed((contenWidth - 50.0)/4.0),spacing: 10.0),
      GridItem(.fixed((contenWidth - 50.0)/4.0),spacing: 10.0),
      GridItem(.fixed((contenWidth - 50.0)/4.0),spacing: 10.0),
    ]
  }
  
  
  var body: some View {
    ScrollView{
      //MARK: Michael: 分钟
      LazyVGrid(columns: columns,spacing: 10.0) {
        Section(header: sectionHeader(title: "分钟")) {
          ForEach(minuteItmes, id: \.self) { type in
            KChartTimeRangeView(type: type)
          }
        }
      }
      .padding(10)
      //MARK: Michael: 小时
      LazyVGrid(columns: columns,spacing: 10.0) {
        Section(header: sectionHeader(title: "小时")) {
          ForEach(hoursItmes, id: \.self) { type in
            KChartTimeRangeView(type: type)
          }
        }
      }
      .padding(10)
    }
    .padding()
    Spacer()
  }
  
  //MARK: Michael: 分组标题视图
    func sectionHeader(title: String) -> some View {
      HStack{
        Text(title)
          .foregroundColor(.gray)
          .font(.system(size: 14.0))
        Spacer()
      }
    }
  //MARK: Michael: 时间展示视图
  func KChartTimeRangeView(type:KChartTimeRangeType) -> some View{
    Button {
      selectTimeRang = type
      lastArrowItme = type
      dismiss()
    } label: {
      Text(type.title)
          .padding() // 添加内边距
          .frame(maxWidth: (contenWidth - 50.0)/4.0,idealHeight: 40.0,maxHeight: 40.0)
          .background(Color.gray.opacity(0.1)) // 灰色背景
          .foregroundColor(.black) // 文字颜色
          .cornerRadius(10) // 圆角半径
          .overlay(
              RoundedRectangle(cornerRadius: 10) // 与背景相同的圆角半径
                  .stroke(selectTimeRang == type ? Color.black : Color.clear, lineWidth: 1.0) // 黑色边框，线宽1
          )
    }
  }
}
