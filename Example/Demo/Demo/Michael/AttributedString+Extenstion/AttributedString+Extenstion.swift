//
//  AttributedString+Extenstion.swift
//  Demo
//
//  Created by <PERSON> on 2025/7/1.
//

import Foundation
import SwiftUI
extension AttributedString{
  @discardableResult mutating func createAttributedStringWith(text:String,font:Font,color:Color)->AttributedString{
    var text = AttributedString(text)
    text.font = font
    text.foregroundColor = color
    
    append(text)
    return self
  }
}
