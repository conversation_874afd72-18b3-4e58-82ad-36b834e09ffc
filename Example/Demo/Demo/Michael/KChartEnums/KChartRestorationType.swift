//
//  KChartRestorationType.swift
//  Demo
//
//  Created by <PERSON> on 2025/7/2.
//


import SwiftUI
import <PERSON><PERSON><PERSON>L<PERSON>

public enum KChartRestorationType:Int,CaseIterable{
  case backWard = 0
  case forWard
  case noneWard
  var title:String{
    switch self {
    case .backWard:
      return "前复权"
    case .forWard:
      return "后复权"
    case .noneWard:
      return "不复权"
    }
  }
}
