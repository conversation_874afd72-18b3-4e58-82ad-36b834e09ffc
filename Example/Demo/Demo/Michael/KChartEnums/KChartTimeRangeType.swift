//
//  TimeRangeType.swift
//  Demo
//
//  Created by <PERSON> on 2025/6/30.
//


import SwiftUI
import K<PERSON><PERSON><PERSON>ib

enum KChartTimeRangeType:Int,CaseIterable{
  case minuteHour = 0
  case fiveDays
  case oneDay
  case onWeek
  case onMonth
  case onYear
  case aMinute
  case twoMinutes
  case threeMintes
  case fiveMinutes
  case tenMinutes
  case fifteenMinutes
  case thirtyMinutes
  case oneHour
  case twoHours
  case fourHours
  var title:String{
    switch self {
    case .minuteHour:
      return "分时"
    case .fiveDays:
      return "五日"
    case .oneDay:
      return "日K"
    case .onWeek:
      return "周K"
    case .onMonth:
      return "月K"
    case .onYear:
      return "年K"
    case .aMinute:
      return "1分"
    case .twoMinutes:
      return "2分"
    case .threeMintes:
      return "3分"
    case .fiveMinutes:
      return "5分"
    case .tenMinutes:
      return "10分"
    case .fifteenMinutes:
      return "15分"
    case .thirtyMinutes:
      return "30分"
    case .oneHour:
      return "1小时"
    case .twoHours:
      return "2小时"
    case .fourHours:
      return "4小时"
    }
  }
  var width:CGFloat{
    let width = title.textHeight(font: .systemFont(ofSize: 14.0), maxWidth: 20)
    return width < 40 ? 40 : width
  }
  var mainIndicators:[KLineMainViewType]{
    switch self{
    case .minuteHour,.fiveDays:
      return [.vwap]
    default:
      return KLineMainViewType.allCases.sorted(by: {$0.rawValue < $1.rawValue})
    }
  }
  var subIndicators:[KLineSubViewType]{
    return KLineSubViewType.allCases.sorted(by: {$0.rawValue < $1.rawValue})
  }
  
  static var minuteItmes:[KChartTimeRangeType] = [
    .aMinute,
    .twoMinutes,
    .threeMintes,
    .fiveMinutes,
    .tenMinutes,
    .fifteenMinutes,
    .thirtyMinutes,
  ]
  static var hoursItmes:[KChartTimeRangeType] = [
    .oneHour,
    .twoHours,
    .fourHours,
  ]
}
