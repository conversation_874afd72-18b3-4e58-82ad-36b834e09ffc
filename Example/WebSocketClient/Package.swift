// swift-tools-version: 6.0
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
    name: "WebSocketClient",
    platforms: [
        .iOS(.v14),
        .macOS(.v12)
    ],
    products: [
        // Products define the executables and libraries a package produces, making them visible to other packages.
        .library(
            name: "WebSocketClient",
            targets: ["WebSocketClient"]),
    ],
    dependencies: [
        .package(url: "https://github.com/daltoniam/Starscream.git", from: "4.0.6"),
        .package(url: "https://github.com/pointfreeco/swift-sharing.git", from: "2.0.0"),
        .package(url: "https://github.com/swhitty/swift-mutex.git", from: "0.0.5"),
        .package(url: "https://github.com/tgrapperon/swift-dependencies-additions", from: "1.1.1")
    ],
    targets: [
        // Targets are the basic building blocks of a package, defining a module or a test suite.
        // Targets can depend on other targets in this package and products from dependencies.
        .target(
            name: "WebSocketClient",
            dependencies: [
                .product(name: "Starscream", package: "<PERSON>cream"),
                .product(name: "Sharing", package: "swift-sharing"),
                .product(name: "Mutex", package: "swift-mutex"),
                .product(name: "DependenciesAdditions", package: "swift-dependencies-additions")
            ]
        ),

    ],
    swiftLanguageModes: [.v5]
)
