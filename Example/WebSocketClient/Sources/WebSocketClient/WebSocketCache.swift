import Foundation
import Mutex
import Dependencies

public struct WebSocketCache: <PERSON><PERSON><PERSON>, Sendable {
    private let id = UUID()
    let values = Values()
    public static func ==(lhs: Self, rhs: Self) -> Bool {
        lhs.id == rhs.id
    }
    public func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    final class Values: Sendable {
        let storage = Mutex<[String: Data]>([:])
        subscript(key: String) -> Data? {
            get { storage.withLock { $0[key] }}
            set {
                storage.withLock {
                    $0[key] = newValue
                }
            }
        }
    }
}

extension WebSocketCache: TestDependencyKey {
    public static var previewValue: WebSocketCache { WebSocketCache() }
    public static var testValue: WebSocketCache { WebSocketCache() }
}

public enum DefaultWebSocketCacheKey: DependencyKey {
    public static var liveValue: WebSocketCache { WebSocketCache() }
}

extension DependencyValues {
    public var webSocketCache: WebSocketCache {
        get { self[WebSocketCache.self] }
        set { self[WebSocketCache.self] = newValue }
    }
}
