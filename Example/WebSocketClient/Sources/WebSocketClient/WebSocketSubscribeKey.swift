
import Foundation
import Mutex
import Sharing
import DependenciesAdditions

public protocol WebSocketMessageProtocol: Decodable, Sendable {
    var topic: WebSocketClient.Topic { get }
    var symbol: String { get }
}

extension SharedReaderKey {
    public static func subscribeKey<Params: Codable & Sendable, MessageData: Decodable & Sendable>(_ key: String) -> Self where Self == WebSocketSubscribeKey<Params, MessageData>.Default {
        Self[WebSocketSubscribeKey(key: key), default: nil]
    }
    public static func globalWebSocketCache() -> Self where Self == WebSocketCacheKey.Default {
        Self[WebSocketCacheKey(), default: [:]]
    }
}

public struct WebSocketSubscribeKey<Params: Codable & Sendable, MessageData: Decodable & Sendable>: SharedReaderKey {
    public typealias Value = WebSocketMessage<Params, MessageData>?
    @SharedReader(.globalWebSocketCache()) var cache
    private let key: String
    public init(key: String) {
        self.key = key
    }
    public var id: WebSocketSubscribeKeyID {
        WebSocketSubscribeKeyID(key: key)
    }
    public struct WebSocketSubscribeKeyID: Hashable {
        let key: String
    }
    @Dependency(\.decode) var decode
    public func load(
        context: LoadContext<Value>,
        continuation: LoadContinuation<Value>
    ) {
        if let cacheData = cache[key] {
            do {
                let message = try decode(Value.self, from: cacheData)
                continuation.resume(returning: message)
            } catch {
                continuation.resumeReturningInitialValue()
            }
        } else {
            continuation.resumeReturningInitialValue()
        }
    }
    @Dependency(
        \.logger[
            subsystem: Bundle.main.bundleIdentifier ?? "",
            category: "WebSocketSubscribeKey"]) var logger
    public func subscribe(
        context: LoadContext<Value>,
        subscriber: SharedSubscriber<Value>
    ) -> SharedSubscription {
        let cancellable = $cache
            .publisher
            .compactMap { $0[key] }
            .removeDuplicates()
            .sink { updatedData in
                do {
                    let message = try decode(Value.self, from: updatedData)
                    subscriber.yield(message)
                } catch {
                    logger.error("\(error, privacy: .auto)")
                }
            }
        
        return SharedSubscription {
            logger.debug("WebSocket \(key) subscription cancelled")
            cancellable.cancel()
        }
    }
}

public struct WebSocketCacheKey: SharedReaderKey {
    public typealias Value = [String: Data]
    private let store: WebSocketCache
    private let webSocket: WebSocketClient
    private let subscribeTask = Mutex<Task<Void, Never>?>(nil)
    public init() {
        @Dependency(\.webSocketCache) var webSocketCache
        self.store = webSocketCache
        @Dependency(\.webSocket) var webSocket
        self.webSocket = webSocket
    }
    public var id: WebSocketCacheKeyID {
        WebSocketCacheKeyID(store: store, webSocket: webSocket)
    }
    public struct WebSocketCacheKeyID: Hashable {
        let store: WebSocketCache
        let webSocket: WebSocketClient
    }
    public func load(
        context: LoadContext<Value>,
        continuation: LoadContinuation<Value>
    ) {
        let cacheValues = store.values.storage.withLock { $0 }
        continuation.resume(returning: cacheValues)
    }
    @Dependency(\.decode) var decode
    public func subscribe(
        context: LoadContext<Value>,
        subscriber: SharedSubscriber<Value>
    ) -> SharedSubscription {
        subscribeTask.withLock { task in
            task?.cancel()
            task = Task { [webSocket] in
                for await webSocketMessage in await webSocket.received() {
                    if let messageData = webSocketMessage.data(using: .utf8) {
                        do {
                            if let jsonObject = try JSONSerialization
                                .jsonObject(with: messageData, options: [])
                                as? [String: Any],
                                let topic = jsonObject["topic"] as? String,
                                !topic.isEmpty,
                                let symbol = jsonObject["symbol"] as? String,
                                !symbol.isEmpty
                            {
                                var key = "\(topic).\(symbol)"
                                if topic == "mergedDepth",
                                   let params = jsonObject["params"] as? [String: Any],
                                   let scale = params["scale"] as? String {
                                    key = "depth.\(symbol).\(scale)"
                                }
                                if topic == "kline" {
                                    if let params = jsonObject["params"] as? [String: Any],
                                       let klineType = params["klineType"] as? String {
                                        key = "kline_\(klineType).\(symbol)"
                                    }
                                }
                                
                                store.values[key] = messageData
                                subscriber.yield(store.values.storage.withLock { $0 })
                            }
                        } catch {
                            subscriber.yield([:])
                        }
                    }
                }
            }
        }
        return SharedSubscription {}
    }
}


