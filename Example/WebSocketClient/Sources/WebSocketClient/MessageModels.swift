//
//  MessageModels.swift
//  ChiefApp
//
//  Created by stx80501 on 2025/4/10.
//

import Foundation
//import DecimalFormat

public struct EmptyParams: Codable, Sendable, Hashable {}
public struct DepthScale: Codable, Hashable, Sendable {
    let scale: String
}
public struct KLineTime: Codable, Hashable, Sendable {
    let klineType: String
}

public struct WebSocketMessage<
    Params: Codable & Sendable, Data: Decodable & Sendable
>: WebSocketMessageProtocol {

    public var topic: WebSocketClient.Topic
    public var symbol: String
    public var symbolName: String
    public var data: [Data]
    public var sendTime: TimeInterval
    public var params: Params?
    enum CodingKeys: CodingKey {
        case topic
        case symbol
        case symbolName
        case data
        case sendTime
        case params
    }
    public init(
        topic: WebSocketClient.Topic = .realtime,
        symbol: String = "",
        symbolName: String = "",
        data: [Data] = [],
        sendTime: TimeInterval = 0,
        params: Params? = nil
    ) {
        self.topic = topic
        self.symbol = symbol
        self.symbolName = symbolName
        self.data = data
        self.sendTime = sendTime
        self.params = params
    }
    public init(from decoder: any Decoder) throws {
        let container: KeyedDecodingContainer<WebSocketMessage.CodingKeys> =
            try decoder.container(
                keyedBy: WebSocketMessage.CodingKeys.self)
        self.topic =
            try container.decodeIfPresent(
                WebSocketClient.Topic.self, forKey: .topic) ?? .realtime
        self.symbol =
            try container.decodeIfPresent(String.self, forKey: .symbol) ?? ""
        self.symbolName =
            try container.decodeIfPresent(String.self, forKey: .symbolName)
            ?? ""
        self.data =
            (try container.decodeIfPresent(
                [Data].self, forKey: .data)) ?? []
        self.sendTime =
            try container.decodeIfPresent(TimeInterval.self, forKey: .sendTime)
            ?? 0
        self.params = try container.decodeIfPresent(
            Params.self, forKey: .params)
    }
}

//@RuntimeDecimalFormatible
//public struct RealTimeData: Codable, Sendable, Hashable {
//    public static func == (
//        lhs: RealTimeData,
//        rhs: RealTimeData
//    ) -> Bool {
//        lhs.t == rhs.t && lhs.stockNumber == rhs.stockNumber
//            && lhs.sn == rhs.sn && lhs.price == rhs.price
//            && lhs.high == rhs.high && lhs.low == rhs.low
//            && lhs.dealTotal == rhs.dealTotal && lhs.ch == rhs.ch
//            && lhs.tagType == rhs.tagType
//            && lhs.dealQuantity == rhs.dealQuantity
//            && lhs.openPrice == rhs.openPrice
//    }
//
//    var t: TimeInterval
//    var stockNumber: String  // stockNumber
//    var sn: String  //
//    @DecimalFormat(DecimalFormat.Config.price) var price: Double  // price
//    var high: String  // high
//    var low: String  // low
//    var dealTotal: Double  // dealTotal
//    var ch: String
//    var tagType: String
//    @DecimalFormat(DecimalFormat.Config.default.min(0).max(3)) var dealQuantity: Double
//    @DecimalFormat(DecimalFormat.Config.default.min(0).max(3)) var openPrice: Double
//
//    @DecimalFormat(DecimalFormat.Config.price) var changePrice: Double
//    @DecimalFormat(DecimalFormat.Config.percent) var changePricePercent: Double
//    @DecimalFormat(DecimalFormat.Config.price) var mostLowPrice: Double
//    @DecimalFormat(DecimalFormat.Config.price) var mostHighPrice: Double
//
//    enum CodingKeys: String, CodingKey {
//        case t
//        case stockNumber = "s"
//        case sn
//        case price = "c"
//        case high = "h"
//        case low = "l"
//        case dealTotal = "qv"
//        case ch
//        case tagType = "e"
//        case dealQuantity = "v"
//        case openPrice = "o"
//    }
//
//    public init() {
//        self.t = 0
//        self.stockNumber = ""
//        self.sn = ""
//        self.price = 0
//        self.high = "-"
//        self.low = "-"
//        self.dealTotal = 0
//        self.ch = "-"
//        self.tagType = ""
//        self.openPrice = 0
//        self.changePrice = 0
//        self.changePricePercent = 0
//        self.mostLowPrice = 0
//        self.mostHighPrice = 0
//        self.dealQuantity = 0
//    }
//
//    public func hash(into hasher: inout Hasher) {
//        hasher.combine(t)
//        hasher.combine(stockNumber)
//        hasher.combine(sn)
//        hasher.combine(price)
//        hasher.combine(high)
//        hasher.combine(low)
//        hasher.combine(dealTotal)
//        hasher.combine(ch)
//        hasher.combine(tagType)
//        hasher.combine(dealQuantity)
//        hasher.combine(openPrice)
//    }
//
//    public init(from decoder: any Decoder) throws {
//        let container: KeyedDecodingContainer<RealTimeData.CodingKeys> =
//            try decoder.container(
//                keyedBy: RealTimeData.CodingKeys.self)
//        self.t = try container.decode(
//            TimeInterval.self, forKey: RealTimeData.CodingKeys.t)
//        self.stockNumber =
//            try container.decodeIfPresent(
//                String.self,
//                forKey: RealTimeData.CodingKeys.stockNumber) ?? ""
//        self.sn = try container.decode(
//            String.self, forKey: RealTimeData.CodingKeys.sn)
//        self.high =
//            try container.decodeIfPresent(
//                String.self, forKey: RealTimeData.CodingKeys.high)
//            ?? "-"
//        self.low =
//            try container.decodeIfPresent(
//                String.self, forKey: RealTimeData.CodingKeys.low)
//            ?? "-"
//        let openPriceString =
//            try container.decodeIfPresent(
//                String.self,
//                forKey: RealTimeData.CodingKeys.openPrice) ?? ""
//        self.openPrice = Double(openPriceString) ?? 0
//        let dealTotalString =
//            try container.decodeIfPresent(
//                String.self,
//                forKey: RealTimeData.CodingKeys.dealTotal) ?? ""
//        self.dealTotal = Double(dealTotalString) ?? 0
//        self.ch = try container.decode(
//            String.self, forKey: RealTimeData.CodingKeys.ch)
//        let tagTypeValue =
//            (try container.decodeIfPresent(
//                Int.self, forKey: RealTimeData.CodingKeys.tagType))
//            ?? 0
//        self.tagType = Helper.stockCodeToStr(tagTypeValue)
//        self.dealQuantity =
//            try container.decodeIfPresent(
//                String.self,
//                forKey: RealTimeData.CodingKeys.dealQuantity)?.doubleValue ?? 0.0
//
//        let priceString =
//            try container.decodeIfPresent(
//                String.self, forKey: RealTimeData.CodingKeys.price)
//            ?? ""
//        self.price = Double(priceString) ?? 0
//        self.changePrice = price - openPrice
//        self.mostLowPrice = low.doubleValue ?? 0
//        self.mostHighPrice = high.doubleValue ?? 0
//        self.changePricePercent =
//        openPrice == 0 ? 0 : (changePrice / openPrice) * 100
//    }
//}
//
//extension RealTimeData {
//    public func resolveDecimalFormatConfig<Value>(
//        for keyPath: KeyPath<Self, Value>,
//        base baseConfig: DecimalFormat.Config
//    ) -> DecimalFormat.Config {
//        let changePricePrefix = changePrice > 0 ? "+" : nil
//        if keyPath == \.changePricePercent {
//            return DecimalFormat.Config(min: 3, max: 3, prefix: changePricePrefix)
//        }
//        if tagType == "VA" {
//            let stockNumberComponents = stockNumber.components(
//                separatedBy: "/")
//            let stockNumberPrefix =
//                stockNumberComponents.count > 1
//                ? stockNumberComponents[0] : stockNumber
//            switch keyPath {
//            case \.price, \.mostLowPrice, \.mostHighPrice:
//                if stockNumberPrefix.contains("USDT") {
//                    if keyPath == \.price {
//                        return .init(min: 0, max: 5, usesSignificantDigits: true)
//                    }
//                    if keyPath == \.mostLowPrice {
//                        return .init(min: 0, max: 5, usesSignificantDigits: true)
//                    }
//                    if keyPath == \.mostHighPrice {
//                        return .init(min: 0, max: 5, usesSignificantDigits: true)
//                    }
//                } else {
//                    let maxDecimalCounts =
//                        price >= 100 ? 2 : (price >= 1 ? 3 : 4)
//                    let usesSignificantDigits = price < 1
//                    let priceConfig = DecimalFormat.Config(
//                        min: 0, max: maxDecimalCounts,
//                        usesSignificantDigits: usesSignificantDigits)
//                    return priceConfig
//                }
//            case \.changePrice:
//                
//                if abs(changePrice) >= 100 {
//                    return DecimalFormat.Config(
//                        min: 0, max: 2, prefix: changePricePrefix)
//                } else if abs(changePrice) >= 1 {
//                    return DecimalFormat.Config(
//                        min: 0, max: 3, prefix: changePricePrefix)
//                } else {
//                    return DecimalFormat.Config(
//                        min: 0, max: 4, prefix: changePricePrefix)
//                }
//            default: return baseConfig
//            }
//            
//        } else {
//            switch keyPath {
//            case \.price, \.mostLowPrice, \.mostHighPrice:
//                return DecimalFormat.Config(min: 0, max: 3)
//            case \.changePrice:
//                return DecimalFormat.Config(min: 3, max: 3, prefix: changePricePrefix)
//            default: return baseConfig
//            }
//        }
//        return baseConfig
//    }
//}
//
//@RuntimeDecimalFormatible
//struct TradeData: Codable, Hashable, Identifiable {
//    static func ==(lhs: TradeData, rhs: TradeData) -> Bool {
//        lhs.symbol == rhs.symbol &&
//        lhs.direction == rhs.direction &&
//        lhs.time == rhs.time &&
//        lhs.price == rhs.price &&
//        lhs.quantity == rhs.quantity
//    }
//    func hash(into hasher: inout Hasher) {
//        hasher.combine(curId)
//        hasher.combine(symbol)
//        hasher.combine(direction)
//        hasher.combine(time)
//        hasher.combine(price)
//        hasher.combine(quantity)
//    }
//    enum Direction: Int, Codable {
//        case buy = 0
//        case sell = 1
//        
//        var title: String {
//            switch self {
//            case .buy: "+"
//            case .sell: "-"
//            }
//        }
//    }
//    enum CodingKeys: String, CodingKey {
//        case curId
//        case symbol = "s"
//        case direction = "bs"
//        case time = "t"
//        case price = "p"
//        case quantity = "q"
//    }
//    var symbol: String
//    var direction: Direction //买卖方向，0：买，1：卖
//    var time: Int //交易时间
//    var curId: Int
//    var id: Int {
//        hashValue
//    }
//    @DecimalFormat var price: Double
//    @DecimalFormat(DecimalFormat.Config.default.min(5).max(5)) var quantity: Double
//    var symbolWithoutCurrency: String {
//        let array = symbol.components(separatedBy: "/")
//        if array.count > 1 {
//            return array[0]
//        } else {
//            return symbol
//        }
//    }
//    
//    init(from decoder: any Decoder) throws {
//        let container = try decoder.container(keyedBy: CodingKeys.self)
//        self.curId = try container.decode(Int.self, forKey: .curId)
//        self.symbol = try container.decode(String.self, forKey: .symbol)
//        self.direction = try container.decode(TradeData.Direction.self, forKey: .direction)
//        self.time = try container.decode(Int.self, forKey: .time)
//        self.price = try container.decode(String.self, forKey: .price).doubleValue ?? 0.0
//        self.quantity = (try container.decodeIfPresent(String.self, forKey: .quantity)?.doubleValue) ?? 0.0
//    }
//}
//
//extension TradeData {
//    func resolveDecimalFormatConfig<Value>(for keyPath: KeyPath<Self, Value>, base baseConfig: DecimalFormat.Config) -> DecimalFormat.Config {
//        switch keyPath {
//        case \.price:
//            if symbol.contains("USDT") {
//                DecimalFormat.Config(min: 0, max: 5, usesSignificantDigits: true)
//            } else {
//                if price >= 100 {
//                    DecimalFormat.Config(min: 0, max: 2)
//                } else if price >= 1 {
//                    DecimalFormat.Config(min: 0, max: 3)
//                } else {
//                    DecimalFormat.Config(min: 0, max: 4, usesSignificantDigits: true)
//                }
//            }
//        default: baseConfig
//        }
//    }
//}
//
//@RuntimeDecimalFormatible
//struct MergedDepthData: Decodable, Identifiable {
//    enum CodingKeys: String, CodingKey {
//        case symbol = "s"
//        case bid = "b"
//        case ask = "a"
//    }
//    var symbol: String
//    var bid: [CHKDepthChartItem]
//    var ask: [CHKDepthChartItem]
//    
//    var id: String {
//        symbol
//    }
//    
//    init(from decoder: any Decoder) throws {
//        let container = try decoder.container(keyedBy: CodingKeys.self)
//        self.symbol = try container.decode(String.self, forKey: .symbol)
//        let bidData = try container.decode([[String]].self, forKey: .bid)
//        self.bid = bidData.compactMap {
//            let item = CHKDepthChartItem()
//            item.type = .bid
//            item.value = $0.first?.cgFloat() ?? 0
//            item.amount = $0.last?.cgFloat() ?? 0
//            return item
//        }
//        let askData = try container.decode([[String]].self, forKey: .ask)
//        self.ask = askData.compactMap {
//            let item = CHKDepthChartItem()
//            item.type = .ask
//            item.value = $0.first?.cgFloat() ?? 0
//            item.amount = $0.last?.cgFloat() ?? 0
//            return item
//        }
////        self.bidPercent = (totalAmount > 0 ? totalBidAmount / totalAmount : 0) * 100
////        self.askPercent = (totalAmount > 0 ? (1 - totalBidAmount / totalAmount) : 0) * 100
//    }
//    
//    var totalBidAmount: CGFloat {
//        bid.map(\.amount).reduce(0, +)
//    }
//    
//    var totalAskAmount: CGFloat {
//        ask.map(\.amount).reduce(0, +)
//    }
//    
//    var totalAmount: CGFloat {
//        totalBidAmount + totalAskAmount
//    }
//    
//    @DecimalFormat(DecimalFormat.Config.percent) var bidPercent: Double {
//        Double(totalAmount > 0 ? totalBidAmount / totalAmount : 0) * 100
//    }
//    
//    @DecimalFormat(DecimalFormat.Config.percent) var askPercent: Double {
//        Double(totalAmount > 0 ? (1 - totalBidAmount / totalAmount) : 0) * 100
//    }
//    
//    var depth: [CHKDepthChartItem] {
//        bid + ask
//    }
//    
//    var depthXArray: [Double] {
//        if let firstBid = bid.last,
//           let lastBid = bid.first,
//           let firstAsk = ask.first,
//           let lastAsk = ask.last {
//            [firstBid.value, (firstBid.value + lastBid.value) / 2.0, (firstAsk.value + lastBid.value) / 2.0, (firstAsk.value + lastAsk.value) / 2.0, lastAsk.value]
//        } else {
//            [0,0,0,0]
//        }
//    }
//}

public struct KLineChartData: Codable, Equatable {
    enum CodingKeys: String, CodingKey {
        case currentId = "curId"
        case stockNumber = "s"
        case time = "t"
        case highPrice = "h"
        case lowPrice = "l"
        case openPrice = "o"
        case closePrice = "c"
        case volume = "v"
    }
    public var currentId: Int
    public var stockNumber: String
    public var time: Int
    public var highPrice: Double
    public var lowPrice: Double
    public var closePrice: Double
    public var openPrice: Double
    public var volume: Double
    public var timestamp: TimeInterval // with mini seconds
    
//    static func fromLegacyKlineChart(_ data: KlineChartData) -> Self {
//        KLineChartData(currentId: data.time, stockNumber: data.stockNumber, time: data.time, highPrice: data.highPrice, lowPrice: data.lowPrice, closePrice: data.closePrice, openPrice: data.openPrice, volume: data.vol)
//    }
    
    public init(from decoder: any Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        currentId = try container.decodeIfPresent(Int.self, forKey: .currentId) ?? 0
        stockNumber = try container.decodeIfPresent(String.self, forKey: .stockNumber) ?? ""
        time = (try container.decodeIfPresent(Int.self, forKey: .time) ?? 0) / 1000
        timestamp = (try container.decodeIfPresent(TimeInterval.self, forKey: .time)) ?? 0
        highPrice = Double(try container.decodeIfPresent(String.self, forKey: .highPrice) ?? "") ?? 0
        lowPrice = Double(try container.decodeIfPresent(String.self, forKey: .lowPrice) ?? "") ?? 0
        openPrice = Double(try container.decodeIfPresent(String.self, forKey: .openPrice) ?? "") ?? 0
        closePrice = Double(try container.decodeIfPresent(String.self, forKey: .closePrice) ?? "") ?? 0
        volume = Double(try container.decodeIfPresent(String.self, forKey: .volume) ?? "") ?? 0
    }
    
    public init(currentId: Int, stockNumber: String, time: Int, highPrice: Double, lowPrice: Double, closePrice: Double, openPrice: Double, volume: Double) {
        self.currentId = currentId
        self.stockNumber = stockNumber
        self.time = time
        self.highPrice = highPrice
        self.lowPrice = lowPrice
        self.closePrice = closePrice
        self.openPrice = openPrice
        self.volume = volume
        self.timestamp = TimeInterval(time * 1000)
    }
    
    public var amplitude: Double {
        if openPrice > 0 {
            return closePrice - openPrice
        }
        return 0
    }
    
    public var amplitudeRatio: Double {
        if openPrice > 0 {
            return amplitude / openPrice * 100
        }
        return 0
    }
}
