import DependenciesAdditions
import Foundation
import Starscream

public enum WebSocketClientError: Error {
    case invalidConfig(WebSocketClient.Config)
    case invalidMessage([WebSocketClient.Content])
}

public struct WebSocketClient: Sendable, Hashable {
    public static func == (lhs: WebSocketClient, rhs: WebSocketClient) -> Bool {
        lhs.config == rhs.config
    }

    public private(set) var config: WebSocketClient.Config
    public init(
        config: WebSocketClient.Config = .apiTestStx,
        startConnect: @Sendable @escaping () async -> Void,
        disConnect: @Sendable @escaping () async -> Void,
        subscribe: @Sendable @escaping (
            _: WebSocketClient.Topic, _: String, _:String, _: (any Hashable & Encodable & Sendable)?
        ) async -> Void,
        cancelSubscribe: @Sendable @escaping (
            _: WebSocketClient.Topic, _: String, _: String, _: (any Hashable & Encodable & Sendable)?
        ) async -> Void,
        cancelAllSubscribes: @Sendable @escaping (_: WebSocketClient.Topic)
            async -> Void,
        isConnected: @Sendable @escaping () async -> AsyncStream<Bool>,
        received: @Sendable @escaping () async -> AsyncStream<String>
    ) {
        self.config = config
        self.startConnect = startConnect
        self.disConnect = disConnect
        self.subscribe = subscribe
        self.cancelSubscribe = cancelSubscribe
        self.cancelAllSubscribes = cancelAllSubscribes
        self.isConnected = isConnected
        self.received = received
    }
    public var startConnect: @Sendable () async -> Void
    public var disConnect: @Sendable () async -> Void
    public var subscribe:
        @Sendable (
            _ topic: WebSocketClient.Topic,
            _ stock: String,
            _ quote: String,
            _ params: (any Hashable & Encodable & Sendable)?
        ) async -> Void
    public var cancelSubscribe:
        @Sendable (
            _ topic: WebSocketClient.Topic,
            _ stock: String,
            _ quote: String,
            _ params: (any Hashable & Encodable & Sendable)?
        ) async -> Void
    public var cancelAllSubscribes:
        @Sendable (_ topic: WebSocketClient.Topic) async -> Void
    public var isConnected: @Sendable () async -> AsyncStream<Bool>
    public var received: @Sendable () async -> AsyncStream<String>

    public func hash(into hasher: inout Hasher) {
        hasher.combine(config)
    }

}

public enum WebSocketKey: DependencyKey {
    public static let liveValue: WebSocketClient = {
        let actor = DelegateProxy(config: .apiTestStx)
        return WebSocketClient(
            startConnect: { [actor] in
                await actor?.startConnect()
            },
            disConnect: { [actor] in
                await actor?.disConnect()
            },
            subscribe: { [actor] topic, stock, quote, params in
                await actor?.subscribe(topic: topic, stock: stock, quote: quote, params: params)
            },
            cancelSubscribe: { [actor] topic, stock, quote, params in
                await actor?.cancelSubscribe(
                    topic: topic, stock: stock, quote: quote, params: params)
            },
            cancelAllSubscribes: { [actor] topic in
                await actor?.cancelAllSubscribe(topic: topic)
            },
            isConnected: { [actor] in
                if let actor {
                    return await actor.isConnectedStream
                }
                return AsyncStream { continuation in
                    continuation.yield(false)
                }
            },
            received: { [actor] in
                if let actor {
                    return await actor.receivedMessageStream
                }
                return AsyncStream { continuation in
                    continuation.yield("")
                }
            }
        )
    }()
    public static func live(config: WebSocketClient.Config) -> WebSocketClient {
        let actor = DelegateProxy(config: config)
        return WebSocketClient(
            config: config,
            startConnect: { [actor] in
                await actor?.startConnect()
            },
            disConnect: { [actor] in
                await actor?.disConnect()
            },
            subscribe: { [actor] topic, stock, quote, params in
                await actor?.subscribe(
                    topic: topic, stock: stock, quote: quote, params: params)
            },
            cancelSubscribe: { [actor] topic, stock, quote, params in
                await actor?.cancelSubscribe(
                    topic: topic, stock: stock, quote: quote, params: params)
            },
            cancelAllSubscribes: { [actor] topic in
                await actor?.cancelAllSubscribe(topic: topic)
            },
            isConnected: { [actor] in
                if let actor {
                    return await actor.isConnectedStream
                }
                return AsyncStream { continuation in
                    continuation.yield(false)
                }
            },
            received: { [actor] in
                if let actor {
                    return await actor.receivedMessageStream
                }
                return AsyncStream { continuation in
                    continuation.yield("")
                }
            }
        )
    }
    private actor DelegateProxy: @preconcurrency WebSocketDelegate {
        let config: WebSocketClient.Config
        let socket: WebSocket
        private var isConnected = false {
            didSet {
                connectionContinuation.yield(isConnected)
            }
        }
        @Dependency(
            \.logger[
                subsystem: Bundle.main.bundleIdentifier ?? "",
                category: "WebSocket"]) private var logger
        @Dependency(\.encode) var encode
        private var heartbeatTask: Task<Void, Never>?
        private let (connectionStream, connectionContinuation) = AsyncStream<
            Bool
        >.makeStream()
        private let (receivedDataStream, receivedDataContinuation) =
            AsyncStream<String>.makeStream()
        public var isConnectedStream: AsyncStream<Bool> {
            self.connectionStream
        }
        public var receivedMessageStream: AsyncStream<String> {
            self.receivedDataStream
        }
        deinit {
            connectionContinuation.finish()
            receivedDataContinuation.finish()
        }
        init?(config: WebSocketClient.Config) {
            self.config = config
            guard var urlRequest = config.request else {
                @Dependency(
                    \.logger[
                        subsystem: Bundle.main.bundleIdentifier ?? "",
                        category: "WebSocket"]) var logger
                let error = WebSocketClientError.invalidConfig(config)
                logger.error("\(error, privacy: .auto)")
                return nil
            }
            urlRequest.timeoutInterval = config.timeoutInterval
            socket = WebSocket(request: urlRequest)
            socket.delegate = self
        }
        func startConnect() {
            guard !isConnected else {
                return
            }
            logger.debug("StartConnect")
            socket.connect()
            setupHeartBeat()
        }

        func disConnect() {
            socket.disconnect()
        }

        func subscribe(
            topic: WebSocketClient.Topic, stock: String,
            quote: String,
            params: (any Hashable & Encodable & Sendable)?
        ) {
            let content = WebSocketClient.Content.subscribeContent(
                topic, stockSymbol: stock, quote: quote, params: params)
            if let contentJsonString = try? contentJsonString(content) {
                logger.debug("Subscribe: \(contentJsonString, privacy: .auto)")
                socket.write(string: contentJsonString)
            }
        }

        func cancelSubscribe(
            topic: WebSocketClient.Topic, stock: String,
            quote: String,
            params: (any Hashable & Encodable & Sendable)?
        ) {
            let content = WebSocketClient.Content.cancelContent(
                topic, stockSymbol: stock, quote: quote, params: params)
            if let contentJsonString = try? contentJsonString(content) {
                logger.debug(
                    "CancelSubscribe: \(contentJsonString, privacy: .auto)")
                socket.write(string: contentJsonString)
            }
        }

        func cancelAllSubscribe(topic: WebSocketClient.Topic) {
            let content = WebSocketClient.Content.cancellAllContent(topic)
            do {
                let contentData = try encode(content)
                if let contentJsonString = String(
                    data: contentData, encoding: .utf8)
                {
                    logger.debug(
                        "CancelAllSubscribe: \(contentJsonString, privacy: .auto)"
                    )
                    socket.write(string: contentJsonString)
                } else {
                    logger.error(
                        "\(WebSocketClientError.invalidMessage([content]), privacy: .auto)"
                    )
                }
            } catch {
                logger.error(
                    "\(WebSocketClientError.invalidMessage([content]), privacy: .auto)"
                )
            }
        }

        func setupHeartBeat() {
            heartbeatTask?.cancel()
            heartbeatTask = Task { [weak self] in
                guard let self else { return }
                let heartBeatStream = AsyncStream<Void> { continuation in
                    Task {
                        while !Task.isCancelled {
                            try? await Task.sleep(
                                nanoseconds: 30 * 1_000_000_000)
                            continuation.yield(())
                        }
                        continuation.finish()
                    }
                }
                for await _ in heartBeatStream {
                    guard !Task.isCancelled else { break }
                    await self.sendHeartBeat()
                }
            }
        }

        func sendHeartBeat() {
            if isConnected {
                logger.debug("ping")
                socket.write(string: "ping")
            } else {
                startConnect()
            }
        }

        private func contentJsonString(_ content: WebSocketClient.Content)
            throws -> String
        {
            do {
                let contentData = try encode(content)
                if let contentJsonString = String(
                    data: contentData, encoding: .utf8)
                {
                    return contentJsonString
                } else {
                    let error = WebSocketClientError.invalidMessage([content])
                    logger.error("\(error, privacy: .auto)")
                    throw error
                }
            } catch {
                let error = WebSocketClientError.invalidMessage([content])
                logger.error("\(error, privacy: .auto)")
                throw error
            }
        }

        func didReceive(
            event: WebSocketEvent, client: any Starscream.WebSocketClient
        ) {
            switch event {
            case .connected(let dictionary):
                isConnected = true
                logger.debug("Connected: \(dictionary)")
            case .disconnected(let string, let code):
                isConnected = false
                logger.debug("DisConnected: \(string) \(code)")
            case .text(let value):
                logger.debug("Received value: \(value)")
                receivedDataContinuation.yield(value)
            case .binary(let data):
                logger.debug("Received data: \(data)")
            case .pong:
                logger.debug("pong")
            case .ping:
                logger.debug("ping")
            case .error(let error):
                isConnected = false
                logger.error("\(error, privacy: .auto)")
            case .viabilityChanged(let viability):
                logger.debug("ViabilityChanged: \(viability)")
            case .reconnectSuggested(let reconnectSuggested):
                isConnected = false
                logger.debug("ReconnectSuggested: \(reconnectSuggested)")
            case .cancelled:
                isConnected = false
                logger.debug("Cancelled")
            case .peerClosed:
                isConnected = false
                logger.debug("PeerClosed")
            }
        }
    }
}

// MARK: - dependency inject

extension DependencyValues {
    public var webSocket: WebSocketClient {
        get { self[WebSocketKey.self] }
        set { self[WebSocketKey.self] = newValue }
    }
}

// MARK: - Entity
extension WebSocketClient {

    public struct Config: Sendable, CustomStringConvertible, Hashable {
        let host: String
        let path: String
        let timeoutInterval: TimeInterval
        public init(
            host: String, path: String, timeoutInterval: TimeInterval = 30
        ) {
            self.host = host
            self.path = path
            self.timeoutInterval = timeoutInterval
        }
        public static let apiTestStx = Config(
            host: "wss://api-test.stx365.com",
            path: "quote/ws/v1"
        )
        public var request: URLRequest? {
            guard let url = URL(string: host + "/" + path) else {
                return nil
            }
            return URLRequest(url: url)
        }
        public var description: String {
            host + "/" + path + ".timeout: \(timeoutInterval)"
        }
    }
    public struct Content: Encodable, Sendable, CustomStringConvertible,
        Hashable
    {
        let topic: Topic
        let event: Event
        let stockSymbol: String
        let quote: String
        let sendTime: TimeInterval
        let params: (any Hashable & Encodable & Sendable)?

        enum CodingKeys: String, CodingKey {
            case topic
            case event
            case stockSymbol = "symbol"
            case sendTime
            case params
        }

        public init(
            topic: Topic,
            event: Event,
            stockSymbol: String,
            quote: String,
            sendTime: TimeInterval,
            params: (any Hashable & Encodable & Sendable)?
        ) {
            self.topic = topic
            self.event = event
            self.stockSymbol = stockSymbol
            self.quote = quote
            self.sendTime = sendTime
            self.params = params
        }
        public func encode(to encoder: any Encoder) throws {
            var container = encoder.container(keyedBy: CodingKeys.self)
            try container.encode(topic, forKey: .topic)
            try container.encode(event, forKey: .event)
            try container.encode("305.\(stockSymbol)\(quote)", forKey: .stockSymbol)
            try container.encode(sendTime, forKey: .sendTime)
            if let params {
                let paramsEncoder = container.superEncoder(forKey: .params)
                try params.encode(to: paramsEncoder)
            }
        }

        public static func subscribeContent(
            _ topic: Topic,
            stockSymbol: String,
            quote: String,
            params: (any Hashable & Encodable & Sendable)?
        ) -> Self {
            Content(
                topic: topic,
                event: .sub,
                stockSymbol: stockSymbol,
                quote: quote,
                sendTime: Date().timeIntervalSince1970,
                params: params
            )
        }

        public static func cancelContent(
            _ topic: Topic,
            stockSymbol: String,
            quote: String,
            params: (any Hashable & Encodable & Sendable)?
        ) -> Self {
            Content(
                topic: topic,
                event: .cancel,
                stockSymbol: stockSymbol,
                quote: quote,
                sendTime: Date().timeIntervalSince1970,
                params: params
            )
        }

        public static func cancellAllContent(_ topic: Topic) -> Self {
            Content(
                topic: topic,
                event: .cancelAll,
                stockSymbol: "",
                quote: "",
                sendTime: Date().timeIntervalSince1970,
                params: nil
            )
        }

        public var description: String {
            "Topic: \(topic.rawValue) - Event: \(event.rawValue) - Symbol: \(stockSymbol) - Params: \(params.debugDescription)"
        }

        public static func == (lhs: Content, rhs: Content) -> Bool {
            lhs.topic == rhs.topic && lhs.event == rhs.event
                && lhs.stockSymbol == rhs.stockSymbol
                && lhs.sendTime == rhs.sendTime
                && type(of: lhs.params) == type(of: rhs.params)
                && lhs.params?.hashValue == rhs.params?.hashValue
        }
        public func hash(into hasher: inout Hasher) {
            hasher.combine(topic)
            hasher.combine(event)
            hasher.combine(stockSymbol)
            hasher.combine(quote)
            if let params = params {
                hasher.combine(params)
            }
        }
    }

    public enum Topic: Codable, Sendable, RawRepresentable, Hashable {
        case depth
        case trade
        case realtime
        case kline(params: String)

        public func encode(to encoder: Encoder) throws {
            var container = encoder.singleValueContainer()
            try container.encode(rawValue)
        }

        public var rawValue: String {
            switch self {
            case .depth: "depth"
            case .trade: "trade"
            case .realtime: "realtime"
            case .kline(let params): "kline_" + params
            }
        }
        
        public init(from decoder: any Decoder) throws {
            let container = try decoder.singleValueContainer()
            let rawValue = try container.decode(String.self)
            guard let topic = Topic(rawValue: rawValue) else {
                throw DecodingError.dataCorruptedError(
                    in: container,
                    debugDescription: "Invalid Topic rawValue: \(rawValue)"
                )
            }
            self = topic
        }

        public init?(rawValue: String) {
            if rawValue == "mergedDepth" {
                self = .depth
            } else if rawValue == "trade" {
                self = .trade
            } else if rawValue == "realtime" {
                self = .realtime
            } else if rawValue.hasPrefix("kline") {
                let params = String(rawValue.dropFirst("kline".count))
                self = .kline(params: params)
            } else {
                return nil
            }
        }
    }
    public enum Event: String, Encodable, Sendable {
        case sub = "sub"
        case cancel = "cancel"
        case cancelAll = "cancel_all"
    }
}
