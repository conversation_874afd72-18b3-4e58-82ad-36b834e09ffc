<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="tdO-BP-ptx">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="6gv-ML-eck">
            <objects>
                <viewController id="IrV-3S-2cy" customClass="ViewController" customModule="UIKitDemo" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="nZW-Ux-pgS">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="qqD-nI-UkV">
                                <rect key="frame" x="0.0" y="689.66666666666663" width="393" height="94.333333333333371"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="AlI-bD-RDr">
                                        <rect key="frame" x="0.0" y="0.0" width="70.666666666666671" height="94.333333333333329"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="切换模式"/>
                                        <connections>
                                            <action selector="switchTheme:" destination="IrV-3S-2cy" eventType="touchUpInside" id="2VU-CP-79P"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Q57-Af-WKR">
                                        <rect key="frame" x="80.666666666666657" y="0.0" width="70.666666666666657" height="94.333333333333329"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="修改 主副视图显示"/>
                                        <connections>
                                            <action selector="tapBtnAction:" destination="IrV-3S-2cy" eventType="touchUpInside" id="mOW-Ca-IhI"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="0Kc-vJ-rNm">
                                        <rect key="frame" x="161.33333333333334" y="0.0" width="70.333333333333343" height="94.333333333333329"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="添加数据"/>
                                        <connections>
                                            <action selector="addLineData:" destination="IrV-3S-2cy" eventType="touchUpInside" id="L2i-f8-C1h"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="yQf-bc-Ol1">
                                        <rect key="frame" x="241.66666666666666" y="0.0" width="70.666666666666657" height="94.333333333333329"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="MA 指标加"/>
                                        <connections>
                                            <action selector="maIndexLine:" destination="IrV-3S-2cy" eventType="touchUpInside" id="zUH-HL-U3K"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="snw-Cg-C86">
                                        <rect key="frame" x="322.33333333333331" y="0.0" width="70.666666666666686" height="94.333333333333329"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="划线工具"/>
                                        <connections>
                                            <action selector="drawLine:" destination="IrV-3S-2cy" eventType="touchUpInside" id="cZc-Wc-Xn1"/>
                                        </connections>
                                    </button>
                                </subviews>
                            </stackView>
                            <stackView opaque="NO" contentMode="scaleToFill" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="0JK-xc-pGl">
                                <rect key="frame" x="0.0" y="625.33333333333337" width="393" height="54.333333333333371"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="6Df-sg-c5z">
                                        <rect key="frame" x="0.0" y="0.0" width="61.333333333333336" height="54.333333333333336"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="山型"/>
                                        <connections>
                                            <action selector="KlineTypeBtnAction:" destination="IrV-3S-2cy" eventType="touchUpInside" id="voG-i1-wxx"/>
                                            <action selector="timeLineStyle:" destination="07f-DN-fHR" eventType="touchUpInside" id="dlH-9o-OL3"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" tag="1" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="x1c-Qp-pBR">
                                        <rect key="frame" x="66.333333333333329" y="0.0" width="61.333333333333329" height="54.333333333333336"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="蜡烛"/>
                                        <connections>
                                            <action selector="KlineTypeBtnAction:" destination="IrV-3S-2cy" eventType="touchUpInside" id="2AH-ed-faK"/>
                                            <action selector="timeLineStyle:" destination="07f-DN-fHR" eventType="touchUpInside" id="y1p-vh-kLs"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" tag="2" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Phg-K2-ney">
                                        <rect key="frame" x="132.66666666666666" y="0.0" width="61.333333333333343" height="54.333333333333336"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="美国线"/>
                                        <connections>
                                            <action selector="KlineTypeBtnAction:" destination="IrV-3S-2cy" eventType="touchUpInside" id="K6G-Xe-wAW"/>
                                            <action selector="timeLineStyle:" destination="07f-DN-fHR" eventType="touchUpInside" id="fPW-Uv-saP"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" tag="3" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="plH-NF-oUD">
                                        <rect key="frame" x="199" y="0.0" width="61.333333333333314" height="54.333333333333336"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="空心蜡烛"/>
                                        <connections>
                                            <action selector="KlineTypeBtnAction:" destination="IrV-3S-2cy" eventType="touchUpInside" id="e8W-ru-vBZ"/>
                                            <action selector="timeLineStyle:" destination="07f-DN-fHR" eventType="touchUpInside" id="35t-LM-Q9P"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" tag="4" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="shT-72-xpP">
                                        <rect key="frame" x="265.33333333333331" y="0.0" width="61.333333333333314" height="54.333333333333336"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="折线"/>
                                        <connections>
                                            <action selector="KlineTypeBtnAction:" destination="IrV-3S-2cy" eventType="touchUpInside" id="2kr-ym-00g"/>
                                            <action selector="timeLineStyle:" destination="07f-DN-fHR" eventType="touchUpInside" id="t99-fJ-ynO"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" tag="5" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="8WV-DW-ghr">
                                        <rect key="frame" x="331.66666666666669" y="0.0" width="61.333333333333314" height="54.333333333333336"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="五日分时"/>
                                        <connections>
                                            <action selector="KlineTypeBtnAction:" destination="IrV-3S-2cy" eventType="touchUpInside" id="uXW-rx-U3j"/>
                                            <action selector="timeLineStyle:" destination="07f-DN-fHR" eventType="touchUpInside" id="7v4-iG-sQa"/>
                                        </connections>
                                    </button>
                                </subviews>
                            </stackView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="umr-zS-ju0">
                                <rect key="frame" x="296.33333333333331" y="586" width="96.666666666666686" height="34.333333333333371"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="MainView"/>
                                <connections>
                                    <action selector="gotoMainView:" destination="IrV-3S-2cy" eventType="touchUpInside" id="lb8-dm-Unp"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="1kv-LY-0BB">
                                <rect key="frame" x="178" y="586" width="108.33333333333331" height="34.333333333333371"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="重置参考线"/>
                                <connections>
                                    <action selector="resetReferences:" destination="IrV-3S-2cy" eventType="touchUpInside" id="bey-sF-bD4"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="u4K-Dg-2dr"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="qqD-nI-UkV" firstAttribute="leading" secondItem="nZW-Ux-pgS" secondAttribute="leading" id="6bC-4J-6AB"/>
                            <constraint firstItem="0JK-xc-pGl" firstAttribute="top" secondItem="umr-zS-ju0" secondAttribute="bottom" constant="5" id="Fbl-Do-5es"/>
                            <constraint firstItem="0JK-xc-pGl" firstAttribute="leading" secondItem="u4K-Dg-2dr" secondAttribute="leading" id="Lmo-RC-9fA"/>
                            <constraint firstItem="1kv-LY-0BB" firstAttribute="centerY" secondItem="umr-zS-ju0" secondAttribute="centerY" id="M67-BZ-ka3"/>
                            <constraint firstItem="qqD-nI-UkV" firstAttribute="centerX" secondItem="nZW-Ux-pgS" secondAttribute="centerX" id="Rhv-20-oXY"/>
                            <constraint firstItem="qqD-nI-UkV" firstAttribute="top" secondItem="0JK-xc-pGl" secondAttribute="bottom" constant="10" id="V0z-xo-2rY"/>
                            <constraint firstItem="u4K-Dg-2dr" firstAttribute="bottom" secondItem="qqD-nI-UkV" secondAttribute="bottom" id="bOk-rN-aS1"/>
                            <constraint firstItem="umr-zS-ju0" firstAttribute="leading" secondItem="1kv-LY-0BB" secondAttribute="trailing" constant="10" id="iwD-K5-l4G"/>
                            <constraint firstAttribute="trailing" secondItem="0JK-xc-pGl" secondAttribute="trailing" id="qI6-X0-ZbZ"/>
                            <constraint firstItem="u4K-Dg-2dr" firstAttribute="trailing" secondItem="umr-zS-ju0" secondAttribute="trailing" id="xA9-6w-yRN"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="QTN-ST-87d"/>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="igf-re-OSx" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1060.3053435114502" y="4.9295774647887329"/>
        </scene>
        <!--Second View Controller-->
        <scene sceneID="6Fc-jP-FlF">
            <objects>
                <viewController restorationIdentifier="SecondViewController" storyboardIdentifier="SecondViewController" id="07f-DN-fHR" customClass="SecondViewController" customModule="UIKitDemo" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="b22-6P-jEs">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <button opaque="NO" tag="1" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="oJM-rZ-bwa">
                                <rect key="frame" x="75" y="142" width="48" height="34"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" title="EMA"/>
                                <connections>
                                    <action selector="mainBtnTap:" destination="07f-DN-fHR" eventType="touchUpInside" id="N30-lw-itK"/>
                                </connections>
                            </button>
                            <button opaque="NO" tag="2" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="gB6-on-DmO">
                                <rect key="frame" x="128" y="141" width="48" height="34"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" title="WMA"/>
                                <connections>
                                    <action selector="mainBtnTap:" destination="07f-DN-fHR" eventType="touchUpInside" id="1jp-Fg-cDj"/>
                                </connections>
                            </button>
                            <button opaque="NO" tag="3" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ACQ-10-omO">
                                <rect key="frame" x="189" y="142" width="41" height="30"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" title="BOOL"/>
                                <connections>
                                    <action selector="mainBtnTap:" destination="07f-DN-fHR" eventType="touchUpInside" id="339-cT-KuA"/>
                                </connections>
                            </button>
                            <button opaque="NO" tag="4" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="EV2-Pd-jOH">
                                <rect key="frame" x="255" y="141" width="48" height="34"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" title="Close"/>
                                <connections>
                                    <action selector="mainBtnTap:" destination="07f-DN-fHR" eventType="touchUpInside" id="LKN-Hh-6Ig"/>
                                </connections>
                            </button>
                            <button opaque="NO" tag="5" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="3av-7G-Asa">
                                <rect key="frame" x="311" y="142" width="48" height="34"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" title="SAR"/>
                                <connections>
                                    <action selector="mainBtnTap:" destination="07f-DN-fHR" eventType="touchUpInside" id="SXL-QL-Jud"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="eRI-fF-ssm">
                                <rect key="frame" x="39" y="279" width="57" height="35"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="VOL"/>
                                <connections>
                                    <action selector="subBtnTap:" destination="07f-DN-fHR" eventType="touchUpInside" id="ag1-Cu-7rE"/>
                                </connections>
                            </button>
                            <button opaque="NO" tag="1" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="lzO-SP-2KJ">
                                <rect key="frame" x="97" y="279" width="73" height="35"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="MACD"/>
                                <connections>
                                    <action selector="subBtnTap:" destination="07f-DN-fHR" eventType="touchUpInside" id="eq9-Ou-RSZ"/>
                                </connections>
                            </button>
                            <button opaque="NO" tag="2" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bZA-iR-m0C">
                                <rect key="frame" x="168" y="279" width="56" height="35"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="KDJ"/>
                                <connections>
                                    <action selector="subBtnTap:" destination="07f-DN-fHR" eventType="touchUpInside" id="UXP-QH-gdA"/>
                                </connections>
                            </button>
                            <button opaque="NO" tag="3" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Fsd-aP-2eZ">
                                <rect key="frame" x="246" y="279" width="50" height="35"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="RSI"/>
                                <connections>
                                    <action selector="subBtnTap:" destination="07f-DN-fHR" eventType="touchUpInside" id="yXe-cN-mkD"/>
                                </connections>
                            </button>
                            <button opaque="NO" tag="4" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="6zE-3C-MVK">
                                <rect key="frame" x="42" y="326" width="51" height="35"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="WR"/>
                                <connections>
                                    <action selector="subBtnTap:" destination="07f-DN-fHR" eventType="touchUpInside" id="XDX-PK-tZO"/>
                                </connections>
                            </button>
                            <button opaque="NO" tag="5" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="LVY-4G-4oy">
                                <rect key="frame" x="104" y="326" width="59" height="35"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="OBV"/>
                                <connections>
                                    <action selector="subBtnTap:" destination="07f-DN-fHR" eventType="touchUpInside" id="hmw-Le-nRq"/>
                                </connections>
                            </button>
                            <button opaque="NO" tag="6" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="cbF-pF-SuG">
                                <rect key="frame" x="166" y="326" width="60" height="35"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="ROC"/>
                                <connections>
                                    <action selector="subBtnTap:" destination="07f-DN-fHR" eventType="touchUpInside" id="Le5-ri-fRg"/>
                                </connections>
                            </button>
                            <button opaque="NO" tag="7" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ln2-Jj-mPr">
                                <rect key="frame" x="245" y="326" width="52" height="35"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="CCI"/>
                                <connections>
                                    <action selector="subBtnTap:" destination="07f-DN-fHR" eventType="touchUpInside" id="wjj-3H-vlm"/>
                                </connections>
                            </button>
                            <button opaque="NO" tag="8" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="6Nt-Zb-opg">
                                <rect key="frame" x="50" y="369" width="94" height="35"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="StochRSI"/>
                                <connections>
                                    <action selector="subBtnTap:" destination="07f-DN-fHR" eventType="touchUpInside" id="mLx-DA-QGQ"/>
                                </connections>
                            </button>
                            <button opaque="NO" tag="9" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="6gd-Um-pQD">
                                <rect key="frame" x="182" y="369" width="61" height="35"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="TRIX"/>
                                <connections>
                                    <action selector="subBtnTap:" destination="07f-DN-fHR" eventType="touchUpInside" id="Zg3-hR-CMJ"/>
                                </connections>
                            </button>
                            <button opaque="NO" tag="10" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="1Dc-T5-ADd">
                                <rect key="frame" x="248" y="369" width="55" height="35"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="DMI"/>
                                <connections>
                                    <action selector="subBtnTap:" destination="07f-DN-fHR" eventType="touchUpInside" id="CNu-wq-uy6"/>
                                </connections>
                            </button>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="主视图" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Erc-CV-c8D">
                                <rect key="frame" x="50" y="92" width="51" height="21"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="副视图" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LLP-4S-lsH">
                                <rect key="frame" x="50" y="228" width="51" height="21"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="有地方限制做多显示4个" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="YKd-Nt-WIM">
                                <rect key="frame" x="128" y="228" width="184" height="21"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="LhQ-XG-cXU">
                                <rect key="frame" x="25" y="142" width="48" height="34"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" title="MA"/>
                                <connections>
                                    <action selector="mainBtnTap:" destination="07f-DN-fHR" eventType="touchUpInside" id="0Nn-Vd-Reg"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="usR-i8-bKq">
                                <rect key="frame" x="30" y="412" width="79" height="35"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="English"/>
                                <connections>
                                    <action selector="swiitchLanguage:" destination="07f-DN-fHR" eventType="touchUpInside" id="VPg-nx-cud"/>
                                </connections>
                            </button>
                            <button opaque="NO" tag="1" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="3mN-9s-w0m">
                                <rect key="frame" x="109" y="412" width="92" height="35"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="简体中文"/>
                                <connections>
                                    <action selector="swiitchLanguage:" destination="07f-DN-fHR" eventType="touchUpInside" id="t3Z-o7-PYe"/>
                                </connections>
                            </button>
                            <button hidden="YES" opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="o1Z-Au-vD2">
                                <rect key="frame" x="214" y="412" width="92" height="35"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="繁體中文"/>
                                <connections>
                                    <action selector="swiitchLanguage:" destination="07f-DN-fHR" eventType="touchUpInside" id="rZC-1Q-hZQ"/>
                                </connections>
                            </button>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="aCe-JQ-gTX">
                                <rect key="frame" x="81" y="472" width="42" height="21"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" on="YES" translatesAutoresizingMaskIntoConstraints="NO" id="DCT-A7-hlq">
                                <rect key="frame" x="270" y="468" width="49" height="31"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <connections>
                                    <action selector="displayIndexValue:" destination="07f-DN-fHR" eventType="valueChanged" id="8cP-dz-xMe"/>
                                </connections>
                            </switch>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="显示指标：" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bGd-67-rGx">
                                <rect key="frame" x="182" y="473" width="76" height="21"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="价格位置" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jld-i3-X5k">
                                <rect key="frame" x="50" y="581" width="84.333333333333258" height="34.333333333333258"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="hJf-24-oUI">
                                <rect key="frame" x="144" y="581" width="71" height="35"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="left 10"/>
                                <connections>
                                    <action selector="pricePosition:" destination="07f-DN-fHR" eventType="touchUpInside" id="Cgm-NM-OwC"/>
                                </connections>
                            </button>
                            <button opaque="NO" tag="1" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xpv-xs-d7k">
                                <rect key="frame" x="220" y="581" width="82" height="35"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="right 10"/>
                                <connections>
                                    <action selector="pricePosition:" destination="07f-DN-fHR" eventType="touchUpInside" id="VDD-Qt-6jg"/>
                                </connections>
                            </button>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="时间轴位置" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cXC-km-gsy">
                                <rect key="frame" x="50" y="516" width="84.333333333333258" height="34.333333333333258"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="GSH-DZ-xb6">
                                <rect key="frame" x="149" y="515" width="60.666666666666671" height="34.333333333333336"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="Main"/>
                                <connections>
                                    <action selector="timeViewPosition:" destination="07f-DN-fHR" eventType="touchUpInside" id="sd9-hr-2iE"/>
                                </connections>
                            </button>
                            <button opaque="NO" tag="1" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="dFQ-RB-bmR">
                                <rect key="frame" x="223" y="516" width="54" height="34.333333333333258"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="Sub"/>
                                <connections>
                                    <action selector="timeViewPosition:" destination="07f-DN-fHR" eventType="touchUpInside" id="0WH-uv-UXi"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="4gg-z5-rB4"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    </view>
                    <connections>
                        <outlet property="displayIndexValueSwitchBtn" destination="DCT-A7-hlq" id="iTs-F3-Jo1"/>
                        <outlet property="testLab" destination="aCe-JQ-gTX" id="wbB-ez-NxJ"/>
                        <outletCollection property="subBtns" destination="eRI-fF-ssm" collectionClass="NSMutableArray" id="wHP-7T-Ji6"/>
                        <outletCollection property="subBtns" destination="lzO-SP-2KJ" collectionClass="NSMutableArray" id="irm-IK-gIf"/>
                        <outletCollection property="subBtns" destination="bZA-iR-m0C" collectionClass="NSMutableArray" id="pRd-Gd-NPG"/>
                        <outletCollection property="subBtns" destination="Fsd-aP-2eZ" collectionClass="NSMutableArray" id="46X-iV-Nif"/>
                        <outletCollection property="subBtns" destination="6zE-3C-MVK" collectionClass="NSMutableArray" id="fKv-sH-78E"/>
                        <outletCollection property="subBtns" destination="LVY-4G-4oy" collectionClass="NSMutableArray" id="M69-YA-THq"/>
                        <outletCollection property="subBtns" destination="cbF-pF-SuG" collectionClass="NSMutableArray" id="ae0-yU-jFd"/>
                        <outletCollection property="subBtns" destination="ln2-Jj-mPr" collectionClass="NSMutableArray" id="iex-dA-NZM"/>
                        <outletCollection property="subBtns" destination="6Nt-Zb-opg" collectionClass="NSMutableArray" id="VGl-zf-cnO"/>
                        <outletCollection property="subBtns" destination="6gd-Um-pQD" collectionClass="NSMutableArray" id="DqQ-Bl-4ZS"/>
                        <outletCollection property="subBtns" destination="1Dc-T5-ADd" collectionClass="NSMutableArray" id="e7J-23-G1h"/>
                        <outletCollection property="mainBtns" destination="LhQ-XG-cXU" collectionClass="NSMutableArray" id="PKk-VF-Tjj"/>
                        <outletCollection property="mainBtns" destination="oJM-rZ-bwa" collectionClass="NSMutableArray" id="LVG-SD-TcC"/>
                        <outletCollection property="mainBtns" destination="ACQ-10-omO" collectionClass="NSMutableArray" id="Xk5-bf-rJo"/>
                        <outletCollection property="mainBtns" destination="EV2-Pd-jOH" collectionClass="NSMutableArray" id="pgn-eC-sBF"/>
                        <outletCollection property="mainBtns" destination="3av-7G-Asa" collectionClass="NSMutableArray" id="KOH-bj-5mE"/>
                        <outletCollection property="mainBtns" destination="gB6-on-DmO" collectionClass="NSMutableArray" id="4Ge-Pm-0Ku"/>
                        <outletCollection property="timeBtns" destination="GSH-DZ-xb6" collectionClass="NSMutableArray" id="T7f-cQ-ScW"/>
                        <outletCollection property="timeBtns" destination="dFQ-RB-bmR" collectionClass="NSMutableArray" id="5Rc-8B-se3"/>
                        <outletCollection property="pricePositionBtns" destination="hJf-24-oUI" collectionClass="NSMutableArray" id="qca-Tv-JKR"/>
                        <outletCollection property="pricePositionBtns" destination="xpv-xs-d7k" collectionClass="NSMutableArray" id="DCf-B9-Bga"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="PT1-mh-tge" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1799" y="11"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="Eb5-qz-vny">
            <objects>
                <navigationController automaticallyAdjustsScrollViewInsets="NO" id="tdO-BP-ptx" sceneMemberID="viewController">
                    <toolbarItems/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" id="m6J-Rr-dt5">
                        <rect key="frame" x="0.0" y="118" width="393" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <nil name="viewControllers"/>
                    <connections>
                        <segue destination="IrV-3S-2cy" kind="relationship" relationship="rootViewController" id="I2m-li-EHS"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Ul4-Zx-6Im" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="135.1145038167939" y="4.9295774647887329"/>
        </scene>
    </scenes>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
