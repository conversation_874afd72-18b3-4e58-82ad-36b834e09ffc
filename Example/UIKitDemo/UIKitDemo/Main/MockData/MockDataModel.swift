//
//  MockDataModel.swift
//  KLineDemo
//
//  Created by <PERSON> on 2025/3/20.
//

import Foundation
import KLineLib
public struct MockDataModel : Codable {
    
    let ch : String?
    let data : [ServerData]?
    let status : String?
    let ts : Int?
    
    
    enum CodingKeys: String, CodingKey {
        case ch = "ch"
        case data = "data"
        case status = "status"
        case ts = "ts"
    }
    public init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        ch = try values.decodeIfPresent(String.self, forKey: .ch)
        data = try values.decodeIfPresent([ServerData].self, forKey: .data)
        status = try values.decodeIfPresent(String.self, forKey: .status)
        ts = try values.decodeIfPresent(Int.self, forKey: .ts)
    }
    public static var allList:[KLineModel] = []
    public static var beforeList:[KLineModel] = []
    public static var afterList:[KLineModel] = []
    //MARK: Michael: 临时使用
    public static func createMockData()->[KLineModel]{
        var list:[KLineModel] = []
        guard let filePath = Bundle.main.path(forResource: "MockData_01", ofType: "json")
        else { return []}
        guard let data = NSData(contentsOfFile: filePath) else{return []}
        guard let servicesModel = try? JSONDecoder().decode(MockDataModel.self, from: data as Data),let dataList = servicesModel.data else{return []}
        
        for i in 0..<dataList.count{
            let serviceData = dataList[i]
            let model = KLineModel()
            model.open = serviceData.open ?? 0.0
            model.close = serviceData.close ?? 0.0
            model.low = serviceData.low ?? 0.0
            model.high = serviceData.high ?? 0.0
            model.amount = serviceData.amount ?? 0.0
            model.volume = serviceData.vol ?? 0.0
            model.timestamp = 1742953526000.0 + Double(i * 60 * 1000)
            list.append(model)
        }
        allList = list
        beforeList = Array(allList.prefix(900))
        afterList = Array(allList.suffix(1000))
//        return Array(list[901..<950])
        return list
        //return randomMockData()
        
    }
    public static func loadingBeforeData(count:Int = 100)->[KLineModel]{
        guard beforeList.count > 0 ,beforeList.count >= count else {return []}
        let tempList = Array(beforeList.reversed())
        beforeList.removeLast(count)
        return Array(tempList[0..<count]).reversed()
    }
    public static func loadingAfterData(count:Int = 100)->[KLineModel]{
        guard afterList.count > 0 else {return []}
        let tempList = afterList
        afterList.removeFirst(count)
        return Array(tempList[0..<count])
    }
    public static func randomMockData(index:Int)->[KLineModel]{
        var list:[KLineModel] = []
        guard let filePath = Bundle.main.path(forResource: "MockData_01", ofType: "json")
        else { return []}
        guard let data = NSData(contentsOfFile: filePath) else{return []}
        guard let servicesModel = try? JSONDecoder().decode(MockDataModel.self, from: data as Data),let dataList = servicesModel.data else{return []}
        
        let serviceData = dataList.randomElement()!
        let model = KLineModel()
        model.open = serviceData.open ?? 0.0
        model.close = serviceData.close ?? 0.0
        model.low = serviceData.low ?? 0.0
        model.high = serviceData.high ?? 0.0
        model.amount = serviceData.amount ?? 0.0
        model.timestamp = 1742953526000.0 + Double(index * 1000)
        model.isShowTime = index % 60 == 0
        model.isShowTime = true
        list.append(model)
        
        return list
    }
}


public struct ServerData : Codable {
    
    let amount : Double?
    let close : Double?
    let count : Int?
    let high : Double?
    let id : Int?
    let low : Double?
    let open : Double?
    let vol : Double?
    
    
    enum CodingKeys: String, CodingKey {
        case amount = "amount"
        case close = "close"
        case count = "count"
        case high = "high"
        case id = "id"
        case low = "low"
        case open = "open"
        case vol = "vol"
    }
    public init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        amount = try values.decodeIfPresent(Double.self, forKey: .amount)
        close = try values.decodeIfPresent(Double.self, forKey: .close)
        count = try values.decodeIfPresent(Int.self, forKey: .count)
        high = try values.decodeIfPresent(Double.self, forKey: .high)
        id = try values.decodeIfPresent(Int.self, forKey: .id)
        low = try values.decodeIfPresent(Double.self, forKey: .low)
        open = try values.decodeIfPresent(Double.self, forKey: .open)
        vol = try values.decodeIfPresent(Double.self, forKey: .vol)
    }
    
    
}
