//
//  ChiefMockData.swift
//  UIKitDemo
//
//  Created by <PERSON> on 2025/4/14.
//

import Foundation
import KLineLib
struct ChiefMockData{
    enum MockData:String{
        case def = "ChiefMocData"
        case oneDay = "ChiefMocData_oneDay"
        case fiveDay = "ChiefMocData_fiveDay"
        case fiveDayv2 = "ChiefMocData_fiveDayV2"
    }
    public static var allList:[KLineModel] = []
    public static var beforeList:[KLineModel] = []
    public static var afterList:[KLineModel] = []
    //MARK: Michael: 临时使用
    public static func createMockData()->[KLineModel]{
        var list:[KLineModel] = []
        guard let filePath = Bundle.main.path(forResource: MockData.fiveDayv2.rawValue, ofType: "json")
        else { return []}
        guard let data = NSData(contentsOfFile: filePath) else{return []}
        guard let servicesModel = try? JSONDecoder().decode(ChiefModel.self, from: data as Data),
              let dataList = servicesModel.records else{return []}
        
        for i in 0..<dataList.count{
            let serviceData = dataList[i]
            let model = KLineModel()
            model.open = serviceData.o ?? 0.0
            model.high = serviceData.h ?? 0.0
            model.low = serviceData.l ?? 0.0
            model.close = serviceData.c ?? 0.0
            model.volume = serviceData.v ?? 0.0
            model.timestamp = serviceData.timestamp ?? 0
            list.append(model)
        }
        allList = list
        beforeList = Array(allList.prefix(150))
        afterList = Array(allList.suffix(63))
//        return Array(list[0..<50])
        return list
        //return randomMockData()
        
    }
    
    
    public static func loadingBeforeData(count:Int = 100)->[KLineModel]{
        guard beforeList.count > 0 ,beforeList.count >= count else {return []}
        let tempList = Array(beforeList.reversed())
        beforeList.removeLast(count)
        return Array(tempList[0..<count]).reversed()
    }
    public static func loadingAfterData(count:Int = 100)->[KLineModel]{
        guard afterList.count > 0 ,afterList.count >= count else {return []}
        let tempList = afterList
        afterList.removeFirst(count)
        return Array(tempList[0..<count])
    }
    
}

struct ChiefModel : Codable {

    let records : [ChiefRecord]?


    enum CodingKeys: String, CodingKey {
        case records = "Records"
    }
    init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        records = try values.decodeIfPresent([ChiefRecord].self, forKey: .records)
    }


}



struct ChiefRecord : Codable {

    let c : Double?
    let cP : Double?
    let h : Double?
    let l : Double?
    let o : Double?
    let t : Double?
    let tI : String?
    let v : Double?
    
    let timestamp:TimeInterval?

    enum CodingKeys: String, CodingKey {
        case c = "C"
        case cP = "CP"
        case h = "H"
        case l = "L"
        case o = "O"
        case t = "T"
        case tI = "TI"
        case v = "V"
    }
    init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        c = try values.decodeIfPresent(Double.self, forKey: .c)
        cP = try values.decodeIfPresent(Double.self, forKey: .cP)
        h = try values.decodeIfPresent(Double.self, forKey: .h)
        l = try values.decodeIfPresent(Double.self, forKey: .l)
        o = try values.decodeIfPresent(Double.self, forKey: .o)
        t = try values.decodeIfPresent(Double.self, forKey: .t)
        tI = try values.decodeIfPresent(String.self, forKey: .tI)
        self.timestamp = tI.convertToTimestamp()! * 1000
        v = try values.decodeIfPresent(Double.self, forKey: .v)
    }


}
    
extension String?{
    func convertToTimestamp() -> TimeInterval? {
        let dateFormatter = DateFormatter()
        // 设置日期格式
        dateFormatter.dateFormat = "yyyyMMddHHmmss"
        // 设置时区（可选，默认使用系统时区）
        dateFormatter.timeZone = TimeZone.current
        // 设置地区（可选，确保格式一致性）
        dateFormatter.locale = Locale.current
        
        guard let date = dateFormatter.date(from: self ?? "") else {
            print("日期格式不正确")
            return nil
        }
        
        return date.timeIntervalSince1970
    }
}
