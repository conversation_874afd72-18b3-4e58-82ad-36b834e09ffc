//
//  OnlyKLineMainViewController.swift
//  UseKlineToolsDemo
//
//  Created by <PERSON> on 2025/4/8.
//

import UIKit

class OnlyKLineMainViewController: UIViewController {
    lazy var mainView: OnlyKLineMainView = {
        let v = OnlyKLineMainView(frame: .zero)
        
        return v
    }()
    override func viewDidLoad() {
        super.viewDidLoad()
        self.view.backgroundColor = .white
        
        setUpUI()

    }
    
    private func setUpUI(){
        self.view.addSubview(mainView)
        mainView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(100)
            make.left.right.equalToSuperview()
            make.height.equalTo(200)
        }
    }

    /*
    // MARK: - Navigation

    // In a storyboard-based application, you will often want to do a little preparation before navigation
    override func prepare(for segue: UIStoryboardSegue, sender: Any?) {
        // Get the new view controller using segue.destination.
        // Pass the selected object to the new view controller.
    }
    */

}
