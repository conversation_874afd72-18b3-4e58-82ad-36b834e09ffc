//
//  ViewController.swift
//  UseKlineToolsDemo
//
//  Created by <PERSON> on 2025/3/31.
//

import UIKit
import SnapKit
import KLineLib
import SwiftDate
class ViewController: UIViewController {

    lazy var kLineView: KLineView = {
        let kLineView = KLineView(delegate: self)
        kLineView.delegate = self
        kLineView.layer.borderWidth = 1
        kLineView.layer.borderColor = KLineConfig.themeManager.dividerColor().withAlphaComponent(0.5).cgColor
        kLineView.tapedBlock = { [weak self] model in
//            self?.infoView.updateData(kLineModel: model)
            //print("click model is \(model.maDictionary)")
        }
        
        kLineView.hiddenInfoViewBlock = { [weak self] isHidden in
//            if KLineConfig.tapType == .top {
//                self?.infoView.isHidden = isHidden
//                guard let self = self else { return }
//                self.view.bringSubviewToFront(self.infoView)
//            } else {
//                self?.infoView.isHidden = true
//            }
        }
        return kLineView
    }()
    /// 画线工具
    lazy var drawToolView: KLineDrawToolView = {
        let v = KLineDrawToolView()
        v.delegate = self
        return v
    }()
    /// 画线设置功能
    lazy var drawSettingView: KLineDrawSettingView = {
        let v = KLineDrawSettingView()
        v.isUserInteractionEnabled = true
        v.addGestureRecognizer(panGesture)
        return v
    }()
    
    /// 画线顶部提示
    lazy var headerTipsV: KLineDrawHeaderTipsView = {
        let v = KLineDrawHeaderTipsView()
        
        return v
    }()
    
    /// 画线中部提示
    lazy var centerTipsV: KLineDrawCenterTipsView = {
        let v = KLineDrawCenterTipsView()
        
        return v
    }()
    /// 自定义 info View Step:1
    public lazy var popView: KLineGesturePopView = {
        let v = KLineGesturePopView()
        v.isHidden = true
        return v
    }()
    
    lazy var panGesture: UIPanGestureRecognizer = {
        let gesture = UIPanGestureRecognizer(target: self, action: #selector(draggedView(_:)))
        return gesture
    }()
    
    override var supportedInterfaceOrientations: UIInterfaceOrientationMask {
        return .all
    }
    override var shouldAutorotate: Bool {
        return true
    }
    
    override var prefersStatusBarHidden: Bool {
        return true
    }
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .white
        //MARK: Michael: 自定义网格中的垂直线
        var model = CustomGridModel()
//        model.keyPoints = ["2025-04-16 08:00:00".toDate()!.date,
//                           "2025-04-16 10:00:00".toDate()!.date,
//                           "2025-04-16 13:00:00".toDate()!.date,
//                           "2025-04-16 14:00:00".toDate()!.date,]
//        let start = "2025-04-16 10:00:00".toDate()?.date
//        let end = "2025-04-16 12:00:00".toDate()?.date
        
        model.keyPoints = [
            Date(year: 2025, month: 4, day: 22, hour: 9, minute: 30),
            Date(year: 2025, month: 4, day: 22, hour: 10, minute: 30),
            Date(year: 2025, month: 4, day: 22, hour: 11, minute: 30),
            Date(year: 2025, month: 4, day: 22, hour: 12, minute: 30),
            Date(year: 2025, month: 4, day: 22, hour: 13, minute: 00),
            Date(year: 2025, month: 4, day: 22, hour: 13, minute: 30),
            
        ]//Array(0..<6).map({Date() - $0.hours}).sorted(by: {$0 < $1})
        model.ignorePoints = [
            
            (Date(year: 2025, month: 4, day: 22, hour: 10, minute: 30),
             Date(year: 2025, month: 4, day: 22, hour: 12, minute: 30))
        ]
        
        //[(model.keyPoints[2],model.keyPoints[3])]
        //MARK: Michael: 需要隐藏的时间轴下标
//        model.needHiddenIndexs = [2,4]
        model.dateFormat = "HH:mm"
//        KLineConfig.customGridModel = model
        
        KLineConfig.kLineType = .professional
        KLineConfig.type = .kline
        //MARK: Michael: 是否将所以数据全部画在当前可视区域
//        KLineConfig.displayAllData = true
        //MARK: Michael: 设置当前可视区域预计数据大概条数（越接近实际数量越好）
//        KLineConfig.expectedTotalDataCount = 200
        //MARK: Michael: 设置时间轴位置
        KLineConfig.kLineTimeViewWithBottomView = .subViewBottom
        //MARK: Michael: K线图中主视图中price View 位置设置
        KLineConfig.pricePosition = .left(offset: 10)
        //MARK: Michael: 是否将当前选中的指标值显示到对应的视图上
        KLineConfig.displayindexValueOnView = true
        //MARK: Michael: 每个副视图的头部视图高度
        KLineConfig.subVeiwHeaderHeight = 0.0
        //MARK: Michael: 每个副视图高度
        KLineConfig.kLineSubViewHeight = 50
        KLineConfig.latestPriceColorWithPreviousDay = true
        KLineConfig.references = [
            SubReferenceLineModel(type: .rsi, maxValue: 100, minValue: 0.0, referenceList: [20,50,80])
        ]
        
        
//        let tradeModel = testModel(ct_isContract: true, ct_tradingPair: "哈哈")
        //MARK: Michael: KLineManager.shared.tradeModel 需在 kLineView 初始化前设置 否则会不生效
//        KLineManager.shared.tradeModel = tradeModel
        //MARK: Michael: 读取历史划线  需在 kLineView 初始化前设置 否则会不生效(在MainView 中的init 中在获取 数据之前)
        KLineDrawViewModel.shared.updateDrawSymbol("哈哈")
        setUpKLineDrawShape()
        setupUI()
        setKlineViewData()
        updatelastData()
        /*
        let models = MockDataModel.createMockData()
        ChiefCalculateTools.calculateMainSMAOptimizeds(klineModes: models, periods: [5,10,30,60])
        
        ChiefCalculateTools.calculateSubSMAOptimizeds(klineModes: models, periods: [5,10,30,60])
        
        ChiefCalculateTools.calculateEMAs(klineModes: models, periods: [6,12,20])
         */
      let fpsLabel = WXFPSLabel()
      self.view.addSubview(fpsLabel)
    }
    
    private func setupUI() {
        // 在这里添加K线图相关的UI组件
        
        /// 自定义颜色测试
        KLineConfig.themeManager =  TestThemeColor()
        
        self.view.addSubview(kLineView)
        kLineView.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.right.equalToSuperview().offset(-50)
            make.top.equalToSuperview().offset(100)
            make.height.equalTo(550)
        }

        professionalKlineShowDarwView()
        //
        self.view.addSubview(headerTipsV)
        headerTipsV.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(kLineView)
        }
        //
        self.view.addSubview(self.centerTipsV)
        self.centerTipsV.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        /// 自定义 info View Step:2
        //MARK: Michael: 将需要显示的info View 添加到 professionalView
        self.kLineView.professionalView.addSubview(popView)
        popView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.bottom.equalTo(0).priority(999)
        }
        
        //MARK: Michael: 只是为了让Demo 进来就可以显示十字 不用看这段
        drawSettingView.isHidden.toggle()
        kLineView.showDrawTool.toggle()
    }
    
    /// 初始化数据
    func setKlineViewData(){
        
        let datas = MockDataModel.createMockData()
        /**/
        let b = KlineOrderModel()
        b.content = "B"
        b.price = 42.25
        b.bgColor = KLineConfig.themeManager.candleDeclineColor()
        
        let s = KlineOrderModel()
        s.price = 43
        s.content = "S"
        s.bgColor = KLineConfig.themeManager.candleRiseColor()
        
        
        let l = KlineOrderModel()
        l.price = 42.75
        l.content = "T"
        l.bgColor = KLineConfig.themeManager.orangeColor()
        l.shape = .square
        let orderList:[KlineOrderModel] = [
            b,s,l
            
        ]
//        datas[datas.count - 2].orderModels = orderList
//        datas[datas.count - 12].orderModels = [b]
//        datas[datas.count - 14].orderModels = [s]
//        datas[datas.count - 18].orderModels = [l]
        
        self.kLineView.setKLineModels(datas, true)
    
    }
  func updatelastData(){
    // 创建一个每秒重复执行的定时器
    var up = false
    var count = 0
    let _ = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { timer in
        
      guard count < 5 else{return}
      let datas = MockDataModel.createMockData()
      up = Bool.random()
      guard let last = datas.last else { return }
//      last.open = CGFloat.random(in: 83813.97...84913.97)
//      last.close = CGFloat.random(in: 83813.97...83913.97)
      last.timestamp = Date().timeIntervalSince1970
      if up{
        last.close = last.open + Double.random(in: 0...50)
        
      }else{
        last.close = last.open - Double.random(in: 0...50)
      }
      self.kLineView.updateLastTimeKLineModels(lastTimeModel: last)
      count += 1
    }
  }
    
/*
 private func randmoModel(orderType:KLineContractOrder)->[KLineContractOrderModel]{
     let tempModel = KLineContractOrderModel()
     tempModel.updateTime = 1742955496000//Int64.random(in: 1742955395000...1742955496000)
     tempModel.symbol = "哈哈"
     tempModel.orderType = orderType
     tempModel.id = UUID().uuidString
     tempModel.price = "\(Double.random(in: 82913.97...83913.97))"
     tempModel.openAvgPrice = "\(Double.random(in: 82913.97...83913.97))"
     tempModel.openAvgPriceFullyScale = "1"
     tempModel.triggerPrice = "\(Double.random(in: 82913.97...83913.97))"
     tempModel.holdVol = "10"
     tempModel.vol = "10"
     tempModel.dealAvgPrice = "\(Double.random(in: 82913.97...83913.97))"
     tempModel.dealVol = "10"
     tempModel.leverage = "10"
     tempModel.positionType =  orderType == .liquidationOrder ? .increase : .decrease
     return [tempModel]
 }
 */
    
    
}
extension ViewController:KDCDelegate{
    func kLineListDidUpdate(needRewDraw: Bool) {
        self.kLineView.context?.readKlineData { [weak self] list in
            guard let self = self else { return }
            self.kLineView.setKLineModels(list, needRewDraw)
        }
    }
    
    
}

extension ViewController{
    //显示划线工具视图
    func professionalKlineShowDarwView(){
        guard KLineConfig.kLineType == .professional else { return }
        // 显示画线工具
        if drawToolView.superview != nil { drawToolView.removeFromSuperview() }
        view.addSubview(drawToolView)
        drawToolView.backgroundColor = KLineConfig.themeManager.bgCard()
        drawToolView.snp.remakeConstraints { make in
            make.left.equalTo(kLineView.snp.right)
            make.top.bottom.equalTo(kLineView)
            make.right.equalToSuperview()
        }
//        targetView.isHidden = true
        drawToolView.isHidden = false
        kLineView.showDrawTool = true
    }
    //隐藏划线工具视图
//    func professionalKlineHiddenDarwView(){
//        guard KLineConfig.kLineType == .professional else { return }
//        // 显示指标设置
//        if targetView.superview != nil { targetView.removeFromSuperview() }
//        view.addSubview(targetView)
//        targetView.snp.remakeConstraints { make in
//            make.left.equalTo(kLineView.snp.right)
//            make.top.bottom.equalTo(kLineView)
//            make.right.equalToSuperview()
//        }
//        targetView.isHidden = false
//        drawToolView.isHidden = true
//        drawSettingView.isHidden = true
//        kLineView.showDrawTool = false
//    }
}
extension ViewController{
   
    /// 线段设置拖动
    @objc func draggedView(_ sender: UIPanGestureRecognizer) {
        let translation = sender.translation(in: self.view)
        var pt = CGPoint(x: drawSettingView.center.x + translation.x, y: drawSettingView.center.y + translation.y)
        let size = drawSettingView.frame.size
        
        let safeArea = (self.view.safeAreaInsets.bottom == 0) ? 12 : self.view.safeAreaInsets.bottom
        pt.x = max(pt.x, size.width * 0.5 + 35)
        pt.x = min(pt.x, self.view.frame.width - size.width * 0.5)
        pt.y = max(pt.y, size.height * 0.5 + safeArea)
        pt.y = min(pt.y, self.view.frame.height - size.height * 0.5 - safeArea)
        sender.setTranslation(CGPoint.zero, in: self.view)
        self.drawSettingView.snp.remakeConstraints { make in
            make.centerX.equalTo(pt.x)
            make.centerY.equalTo(pt.y)
            make.width.equalTo(296)
            make.height.equalTo(44)
        }
        self.drawSettingView.layoutIfNeeded()
        self.drawSettingView.reset()
    }
}

extension ViewController:KlineViewDelegate{
  func leftmostModelWith(klineView: KLineLib.KLineView, klineModel: KLineLib.KLineModel) {
    
  }
  
    
    /// 滚动到最前 请求加载之前的数据
    func scrollToStartPosition() {
        self.kLineView.addBeforeKlineData(beforeModels: MockDataModel.loadingBeforeData(count: 100))
    }
    //MARK: Michael: 当前选中的KlineModel 里面包含所以得信息
    func currentSelectModelWith(klineView: KLineView, klineModel: KLineModel) {
//        print("current select model is \(klineModel.maDictionary)")
    }
    /// 自定义 info View Step:3
    func updatePopDataWith(klineView: KLineView, kLineModel: KLineModel, price: String) {
        popView.updateData(kLineModel: kLineModel, price: price)
    }
    /// 自定义 info View Step:4
    func showIndicatorViewsWith(klineView: KLineLib.KLineView, isShow: Bool, horizontalStatus: Bool, point: CGPoint?) {
        popView.showIndicatorViews(isShow: isShow, horizontalStatus: horizontalStatus, point: point)
    }
  func closingPriceOfThePreviousDay() -> Double? {
    return self.kLineView.kLineModels.last?.open
  }
}
