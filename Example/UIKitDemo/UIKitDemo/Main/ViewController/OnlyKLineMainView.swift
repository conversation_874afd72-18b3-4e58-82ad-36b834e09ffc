//
//  OnlyKLineMainView.swift
//  UseKlineToolsDemo
//
//  Created by <PERSON> on 2025/4/8.
//

import UIKit
import KLineLib
class OnlyKLineMainView: UIView {
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.backgroundColor = .white
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }
    override func draw(_ rect: CGRect) {
        super.draw(rect)
        let models = MockDataModel.createMockData()
        //重点是这一行代买 需要使用KLineViewModel中一个方法生成 positionModel 200为你要显示的视图高度
        let result = convertToKLinePositionModels(containerSize: CGSize(width: self.frame.width, height: 200),kLineModels: models,expectedTotalDataCount:80)
        
        guard let context = UIGraphicsGetCurrentContext() else { return }
        
        let color = UIColor.blue
        var array = [CGPoint]()
        
        for model in result.kLineModels {
            guard let positionModel = model.positionModel else { continue }
            let value: CGPoint = positionModel.closePoint
            array.append(value)
        }
        //你可以新起一个方法 照下面的抄 简单改一下
      self.drawLineWith(context:context, rect: self.bounds,positionArray: array,color: color,width: CGFloat(1),gradient: true,curves: false)
    }
}

extension OnlyKLineMainView{
    
    /// 将model转化为Position模型
    func convertToKLinePositionModels(containerSize:CGSize,
                                             kLineModels: [KLineModel],
                                             displayAllData:Bool = true,
                                             expectedTotalDataCount:Int?) -> KLineInvokeValues {
        guard kLineModels.first != nil else { return (kLineModels, 0.0, 0.0, 0) }
        var minValue = Double.greatestFiniteMagnitude
        var maxValue: Double = 0.0
        
        for kLineModel in kLineModels {
            
                if kLineModel.high > maxValue {
                    maxValue = kLineModel.high
                }
                
                if kLineModel.low  < minValue {
                    minValue = kLineModel.low
                }
                
                if kLineModel.close  > maxValue {
                    maxValue = kLineModel.close
                }
                
                if kLineModel.close  < minValue {
                    minValue = kLineModel.close
                }
                
        }
        
        let minY = KLineConfig.kLineMainViewMinY
        let maxY = containerSize.height - KLineConfig.kLineMainViewMaxY
        var unitValue = 1.0
        
        if maxY != minY && maxValue != minValue {
            unitValue = (maxValue - minValue) / Double((maxY - minY))
        }
        let lineGap = KLineConfig.kLineGap
        let lineWidth = KLineConfig.kLineWidth
        
        
        var idx = 0
        var scale = 14
        
        let templineWidth = calculateLineWidth(oldlineWidth: lineWidth, contentWidth: containerSize.width, count: kLineModels.count,expectedTotalDataCount: expectedTotalDataCount)
        
        for kLineModel in kLineModels {
            var xPosition:CGFloat = .zero
            if displayAllData{
                if let expectedCount = expectedTotalDataCount {
                    if expectedCount < kLineModels.count{
                        xPosition = CGFloat(idx) *  containerSize.width / CGFloat(kLineModels.count)
                    }else{
                        xPosition = CGFloat(idx) *  containerSize.width / CGFloat(expectedCount)
                    }
                }else{
                    xPosition = CGFloat(idx) *  containerSize.width / CGFloat(kLineModels.count)
                }
                xPosition += templineWidth
            }else{
                xPosition =  CGFloat(idx) * (lineGap + lineWidth)
            }
            var openPoint = CGPoint(x: xPosition,
                                    y: (maxY - CGFloat((kLineModel.open  - minValue) / unitValue)))
            var closePointY = (maxY - CGFloat((kLineModel.close  - minValue) / unitValue))
            scale = kLineModel.priceScale
            if scale < 0 { scale = 2 }
            
            // 防止出现柱子特别细，看不清问题
            if abs(closePointY - openPoint.y) < KLineConfig.kLineMinWidth {
                if openPoint.y > closePointY {
                    openPoint.y = closePointY + KLineConfig.kLineMinWidth
                } else if openPoint.y < closePointY {
                    closePointY = openPoint.y + KLineConfig.kLineMinWidth
                } else {
                    if idx > 0 {
                        let preKLineModel = kLineModels[idx - 1]
                        
                        if kLineModel.open  > preKLineModel.close  {
                            openPoint.y = closePointY + KLineConfig.kLineMinWidth
                        } else {
                            closePointY = openPoint.y + KLineConfig.kLineMinWidth
                        }
                    } else if idx + 1 < kLineModels.count {
                        // idx==0即第一个时
                        let subKLineModel = kLineModels[idx + 1]
                        
                        if kLineModel.close  < subKLineModel.open  {
                            openPoint.y = closePointY + KLineConfig.kLineMinWidth
                        } else {
                            closePointY = openPoint.y + KLineConfig.kLineMinWidth
                        }
                    }
                }
            }
            let closePoint = CGPoint(x: xPosition, y: closePointY)
            let highPoint = CGPoint(x: xPosition,
                                    y: (maxY - CGFloat((kLineModel.high  - minValue) / unitValue)))
            let lowPoint = CGPoint(x: xPosition,
                                   y: (maxY - CGFloat((kLineModel.low  - minValue) / unitValue)))
            
            let kLinePositionModel = KLinePositionModel()
            kLinePositionModel.openPoint = openPoint
            kLinePositionModel.closePoint = closePoint
            kLinePositionModel.highPoint = highPoint
            kLinePositionModel.lowPoint = lowPoint
//            kLinePositionModel.color = kLineModel.open  > kLineModel.close  ?
//            KLineConfig.themeManager.candleRiseColor() : KLineConfig.themeManager.candleDeclineColor()
            
            kLineModel.positionModel = kLinePositionModel
            idx += 1
        }
        
        if minValue == Double.greatestFiniteMagnitude { return (kLineModels, 0.0, 0.0, 0) }
        return (kLineModels, maxValue, minValue, scale)
    }
    
    func calculateLineWidth(oldlineWidth:CGFloat,contentWidth:CGFloat,count:Int,displayAllData:Bool = true,expectedTotalDataCount:Int?)->CGFloat{
        var templineWidth = oldlineWidth
        if displayAllData{
            if expectedTotalDataCount == nil {
                templineWidth = contentWidth / CGFloat(count)
            }else if  let expectedCount = expectedTotalDataCount ,expectedCount >= count{
                templineWidth = contentWidth / CGFloat(expectedCount)
            }else{
                templineWidth = contentWidth / CGFloat(count)
            }
            templineWidth *= 0.8 //再缩小一点 才能显示出间隙
        }
        return templineWidth
    }
}
