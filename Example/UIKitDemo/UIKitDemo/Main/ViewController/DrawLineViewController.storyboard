<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Draw Line View Controller-->
        <scene sceneID="s0d-6b-0kx">
            <objects>
                <viewController storyboardIdentifier="DrawLineViewController" id="Y6W-OH-hqX" customClass="DrawLineViewController" customModule="UIKitDemo" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="5EZ-qb-Rvc">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="5BJ-TD-7U4">
                                <rect key="frame" x="9.9999999999999929" y="128.00000000000003" width="125.33333333333331" height="344.66666666666674"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="CSf-6m-4lh">
                                        <rect key="frame" x="33.666666666666664" y="0.0" width="57.999999999999993" height="34.333333333333336"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="线条"/>
                                        <connections>
                                            <action selector="drawBtnTap:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="iO1-05-iAW"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" tag="1" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Uho-ar-Hkm">
                                        <rect key="frame" x="25.333333333333336" y="44.333333333333343" width="74.666666666666657" height="34.333333333333343"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="多边形"/>
                                        <connections>
                                            <action selector="drawBtnTap:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="OWs-5X-dLn"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" tag="2" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="9AE-Zj-bfP">
                                        <rect key="frame" x="33.666666666666664" y="88.666666666666657" width="57.999999999999993" height="34.333333333333343"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="多浪"/>
                                        <connections>
                                            <action selector="drawBtnTap:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="ma1-Vd-pV3"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" tag="3" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="rpE-46-6mH">
                                        <rect key="frame" x="0.0" y="133" width="125.33333333333333" height="34.333333333333343"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="斐波那契回撤"/>
                                        <connections>
                                            <action selector="drawBtnTap:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="cTu-K8-T6Q"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="rdO-MM-lFn">
                                        <rect key="frame" x="17" y="177.33333333333331" width="91.666666666666671" height="34.333333333333343"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="隐藏划线"/>
                                        <connections>
                                            <action selector="hiddenDrawLine:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="C9b-n4-5j7"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="uy7-ur-bXO">
                                        <rect key="frame" x="17" y="221.66666666666669" width="91.666666666666671" height="34.333333333333314"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="连续绘制"/>
                                        <connections>
                                            <action selector="continuousDrawing:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="qQm-d0-6Zn"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="9ap-NY-pd2">
                                        <rect key="frame" x="0.0" y="266" width="125.33333333333333" height="34.333333333333314"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="删除所有绘图"/>
                                        <connections>
                                            <action selector="deleteAllLine:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="wxJ-zl-NOj"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="mNA-lk-h8k">
                                        <rect key="frame" x="0.0" y="310.33333333333331" width="125.33333333333333" height="34.333333333333314"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="配置划线工具"/>
                                        <connections>
                                            <action selector="drawToolsconfiguration:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="GQx-cH-P3h"/>
                                        </connections>
                                    </button>
                                </subviews>
                            </stackView>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="GI7-KV-TTy">
                                <rect key="frame" x="145.33333333333337" y="128" width="237.66666666666663" height="646"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <prototypes>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="DrawLineTableViewCell" id="5FW-Cp-YFQ" customClass="DrawLineTableViewCell" customModule="UIKitDemo" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="50" width="237.66666666666663" height="44.666667938232422"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="5FW-Cp-YFQ" id="Xix-r4-udO">
                                            <rect key="frame" x="0.0" y="0.0" width="237.66666666666663" height="44.666667938232422"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tnk-HX-E6T">
                                                    <rect key="frame" x="10" y="12.333333333333334" width="41.333333333333336" height="20.333333333333329"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <nil key="textColor"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="tnk-HX-E6T" firstAttribute="leading" secondItem="Xix-r4-udO" secondAttribute="leading" constant="10" id="qys-FR-mEL"/>
                                                <constraint firstItem="tnk-HX-E6T" firstAttribute="centerY" secondItem="Xix-r4-udO" secondAttribute="centerY" id="sJf-Uq-XWh"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <connections>
                                            <outlet property="titleLab" destination="tnk-HX-E6T" id="vFb-wC-8Eb"/>
                                        </connections>
                                    </tableViewCell>
                                </prototypes>
                                <connections>
                                    <outlet property="dataSource" destination="Y6W-OH-hqX" id="Feo-vs-47k"/>
                                    <outlet property="delegate" destination="Y6W-OH-hqX" id="HQu-oO-sYG"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="vDu-zF-Fre"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="5BJ-TD-7U4" firstAttribute="top" secondItem="vDu-zF-Fre" secondAttribute="top" constant="10" id="EgU-J2-pB7"/>
                            <constraint firstItem="GI7-KV-TTy" firstAttribute="leading" secondItem="5BJ-TD-7U4" secondAttribute="trailing" constant="10" id="FJI-qb-gjN"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="bottom" secondItem="GI7-KV-TTy" secondAttribute="bottom" constant="10" id="Gw5-Ns-Y0f"/>
                            <constraint firstItem="5BJ-TD-7U4" firstAttribute="leading" secondItem="5EZ-qb-Rvc" secondAttribute="leading" constant="10" id="qcW-sh-rw9"/>
                            <constraint firstItem="GI7-KV-TTy" firstAttribute="top" secondItem="CSf-6m-4lh" secondAttribute="top" id="tWn-Vx-aOP"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="trailing" secondItem="GI7-KV-TTy" secondAttribute="trailing" constant="10" id="vnr-XK-L9v"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="tableView" destination="GI7-KV-TTy" id="cZz-5n-0Sw"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Ief-a0-LHa" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="140" y="5"/>
        </scene>
    </scenes>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
