//
//  ViewController+.swift
//  UseKlineToolsDemo
//
//  Created by <PERSON> on 2025/3/31.
//

import Foundation
import UIKit
import KLineLib
///划线相关代理
extension ViewController:KLineDrawToolViewDelegate{
    func drawShapeDidChange(_ type: KLineDrawShapeType) {
        let drawViewModel = KLineDrawViewModel.shared
        drawViewModel.hiddenDraw = false
        drawViewModel.createNewDrawModel(type)
    }
    
    func drawToolDidSelected(_ type: KLineDrawToolBoxType) {
        let drawViewModel = KLineDrawViewModel.shared
        switch type {
        case .hideDraw:
            self.drawSettingView.isHidden = true
            drawViewModel.hiddenDraw = true
        case .showDraw:
            self.drawSettingView.isHidden = (drawViewModel.currentDrawModel == nil)
            drawViewModel.hiddenDraw = false
        case .clean:
            let alertView = AlertPopView(title: "ob_common_tips".BPLocalized(),
                                           desc: "ob_chart_delete_drawing_lines_tips".BPLocalized(),
                                           confirm: "ob_common_confirm".BPLocalized(),
                                           leftConfirm: "ob_common_cancel".BPLocalized(),
                                            theme: KlineLibThemeManager.currentTheme)
            
            alertView.show(confirmBlock: { [weak self] in
                guard let self = self else { return }
                self.drawSettingView.isHidden = true
                drawViewModel.removeAlllDrawModel()
            })
        case .share:
            //KLineViewController.shareKLineView(vc: self)
            break
        case .exit:
            /* 需要设置成这样 K线图才会显示info View
            drawSettingView.isHidden = true
            kLineView.showDrawTool = false
            */
            drawSettingView.isHidden.toggle()
            kLineView.showDrawTool.toggle()
            
//            self.professionalKlineHiddenDarwView()
        case .repeatDraw:
            drawViewModel.isRepeatDraw.toggle()
        case .fibonacci:
            drawShapeDidChange(KLineDrawShapeType.fibonacci)
        default: break
        }
    }
}
///
extension ViewController{
    /// 设置图形参数
    func setUpKLineDrawShape() {
        // 设置Controller
        let drawViewModel = KLineDrawViewModel.shared
        
        drawViewModel.clear()
        drawViewModel.notificationDelegate = self
        
        // 更新数据
        drawViewModel.reloadKlineTimeData()
        
        // 画图对象发生改变
        CusstomNotification.observe(self, KLineDrawModeDidChangedNotification) { [weak self] notify in
            guard let drawStatus = notify.object as? KLineDrawStatus, let self = self else { return }
            
            if drawStatus == .drawShape {
                //                if drawViewModel.currentDrawModel?.status == .drawPoint {
                //                    self.headerMaskView.isHidden = false
                //                } else {
                //                    self.headerMaskView.isHidden = true
                //                }
                self.drawSettingView.isHidden = false
                
                if self.drawSettingView.superview == nil {
                    self.view.addSubview(self.drawSettingView)
                    self.drawSettingView.snp.remakeConstraints { make in
                        make.centerX.equalToSuperview()
                        make.bottom.equalToSuperview().offset(-44)
                        make.width.equalTo(296)
                        make.height.equalTo(44)
                    }
                }
                // 更新数据
                self.drawSettingView.reloadDrawModel()
            } else {
                //self.headerMaskView.isHidden = true
                self.drawSettingView.isHidden = true
            }
            
            // 更新右侧高亮
            self.drawToolView.reloadDrawShapeStatus()
        }
    }
    
    /// VC deinit 时需要处理的logic
    func destoryKLineDrawShape() {
        KLineDrawViewModel.shared.drawStatus = .none
        CusstomNotification.remove(self, KLineDrawModeDidChangedNotification)
    }
}
extension ViewController:KLineDrawManagerDelegate{
    func drawSettingViewReloadDrawModel() {
        self.drawSettingView.reloadDrawModel()
    }
    
    
    func showHeaderTipsView(title: String, detail: String) {
        self.headerTipsV.titleL.text = title
        self.headerTipsV.detailL.text = detail
        self.headerTipsV.isHidden = false
    }
    
    func closeHeaderTipsView() {
        self.headerTipsV.isHidden = true
    }
    
    func showCenterTipsView(title: String) {
        self.centerTipsV.titleL.text = title
        self.centerTipsV.isHidden = false
        DispatchQueue.main.asyncAfter(deadline: .now() + 10){ [weak self]  in
            guard let self = self else { return }
            self.centerTipsV.isHidden = true
            
        }
    }
    
    func closeCenterTipsView() {
        self.centerTipsV.isHidden = true
    }
    
    func didClearCurrentDrawModel() {
        
    }
}
extension ViewController{
    @IBAction func KlineTypeBtnAction(_ sender: UIButton) {
        KLineConfig.type = KLineViewType(rawValue:sender.tag) ?? .timeLine
        // 更新数据
        KLineDrawViewModel.shared.reloadKlineTimeData()
        
        asyncOnMain {[weak self]  in
            guard let self = self else { return }
            self.kLineView.updateUI()
        }
        //可选 是否更新数据源
//        self.kLineView.setKLineModels(MockDataModel.createMockData(), true)
    }
    @IBAction func tapBtnAction(_ sender: UIButton) {
#warning("使用例子 看这个地方")
        let vc = UIStoryboard(name: "Main", bundle: nil).instantiateViewController(withIdentifier: "SecondViewController") as! SecondViewController
        vc.mainTypes = KLineConfig.mainViewType
        vc.subTypes = KLineConfig.subViewType
        
        
        vc.blockMainTypes = { [weak self] types in
            guard let self = self else { return }
            //主K线更新
            KLineConfig.mainViewType = types
            UserDefaults.cachedKLineMainType = types
            self.kLineView.updateUI()
            
            
            //更新原Demo中的UI Chief 中不需要
            //self.targetView.updateUI()
        }
        vc.blockSubTypes = { [weak self] types in
            guard let self = self else { return }
            //副K线更新
            KLineConfig.subViewType = types
            UserDefaults.cachedKLineSubTypeList = types
            self.kLineView.updateUI()
            
            
            
            //更新原Demo中的UI Chief 中不需要
            //self.targetView.updateUI()
        }
        
        self.navigationController?.pushViewController(vc, animated: true)
        
    }
    @IBAction func addLineData(_ sender: UIButton) {
        
        self.kLineView.addKlineData(appendModels:MockDataModel.loadingAfterData(count: 100))
        
        
    }
    @IBAction func gotoMainView(_ sender: UIButton) {
        let vc = OnlyKLineMainViewController()
        self.navigationController?.pushViewController(vc, animated: true)
    }
    @IBAction func switchTheme(_ sender: UIButton) {
        if KlineLibThemeManager.currentTheme == .dark{
            KlineLibThemeManager.currentTheme = .light
        }else{
            KlineLibThemeManager.currentTheme = .dark
        }
        //自定义 info View
        popView.changeTheme()
    }
  
    @IBAction func drawLine(_ sender: UIButton) {
        let vc = UIStoryboard(name: "DrawLineViewController", bundle: nil).instantiateViewController(withIdentifier: "DrawLineViewController") as! DrawLineViewController
        self.navigationController?.pushViewController(vc, animated: true)
    }
    @IBAction func maIndexLine(_ sender: UIButton) {
        KLineConfig.WMAArray = [26,20,18,15,12,9,6]
        self.kLineView.reloadKLineIndicator()
        self.kLineView.drawKLine()
        
    }
  
  @IBAction func resetReferences(_ sender: UIButton){
    sender.isSelected.toggle()
    let first:[CGFloat] = [20,50,60]
    let second:[CGFloat] = [10,50,70]
    KLineConfig.references = [
      SubReferenceLineModel(type: .rsi, maxValue: 100, minValue: 0.0, referenceList: sender.isSelected == true ? first : second)
    ]
    kLineView.updateUI()
  }
}
