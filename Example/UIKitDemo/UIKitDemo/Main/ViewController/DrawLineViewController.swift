//
//  DrawLineViewController.swift
//  UIKitDemo
//
//  Created by <PERSON> on 2025/4/17.
//

import UIKit
import KLineLib
class DrawLineViewController: UIViewController {

    var dataSources:[KLineDrawShapeType] = [.segmentLine, .horizontalLine, .radialLine, .verticalLine, .priceLine, .parallelChannel]
    @IBOutlet weak var tableView: UITableView!
    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
    }
    
    @IBAction func drawBtnTap(_ sender: UIButton) {
        switch sender.tag{
        case 0:
            dataSources = [.segmentLine, .horizontalLine, .radialLine, .verticalLine, .priceLine, .parallelChannel]
        case 1:
            dataSources = [.rectangle, .parallelogram]
        case 2:
            dataSources = [.threeWaves, .fiveWaves]
        case 3:
            selectDrawType(type: .fibonacci)
        default:break
        }
        self.tableView.reloadData()
    }
    
    @IBAction func hiddenDrawLine(_ sender: Any) {
        //隐藏划线
        KLineDrawViewModel.shared.hiddenDraw.toggle()
    }
    
    @IBAction func continuousDrawing(_ sender: Any) {
        //连续绘制
        KLineDrawViewModel.shared.isRepeatDraw.toggle()
        
    }
    @IBAction func deleteAllLine(_ sender: Any) {
        //删除所以划线
        KLineDrawViewModel.shared.removeAlllDrawModel()
    }
   
        
    @IBAction func drawToolsconfiguration(_ sender: Any) {
        
        
//        guard let model = KLineDrawViewModel.shared.currentDrawModel else { return }
//        model.fillColor = "#FF0000"
//        
//        model.lineThickness = 2
//        model.lineDashes = [2,2]
//        model.lockDraw = false
//        KLineDrawViewModel.shared.updateDrawParam()
        
        
    }
        
        
    
}
extension DrawLineViewController:UITableViewDelegate,UITableViewDataSource{
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        dataSources.count
    }
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "DrawLineTableViewCell", for: indexPath) as! DrawLineTableViewCell
        cell.titleLab.text = dataSources[indexPath.row].title.BPLocalized()
        return cell
    }
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        selectDrawType(type: dataSources[indexPath.row])
    }
    private func selectDrawType(type:KLineDrawShapeType){
        let drawViewModel = KLineDrawViewModel.shared
        drawViewModel.hiddenDraw = false
        drawViewModel.createNewDrawModel(type)
        self.navigationController?.popViewController(animated: true)
    }
}
class DrawLineTableViewCell:UITableViewCell{
    @IBOutlet weak var titleLab: UILabel!
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
    }
    
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }
}
