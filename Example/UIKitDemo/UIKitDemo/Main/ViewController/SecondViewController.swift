//
//  SecondViewController.swift
//  UseKlineToolsDemo
//
//  Created by <PERSON> on 2025/4/2.
//

import UIKit
import KLineLib
class SecondViewController: UIViewController {

    @IBOutlet weak var testLab: UILabel!
    var mainTypes:[KLineMainViewType] = []
    
    @IBOutlet var mainBtns: [UIButton]!
    var subTypes:[KLineSubViewType] = []
    @IBOutlet weak var displayIndexValueSwitchBtn: UISwitch!
    @IBOutlet var subBtns: [UIButton]!
    
    @IBOutlet var pricePositionBtns: [UIButton]!
    
    var blockMainTypes:(([KLineMainViewType])->Void)? = nil
    var blockSubTypes:(([KLineSubViewType])->())? = nil
//    var timelintStyle:((KlineTimeline)->Void)? = nil
    
    
    @IBOutlet var timeBtns: [UIButton]!
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        self.blockMainTypes!(self.mainTypes)
        self.blockSubTypes!(self.subTypes)
        
    }
    override func viewDidLoad() {
        super.viewDidLoad()
        testLab.text = "ob_common_open".BPLocalized()
        // Do any additional setup after loading the view.
        reloadUI()
        timeBtns.forEach({$0.isSelected = KLineConfig.kLineTimeViewWithBottomView.rawValue == $0.tag})
    }
    func reloadUI(){
        displayIndexValueSwitchBtn.isOn = KLineConfig.displayindexValueOnView
        switch KLineConfig.pricePosition{
        case .left(_):
            pricePositionBtns[0].isSelected = true
        case .right(_):
            pricePositionBtns[1].isSelected = true
        }
        
        
        self.mainBtns.forEach({$0.isSelected = false})
        self.subBtns.forEach({$0.isSelected = false})
        
        self.mainTypes.forEach { type in
            if let find = mainBtns.first(where: {$0.tag == type.rawValue}){
                find.isSelected = true
            }
        }
        
        self.subTypes.forEach { type in
            if let find = subBtns.first(where: {$0.tag == type.rawValue}){
                find.isSelected = true
            }
        }
    }
    @IBAction func mainBtnTap(_ sender: UIButton) {
        sender.isSelected.toggle()
        if sender.isSelected,let type = KLineMainViewType(rawValue: sender.tag){
            mainTypes.append(type)
        }else{
            mainTypes.removeAll(where: {$0.rawValue == sender.tag})
        }
    }
    
    
    @IBAction func subBtnTap(_ sender: UIButton) {
        sender.isSelected.toggle()
        if sender.isSelected,let type = KLineSubViewType(rawValue: sender.tag){
            subTypes.append(type)
            let temp = subTypes.suffix(4)
            subBtns.forEach({$0.isSelected = false})
            temp.forEach { type in
                if let find = subBtns.first(where: {$0.tag == type.rawValue}){
                    find.isSelected = true
                }
            }
            
        }else{
            subTypes.removeAll(where: {$0.rawValue == sender.tag})
        }
    }
    

    
    
    
    @IBAction func swiitchLanguage(_ sender: UIButton) {
        guard let title = sender.titleLabel?.text else{return}
        switch sender.tag {
        case 0:
            LocalizationManager.shared.setLanguageWith(language: .english)
        case 1:
            LocalizationManager.shared.setLanguageWith(language: .simplifiedChinese)

        default:break
        }
        testLab.text = "ob_common_open".BPLocalized()
    }
    
    @IBAction func timeViewPosition(_ sender: UIButton) {
        timeBtns.forEach({$0.isSelected = false})
        sender.isSelected = true
        KLineConfig.kLineTimeViewWithBottomView = kLineTimeViewPosition(rawValue: sender.tag) ?? .mainViewBottom
    }
    @IBAction func displayIndexValue(_ sender: UISwitch) {
        KLineConfig.displayindexValueOnView = sender.isOn
    }
    
    @IBAction func pricePosition(_ sender: UIButton) {
        pricePositionBtns.forEach({$0.isSelected = false})
        sender.isSelected = true
        switch sender.tag{
        case 0:
            KLineConfig.pricePosition = .left(offset: 10)
        case 1:
            KLineConfig.pricePosition = .right(offset: 10)
        default:break
        }
    }
    
}
