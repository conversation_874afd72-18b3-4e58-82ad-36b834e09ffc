// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		69D2B96C2DA8EE8C00AF224A /* KLineLib in Frameworks */ = {isa = PBXBuildFile; productRef = 69D2B96B2DA8EE8C00AF224A /* KLineLib */; };
		8E5607472DAF833300C8697F /* SwiftDate in Frameworks */ = {isa = PBXBuildFile; productRef = 8E5607462DAF833300C8697F /* SwiftDate */; };
		8EA4B3EB2DA8EFA700FB557B /* FLEX in Frameworks */ = {isa = PBXBuildFile; productRef = 8EA4B3EA2DA8EFA700FB557B /* FLEX */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		69D2B9202DA8EE3500AF224A /* UIKitDemo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = UIKitDemo.app; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		69D2B9322DA8EE3600AF224A /* Exceptions for "UIKitDemo" folder in "UIKitDemo" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 69D2B91F2DA8EE3500AF224A /* UIKitDemo */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		69D2B9222DA8EE3500AF224A /* UIKitDemo */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				69D2B9322DA8EE3600AF224A /* Exceptions for "UIKitDemo" folder in "UIKitDemo" target */,
			);
			path = UIKitDemo;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		69D2B91D2DA8EE3500AF224A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				69D2B96C2DA8EE8C00AF224A /* KLineLib in Frameworks */,
				8EA4B3EB2DA8EFA700FB557B /* FLEX in Frameworks */,
				8E5607472DAF833300C8697F /* SwiftDate in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		69D2B9172DA8EE3500AF224A = {
			isa = PBXGroup;
			children = (
				69D2B9222DA8EE3500AF224A /* UIKitDemo */,
				69D2B96A2DA8EE8C00AF224A /* Frameworks */,
				69D2B9212DA8EE3500AF224A /* Products */,
			);
			sourceTree = "<group>";
		};
		69D2B9212DA8EE3500AF224A /* Products */ = {
			isa = PBXGroup;
			children = (
				69D2B9202DA8EE3500AF224A /* UIKitDemo.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		69D2B96A2DA8EE8C00AF224A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		69D2B91F2DA8EE3500AF224A /* UIKitDemo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 69D2B9332DA8EE3600AF224A /* Build configuration list for PBXNativeTarget "UIKitDemo" */;
			buildPhases = (
				69D2B91C2DA8EE3500AF224A /* Sources */,
				69D2B91D2DA8EE3500AF224A /* Frameworks */,
				69D2B91E2DA8EE3500AF224A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				69D2B9222DA8EE3500AF224A /* UIKitDemo */,
			);
			name = UIKitDemo;
			packageProductDependencies = (
				69D2B96B2DA8EE8C00AF224A /* KLineLib */,
				8EA4B3EA2DA8EFA700FB557B /* FLEX */,
				8E5607462DAF833300C8697F /* SwiftDate */,
			);
			productName = UIKitDemo;
			productReference = 69D2B9202DA8EE3500AF224A /* UIKitDemo.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		69D2B9182DA8EE3500AF224A /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					69D2B91F2DA8EE3500AF224A = {
						CreatedOnToolsVersion = 16.2;
					};
				};
			};
			buildConfigurationList = 69D2B91B2DA8EE3500AF224A /* Build configuration list for PBXProject "UIKitDemo" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 69D2B9172DA8EE3500AF224A;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				8EA4B3E92DA8EFA700FB557B /* XCRemoteSwiftPackageReference "FLEX" */,
				8E5607452DAF833300C8697F /* XCRemoteSwiftPackageReference "SwiftDate" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 69D2B9212DA8EE3500AF224A /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				69D2B91F2DA8EE3500AF224A /* UIKitDemo */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		69D2B91E2DA8EE3500AF224A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		69D2B91C2DA8EE3500AF224A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		69D2B9342DA8EE3600AF224A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = CWZ27838R7;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = UIKitDemo/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.stx.UIKitDemo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		69D2B9352DA8EE3600AF224A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = CWZ27838R7;
				EXCLUDED_SOURCE_FILE_NAMES = "FLEX*";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = UIKitDemo/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.stx.UIKitDemo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		69D2B9362DA8EE3600AF224A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		69D2B9372DA8EE3600AF224A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		69D2B91B2DA8EE3500AF224A /* Build configuration list for PBXProject "UIKitDemo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				69D2B9362DA8EE3600AF224A /* Debug */,
				69D2B9372DA8EE3600AF224A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		69D2B9332DA8EE3600AF224A /* Build configuration list for PBXNativeTarget "UIKitDemo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				69D2B9342DA8EE3600AF224A /* Debug */,
				69D2B9352DA8EE3600AF224A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		8E5607452DAF833300C8697F /* XCRemoteSwiftPackageReference "SwiftDate" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/malcommac/SwiftDate.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 7.0.0;
			};
		};
		8EA4B3E92DA8EFA700FB557B /* XCRemoteSwiftPackageReference "FLEX" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/FLEXTool/FLEX.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.22.10;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		69D2B96B2DA8EE8C00AF224A /* KLineLib */ = {
			isa = XCSwiftPackageProductDependency;
			productName = KLineLib;
		};
		8E5607462DAF833300C8697F /* SwiftDate */ = {
			isa = XCSwiftPackageProductDependency;
			package = 8E5607452DAF833300C8697F /* XCRemoteSwiftPackageReference "SwiftDate" */;
			productName = SwiftDate;
		};
		8EA4B3EA2DA8EFA700FB557B /* FLEX */ = {
			isa = XCSwiftPackageProductDependency;
			package = 8EA4B3E92DA8EFA700FB557B /* XCRemoteSwiftPackageReference "FLEX" */;
			productName = FLEX;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 69D2B9182DA8EE3500AF224A /* Project object */;
}
