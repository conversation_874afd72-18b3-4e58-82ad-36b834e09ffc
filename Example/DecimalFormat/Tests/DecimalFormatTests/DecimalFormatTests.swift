import SwiftSyntax
import SwiftSyntaxBuilder
import SwiftSyntaxMacros
import SwiftSyntaxMacrosTestSupport
import XCTest

// Macro implementations build for the host, so the corresponding module is not available when cross-compiling. Cross-compiled tests may still make use of the macro itself in end-to-end tests.
#if canImport(DecimalFormatMacros)
@testable import DecimalFormatMacros
import MacroTesting

#endif

final class DecimalFormatTests: XCTestCase {
    func testMacro() throws {
        #if canImport(DecimalFormatMacros)
        assertMacro([DecimalFormatMacro.self, RuntimeDecimalFormatibleMacro.self]) {
            """
            @RuntimeDecimalFormatible
            struct TestData {
                @DecimalFormat(.default.min(2).max(2))
                var weight: Decimal? = 10.5
            }
            """
        } expansion: {
            #"""
            struct TestData {
                var weight: Decimal? = 10.5

                @MainActor // Apply MainActor for formatter cache access
                public var weightFormatted: String {
                    // Check for Main Thread - useful for debugging cache issues
                    // Foundation._assertIfMainThread()

                    // 1. Get base config from macro attribute
                    let baseConfig = .default.min(2).max(2)

                    // 2. Runtime Hook: Check if self conforms to the protocol
                    //    and call the resolution function if it does.
                    guard let configProvider = self as? (any DecimalFormat.RuntimeConfiguration) else {
                        throw DecimalFormatError.structShouldConformsToDecimalRuntimeConfig
                    }
                    let runtimeConfig = configProvider.resolveDecimalFormatConfig(for: \.weight, base: baseConfig)
                    // 3. Access original value (handle optionality if needed)
                    guard let value = self.weight else {
                        return "-"
                    }

                    // 4. Format using the helper
                    //    Pass the potentially overridden runtimeConfig
                    return DecimalFormatHelper.formatValue(value, using: runtimeConfig)
                }
            }

            extension TestData: DecimalFormat.RuntimeConfiguration {
            }
            """#
        }
        #else
        throw XCTSkip("macros are only supported when running tests for the host platform")
        #endif
    }
}
