//
//  DecimalFormatError.swift
//  DecimalFormat
//
//  Created by stx80501 on 2025/4/10.
//

import Foundation

public enum DecimalFormatError: CustomStringConvertible, Error {
    case notAVariable
    case staticNotSupported
    case invalidDeclaration
    case missingTypeAnnotation
    case invalidArgument
    case structShouldConformsToDecimalRuntimeConfig
    case codeGenerationFailed(String)

    public var description: String {
        switch self {
        case .notAVariable: return "@DecimalFormat can only be applied to variable declarations (`var`)."
        case .staticNotSupported: return "@DecimalFormat does not support static or class variables currently."
        case .invalidDeclaration: return "Could not extract variable name from declaration."
        case .missingTypeAnnotation: return "Property with @DecimalFormat requires an explicit type annotation (e.g., ': Double?')."
        case .invalidArgument: return "Invalid argument provided to @DecimalFormat macro."
        case .structShouldConformsToDecimalRuntimeConfig: return "struct should conforms to DecimalFormat.RuntimeConfiguration"
        case .codeGenerationFailed(let code): return "Internal error: Failed to generate valid Swift code.\nGenerated:\n\(code)"
            
        }
    }
}

public enum RuntimeConfigError: Error {
    case onlyApplicableToStruct
}
