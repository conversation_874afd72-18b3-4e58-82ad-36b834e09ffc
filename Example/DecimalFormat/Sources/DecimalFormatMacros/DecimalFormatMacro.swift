import SwiftCompilerPlugin
import SwiftSyntax
import SwiftSyntaxBuilder
import SwiftSyntaxMacros

/// Implementation of the `stringify` macro, which takes an expression
/// of any type and produces a tuple containing the value of that expression
/// and the source code that produced the value. For example
///
///     #stringify(x + y)
///
///  will expand to
///
///     (x + y, "x + y")
public struct StringifyMacro: ExpressionMacro {
    public static func expansion(
        of node: some FreestandingMacroExpansionSyntax,
        in context: some MacroExpansionContext
    ) -> ExprSyntax {
        guard let argument = node.arguments.first?.expression else {
            fatalError("compiler bug: the macro does not have any arguments")
        }

        return "(\(argument), \(literal: argument.description))"
    }
}

public struct DecimalFormatMacro: PeerMacro {
    public static func expansion(
        of node: AttributeSyntax,
        providingPeersOf declaration: some DeclSyntaxProtocol,
        in context: some MacroExpansionContext
    ) throws -> [DeclSyntax] {
        guard let varDecl = declaration.as(VariableDeclSyntax.self) else {
            throw DecimalFormatError.notAVariable
        }
        guard
            !varDecl.modifiers.contains(where: {
                $0.name.tokenKind == .keyword(.static)
                    || $0.name.tokenKind == .keyword(.class)
            })
        else {
            throw DecimalFormatError.staticNotSupported
        }
        guard varDecl.bindings.count == 1,
            let binding = varDecl.bindings.first,
            let identifierPattern = binding.pattern.as(
                IdentifierPatternSyntax.self)
        else {
            throw DecimalFormatError.invalidDeclaration
        }
        let originalVarName = identifierPattern.identifier.text
        let originalVarType: TypeSyntax? = binding.typeAnnotation?.type
        guard let originalVarType else {
            throw DecimalFormatError.missingTypeAnnotation
        }
        let configArgument = try getConfigArgument(from: node)
        let peerVarName = TokenSyntax.identifier(originalVarName + "Formatted")
        let generatedCode = try generatePeerCode(
            peerVarName: peerVarName,
            originalVarName: TokenSyntax.identifier(originalVarName),
            originalVarType: originalVarType,
            configArgument: configArgument
        )
        return [DeclSyntax(generatedCode)]
    }

    private static func getConfigArgument(from node: AttributeSyntax) throws
        -> ExprSyntax
    {
        // Find the argument for the 'config'
        guard let arguments = node.arguments?.as(LabeledExprListSyntax.self),
            let configExpr = arguments.first?.expression
        else {
            // If no argument provided, assume .default
            return ExprSyntax("DecimalFormat.Config.default")  // Assuming DecimalFormatConfig.default exists
        }
        // Handle if macro allows unlabeled argument: node.argument?.as(...)
        if arguments.count == 1 && arguments.first?.label == nil {
            return configExpr  // Unlabeled first argument like @DecimalFormat(.price)
        }
        // Handle labeled argument (though the macro definition currently expects unlabeled)
        // if let configArg = arguments.first(where: { $0.label?.text == "config" }) {
        //     return configArg.expression
        // }
        throw DecimalFormatError.invalidArgument  // Or handle differently
    }

    private static func generatePeerCode(
        peerVarName: TokenSyntax,
        originalVarName: TokenSyntax,
        originalVarType: TypeSyntax,
        configArgument: ExprSyntax
    ) throws -> VariableDeclSyntax {

        // Determine if the original type is Optional
        let isOptional = originalVarType.is(OptionalTypeSyntax.self)
        let needsGuard = isOptional
        let keyPathExpr = "\\.\(originalVarName)"
        let code = """
            @MainActor // Apply MainActor for formatter cache access
            public var \(peerVarName): String {
                // Check for Main Thread - useful for debugging cache issues
                // Foundation._assertIfMainThread()

                // 1. Get base config from macro attribute
                let baseConfig = \(configArgument)

                // 2. Runtime Hook: Check if self conforms to the protocol
                //    and call the resolution function if it does.
                guard let _ = self as? (any DecimalFormat.RuntimeConfiguration) else {
                    fatalError("struct should conforms to DecimalFormat.RuntimeConfig")
                }
                let runtimeConfig = resolveDecimalFormatConfig(for: \(keyPathExpr), base: baseConfig)
                // 3. Access original value (handle optionality if needed)
                \(needsGuard ? "guard let value = self.\(originalVarName) else { return \"-\" }": "let value = self.\(originalVarName)")

                // 4. Format using the helper
                //    Pass the potentially overridden runtimeConfig
                return DecimalFormatHelper.formatValue(value, using: runtimeConfig)
            }
            """

        // Parse the generated string back into syntax
        let builder = DeclSyntax(stringLiteral: code)
        guard let generatedVarDecl = builder.as(VariableDeclSyntax.self) else {
            // This should not happen if the string is valid Swift code
            throw DecimalFormatError.codeGenerationFailed(code)
        }
        return generatedVarDecl
    }
}

public struct RuntimeDecimalFormatibleMacro: ExtensionMacro {
    public static func expansion(
        of node: AttributeSyntax,
        attachedTo declaration: some DeclGroupSyntax,
        providingExtensionsOf type: some TypeSyntaxProtocol,
        conformingTo protocols: [TypeSyntax],
        in context: some MacroExpansionContext
    ) throws -> [ExtensionDeclSyntax] {
        guard let structDecl = declaration.as(StructDeclSyntax.self) else {
            throw RuntimeConfigError.onlyApplicableToStruct
        }
        let formattedProperties = structDecl.memberBlock.members
            .compactMap { $0.decl.as(VariableDeclSyntax.self) }
            .filter { variable in
                let attributes = variable.attributes
                return attributes.contains(where: { $0.as(AttributeSyntax.self)?.attributeName.trimmedDescription.contains("DecimalFormat") ?? false})
            }
            .compactMap { $0.bindings.first?.pattern.as(IdentifierPatternSyntax.self)?.identifier.text }
        guard !formattedProperties.isEmpty else {
            return []
        }
        let extensionDecl = try ExtensionDeclSyntax("extension \(type.trimmed): DecimalFormat.RuntimeConfiguration") {}
        return [extensionDecl]
    }
}

@main
struct DecimalFormatPlugin: CompilerPlugin {
    let providingMacros: [Macro.Type] = [
        StringifyMacro.self,
        DecimalFormatMacro.self,
        RuntimeDecimalFormatibleMacro.self
    ]
}
