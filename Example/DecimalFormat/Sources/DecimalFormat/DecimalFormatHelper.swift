//
//  DecimalFormatHelper.swift
//  DecimalFormat
//
//  Created by stx80501 on 2025/4/10.
//

import Foundation

public enum DecimalFormatHelper {
    @MainActor // Ensure cache access is safe if mutated
    private static var formatterCache: [DecimalFormat.Config: NumberFormatter] = [:]

    @MainActor
    public static func cachedFormatter(for config: DecimalFormat.Config) -> NumberFormatter {
        if let cached = formatterCache[config] {
            return cached
        }
        let formatter = NumberFormatter()
        formatter.numberStyle = config.numberStyle
        formatter.minimumFractionDigits = config.min
        formatter.maximumFractionDigits = config.max
        formatter.roundingMode = config.roundingMode
        formatter.isLenient = config.isLenient
        formatter.usesSignificantDigits = config.usesSignificantDigits
        // Note: Prefix/Suffix are handled *outside* the formatter now
        // to allow dynamic ones more easily.
        formatterCache[config] = formatter
        return formatter
    }

    @MainActor
    public static func formatValue(
        _ value: (any BinaryFloatingPoint)?, // More generic input
        using config: DecimalFormat.Config,
        nilDisplayValue: String = "-"       // Default for nil
    ) -> String {
        guard let value = value else {
            return nilDisplayValue
        }

        let numberValue = NSNumber(value: Double(value)) // Convert to Double for NSNumber
        let formatter = cachedFormatter(for: config)

        if let formattedString = formatter.string(from: numberValue) {
            return (config.prefix ?? "") + formattedString + (config.suffix ?? "")
        } else {
            // Fallback if formatting fails for some reason
            return "\(value)"
        }
    }

     // Overload for Decimal? if needed
     @MainActor
     public static func formatValue(
         _ value: Decimal?,
         using config: DecimalFormat.Config,
         nilDisplayValue: String = "-"
     ) -> String {
         guard let value = value, !value.isNaN else {
             return nilDisplayValue
         }

         let numberValue = value as NSNumber
         let formatter = cachedFormatter(for: config)

         if let formattedString = formatter.string(from: numberValue) {
             return (config.prefix ?? "") + formattedString + (config.suffix ?? "")
         } else {
             // Fallback
             return "\(value)"
         }
     }
}
