// The Swift Programming Language
// https://docs.swift.org/swift-book

/// A macro that produces both a value and a string containing the
/// source code that generated the value. For example,
///
///     #stringify(x + y)
///
/// produces a tuple `(x + y, "x + y")`.
@freestanding(expression)
public macro stringify<T>(_ value: T) -> (T, String) = #externalMacro(module: "DecimalFormatMacros", type: "StringifyMacro")

@attached(peer, names: suffixed(Formatted))
public macro DecimalFormat(_ config: DecimalFormat.Config = .default) = #externalMacro(module: "DecimalFormatMacros", type: "DecimalFormatMacro")

@attached(extension, conformances: DecimalFormat.RuntimeConfiguration)
public macro RuntimeDecimalFormatible() = #externalMacro(module: "DecimalFormatMacros", type: "RuntimeDecimalFormatibleMacro")
