//
//  Config.swift
//  DecimalFormat
//
//  Created by stx80501 on 2025/4/10.
//
import Foundation

public enum DecimalFormat {
    
    public struct Config: Hashable, Sendable {
        public private(set) var numberStyle: NumberFormatter.Style
        public private(set) var min: Int
        public private(set) var max: Int
        public private(set) var roundingMode: NumberFormatter.RoundingMode
        public private(set) var isLenient: Bool
        public private(set) var prefix: String?
        public private(set) var suffix: String?
        public private(set) var usesSignificantDigits: Bool
        public init(
            numberStyle: NumberFormatter.Style = .decimal,
            min: Int = 0,
            max: Int = 2,
            roundingMode: NumberFormatter.RoundingMode = .down,
            isLenient: Bool = false,
            prefix: String? = nil,
            suffix: String? = nil,
            usesSignificantDigits: Bool = false
        ) {
            self.numberStyle = numberStyle
            self.min = min
            self.max = max
            self.roundingMode = roundingMode
            self.isLenient = isLenient
            self.prefix = prefix
            self.suffix = suffix
            self.usesSignificantDigits = usesSignificantDigits
        }
    }
    
    public protocol RuntimeConfiguration {
        /// Resolves the final configuration to use for a specific property.
        /// - Parameters:
        ///   - propertyName: The name of the original property (e.g., "bidPercent").
        ///   - baseConfig: The configuration provided in the macro attribute.
        /// - Returns: The final `DecimalFormatConfig` to use for formatting.
        func resolveDecimalFormatConfig<Value>(for keyPath: KeyPath<Self, Value>, base baseConfig: DecimalFormat.Config) -> DecimalFormat.Config
    }
}

extension DecimalFormat.RuntimeConfiguration {
    public func resolveDecimalFormatConfig<Value>(for keyPath: KeyPath<Self, Value>, base baseConfig: DecimalFormat.Config) -> DecimalFormat.Config {
        baseConfig
    }
}

extension DecimalFormat.Config {
    
    public static let `default` = Self(
        min: 0,
        max: 2,
        roundingMode: .down,
        isLenient: false
    )
    
    public static let price = Self(
        min: 0,
        max: 2,
        roundingMode: .down,
        isLenient: true
    )
    
    public static let percent = Self(
        min: 2,
        max: 2,
        roundingMode: .down,
        isLenient: false,
        suffix: "%"
    )
    
    public static func quantity(_ symbol: String?) -> Self {
        Self(
            min: 0,
            max: 8,
            roundingMode: .down,
            isLenient: false,
            suffix: symbol
        )
    }
    
    public func min(_ value: Int) -> Self {
        var new = self
        new.min = value
        return new
    }
    
    public func max(_ value: Int) -> Self {
        var new = self
        new.max = value
        return new
    }
    
    public func roundingMode(_ mode: NumberFormatter.RoundingMode) -> Self {
        var new = self
        new.roundingMode = mode
        return new
    }
    public func isLenient(_ value: Bool) -> Self {
        var new = self
        new.isLenient = value
        return new
    }
    
    public func symbol(_ value: String?) -> Self {
        var new = self
        new.suffix = value
        return new
    }
}
