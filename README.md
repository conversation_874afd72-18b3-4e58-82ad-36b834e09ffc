# KLineLib Directory Structure

## 📁 Extension
- Contains various extensions
- *Purpose*: Utility extensions for the project

## 📁 KlineView
- Main K-line chart related code
- *Purpose*: Core functionality of the KLineLib
  - ### 📁 AlertPopView
  - 暂时不知道什么作用 解决报错添加的文件
  - ### 📁 Class
  - 一些会用到的类 暂时不知道放在哪里合适 暂时放这
  - ### 📁 Config
  - Kline 的一些Config
  - ### 📁 Context
  - 上下文管理相关类
  - ### 📁 CycleView
  - 循环视图组件
  - ### 📁 Delegate
  - 委托协议定义
  - ### 📁 Draw
  - 自定义划线的主要相关类
  - ### 📁 Enum
  - 通用枚举类型定义
  - ### 📁 HeaderView
  - 可定义 原Demo中的 顶部显示当前时间 那些功能
  - ### 📁 infoView
  - 点击K线图时 显示的详细信息的 View 到时候我会把详细信息想办法抛出来
  - ### 📁 KlineSegment
  - 原Demo中的切换分时那个Segment 不好用 到时候会找代替或删除
  - ### 🗂️ KlineView
  - KlineLib 核心代码
  - ### 📁 Languange
  - 多语言相关
  - ### 📁 Magnifier
  - 放大镜功能组件
  - ### 🗂️ MainView
  - KlineLib 核心代码
    - #### 🗂️ Professional
    - 专业K线图
    - #### 🗂️ Simple
    - 简易K线图
  - ### 📁 Model
  - K线图基础模型 后面会修改 里面有垃圾代码
  - ### 📁 Notificattion
  - 通知相关
  - ### 📁 Order
  - 订单委托相关 后面可能需要 自定义
  - ### 📁 RightCurrentValueView
  - 右侧当前值显示视图
  - ### 📁 RightValueView
  - 右侧值显示视图
  - ### 📁 Struct
  - 结构体定义，包括K线数据结构
  - ### 📁 SubView
  - 子视图组件
  - ### 📁 Utils
  - 原Demo部分工具

## 📁 LocalizationTool
- Multi-language support classes
- *Purpose*: Internationalization/localization handling

## 📁 Log
- Logging related classes
- ⚠️ **Note**: To be removed later (temporary)

## 📁 MockData
- Local mock data files
- ⚠️ **Note**: To be removed later (temporary utility)

## 📁 NetWork
- Incomplete network request related code from original project
- ⚠️ **Note**: To be removed later (legacy code)

## 📁 Resources
- 项目资源文件

## 📁 Theme
- Dark/Light mode switching code from original project
- ⚠️ **Status**: Needs modification or removal

## 📁 Example
- 示例项目，展示KLineLib的使用方法
