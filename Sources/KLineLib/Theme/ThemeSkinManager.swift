//
//  File.swift
//  
//
//  Created by pippen on 2024/1/12.
//

import Foundation
import UIKit
extension Theme {
    /// 获取该模式下图片后缀
    /// - Returns: 图片后缀
    public func imageSuffix() -> String {
        switch self {
        case .dark: return "dark"
        case .light: return "light"
        }
    }
}

extension String {
    /// 获取主题图片
    ///
    /// 使用图片名调用
    ///
    /// 由图片名称调用, 根据当前模式: 明亮/暗黑 ,  当前皮肤: 默认来加工图片名称
    ///   图片文件命名规则:  图片名_模式_主题
    ///     例如:
    ///        默认主题明亮:  ob_setting_light
    ///        默认主题暗黑:  ob_setting_dark
    ///
    /// - Parameters:
    ///   - presetTheme: 预置主题, 默认nil, 若设置则使用
    ///   - placeholder: 默认图片
    /// - Returns: UIImage对象
    public func themeImg(_ presetTheme: Theme? = nil, _ placeholder: String? = "" ) -> UIImage {
        if self.isEmpty {
            return UIImage()
        }
        var resultImg = self
        var theme = KlineLibThemeManager.currentTheme
        if presetTheme != nil {
            theme = presetTheme ?? .light
        }
        let parts: [String] = self.components(separatedBy: "_")
        
        let origin = resultImg
        var withoutSkin = resultImg
        let withoutTheme = resultImg

        let themeSuffix = theme.imageSuffix()
        if parts.last != themeSuffix {
            resultImg.append("_\(themeSuffix)")
            withoutSkin = resultImg
        }

        var expectImage = UIImage(named: resultImg)
        if expectImage != nil { return expectImage ?? UIImage() }
        
        // 降级查找
        // 1. 带皮肤,不带主题字段
        expectImage = UIImage(named: withoutTheme)
        if expectImage != nil { return expectImage ?? UIImage() }
        
        // 2. 不带皮肤,带主题字段
        expectImage = UIImage(named: withoutSkin)
        if expectImage != nil { return expectImage ?? UIImage() }
        
        // 3. 不带主题和皮肤字段
        expectImage = UIImage(named: origin)
        if expectImage != nil { return expectImage ?? UIImage() }
        
        return UIImage(named: placeholder ?? "") ?? UIImage()
    }
    /// 给image 渲染颜色 iconPrimary
    public func iconPrimaryImage(image: UIImage) -> UIImage {
        return image.blendImage()
    }
}




