import Foundation

/// 图标字体定义
public struct IconFont {
    
    /// 其他图标
    public struct Other {
        /// 价格提醒
        public static let priceAlert = "\u{e650}"
        /// 交易
        public static let trade = "\u{e651}"
        /// 委托
        public static let entrust = "\u{e652}"
        /// 持仓
        public static let position = "\u{e653}"
        /// 刷新
        public static let refresh = "\u{e654}"
        /// 搜索
        public static let search = "\u{e655}"
        
        public static let arrowDrawKLine = "\u{e650}"
        public static let timelineAngle = "\u{e650}"

        
    }
    //MARK: Michael: 推测 需要这么个struct 及属性
//    public struct arcRight{
//        public static let  flipsValue = ""
//    }
//    public struct arrowRightKLinePrice{
//        public static let value = ""
//    }
//    public struct arrowLeftKLinePrice{
//        public static let value = ""
//    }
    
}

// MARK: - 使用示例
/*
 
 // 1. 设置图标字体
 label.font = OBFontType.iconfont(OBSize.icon.medium).font()
 
 // 2. 设置图标
 // K线周期
 label.text = OBIconFont.KLine.day
 
 // 指标
 label.text = OBIconFont.Indicator.macd
 
 // 绘图工具
 label.text = OBIconFont.DrawTool.trendLine
 
 // 操作按钮
 label.text = OBIconFont.Action.settings
 
 // 提示图标
 label.text = OBIconFont.Alert.success
 
 // 其他图标
 label.text = OBIconFont.Other.priceAlert
 
 // 3. 链式调用
 label.F(OBFontType.iconfont(OBSize.icon.medium).font())
      .T(OBIconFont.Action.settings)
 
 */ 
