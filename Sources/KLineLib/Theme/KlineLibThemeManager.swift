//
//  OBThemeManger.swift
//
//
//  Created by pippen on 2024/1/11.
//

import UIKit
//  APP显示模式. dark夜间模式,  light白天模式.
public enum Theme: Int {
    case dark = 1, light = 2
    // 是否是白天版本.
    public func isLight() -> <PERSON><PERSON> { return self == .light }
    // 是否是夜间版本.
    public func isDark() -> <PERSON><PERSON> { return self == .dark }
}

// MARK: - 关于主题处理.
var __ob_current_theme = Theme.light
private let kCurrentThemeKey = "com.ob.themeversion.key"
public enum KlineLibThemeManager {
    // 初始化APP使用的主题.
    public static func setupTheme() {
        if let value = UDTool.value(key: kCurrentThemeKey) as? Int {

            if value == 1 {
                __ob_current_theme = .dark
            } else {
                __ob_current_theme = .light
            }
        } else {
            // 跟随系统
            let currentMode = UITraitCollection.current.userInterfaceStyle
            if currentMode == .dark {
                __ob_current_theme = .dark
            } else {
                __ob_current_theme = .light
            }
        }
    }
    
    // 当前的显示模式.
    public static var currentTheme: Theme {
        get {
            return __ob_current_theme
        }
        set {
            __ob_current_theme = newValue
            let result = newValue.rawValue
            UDTool.setValue(value: result, forKey: kCurrentThemeKey)
            CusstomNotification.post(Noti.Data.klineThemeSkinDidChanged)
        }
    }
}

