//
//  ThemeColorProtocol.swift
//  UseKlineToolsDemo
//
//  Created by <PERSON> on 2025/4/9.
//

import Foundation
import UIKit
public protocol ThemeColorProtocol{
    
    /// 主体绿色
    func mainGreenColor()->UIColor
    
    /// 主体红色
    /// - Returns: -
    func mainRedColor()->UIColor
    /*主图相关颜色定义*/
    /// 山型图颜色
    func mountainColor()->UIColor 
    
    /// 山型图渐变比例
    func mountainGradient()->[CGFloat]
    
    /// 蜡烛图上涨颜色
    func candleRiseColor()->UIColor 
    
    /// 蜡烛图下降颜色
    func candleDeclineColor()->UIColor 
    
    /// 折线图颜色
    func brokenLineColor()->UIColor 
    
    /// 主图ma指标颜色数组
    func maIndicatorColors()->[UIColor]
    
    /// 主图ema指标颜色数组
    func emaIndicatorColors()->[UIColor]
    
    /// 主图wma指标颜色数组
    func wmaIndicatorColors()->[UIColor]
    
    /// 主图boll指标颜色数组
    func bollIndicatorColors()->[UIColor]
    
    /// 主图close指标颜色数组
    func closeIndicatorColors()->[UIColor]
    
    /// 主图SAR指标颜色数组
    func sarIndicatorColors()->[UIColor]
    
    /*副图相关颜色定义*/
    
    /// 副图VOL指标颜色数组
    func volIndicatorColors()->[UIColor]
    
    /// 副图MACD指标颜色数组
    func macdIndicatorColors()->[UIColor]
    
    /// 副图KDJ指标颜色数组
    func kdjIndicatorColors()->[UIColor]
    
    /// 副图RSI指标颜色数组
    func rsiIndicatorColors()->[UIColor]
    
    /// 副图WR指标颜色数组
    func wrIndicatorColors()->[UIColor]
    
    /// 副图OBV指标颜色数组
    func obvIndicatorColors()->[UIColor]
    
    /// 副图ROC指标颜色数组
    func rocIndicatorColors()->[UIColor]
    
    /// 副图CCI指标颜色数组
    func cciIndicatorColors()->[UIColor]
    
    /// 副图StochRSI指标颜色数组
    func stochRSIIndicatorColors()->[UIColor]
    
    /// 副图TRIX指标颜色数组
    func trixIndicatorColors()->[UIColor]
    
    /// 副图DMI指标颜色数组
    func dmiIndicatorColors()->[UIColor]
    
    
    /*部分视图背景颜色*/
    
    /// 部分跟随主题的视图背景颜色
    func klineLibDefaultBGColor()->UIColor
    /// 白色的文字颜色
    func klineLibDefaultWhiteColor()->UIColor
    /// 部分跟随主题的文字颜色
    func klineLibDefaultTextColor()->UIColor
    /// 部分跟随主题的文字反转颜色
    func klineLibDefaultTextReversalColor()->UIColor
    /// k线图最高最小值的文字颜色
    func klineLibHihtLowPriceTextColor()->UIColor
    
    /// 部分跟随主题的副标题文字颜色
    func klineLibSubTextColor()->UIColor
    
    /// 划线工具线图颜色选择数组
    /// - Returns: -
    func drawLineColorList()->[UIColor]
    
    /// 三级文本
    /// - Returns: -
    func textTertiaryColor()->UIColor
    
    /// 分割线颜色
    /// - Returns: -
    func dividerColor()->UIColor
    
    /// 边框颜色
    /// - Returns: -
    func borderColor()->UIColor
    
    /// 橙色
    /// - Returns: -
    func orangeColor()->UIColor
    
    /// 按层级标准的一级背景
    /// - Returns: -
    func bgPrimary()->UIColor
    
    /// 卡片背景
    /// - Returns: -
    func bgCard()->UIColor
    
    /// 填充背景色（我也不晓得啥意思）
    /// - Returns: -
    func fillBgColor()->UIColor
    
    /// Tost Color 自定义不使用的原本的Tost的话 这个就没啥用
    /// - Returns: -
    func fillToastColor()->UIColor
  
  /// 文字灰色
  /// - Returns: -
  func textGreyColor()->UIColor
  
  /// 多个买卖点合并颜色
  /// - Returns: -
  func buyingAndSellingPointColor()->UIColor
  
  
  /// 均价颜色
  /// - Returns: -
  func klineAverageLineColor()->UIColor
  
  
  /// 买卖点矢量图
  /// - Returns: -
  func klineBuySellPointImage()->UIImage
}


extension ThemeColorProtocol{
    public func mainGreenColor()-> UIColor{
        switch KlineLibThemeManager.currentTheme {
        case .dark:
            return UIColor("#369562")
        case .light:
            return UIColor("#3EAB70")
        }
    }
    public func mainRedColor()->UIColor{
        switch KlineLibThemeManager.currentTheme {
        case .dark:
            return UIColor("#F6465D")
        case .light:
            return UIColor("#E33B54")
        }
    }
    public func mountainColor()->UIColor{
        UIColor("8A96A4")
    }
    
    public func mountainGradient()->[CGFloat]{
        [0, 0.05, 0.1, 0.4]
    }
    
    public func candleRiseColor()-> UIColor{
        UIColor("#E33B54")
    }
    
    public func candleDeclineColor()-> UIColor{
        UIColor("#2EBD85")
    }
    
    public func brokenLineColor()-> UIColor{
        UIColor("8A96A4")
    }
    
    public func maIndicatorColors()-> [UIColor] {
        [
            "#DD93E2", "#FCA744", "#6951FF", "#F15866",
            "#37B985", "#D6BC38", "#9AC5EC", "#F8CB6F",
            "#A83133", "#326AD5"
        ].map({UIColor($0)})
    }
    
    public func emaIndicatorColors()-> [UIColor]{
        [
            "#DD93E2", "#FCA744", "#6951FF", "#F15866",
            "#37B985", "#D6BC38", "#9AC5EC", "#F8CB6F",
            "#A83133", "#326AD5"
        ].map({UIColor($0)})
    }
    
    public func wmaIndicatorColors()-> [UIColor]{
        [
            "#DD93E2", "#FCA744", "#6951FF", "#F15866",
            "#37B985", "#D6BC38", "#9AC5EC", "#F8CB6F",
            "#A83133", "#326AD5"
        ].map({UIColor($0)})
    }
    public func bollIndicatorColors()-> [UIColor] {
        ["#085CFF","#E14CEB","#EFC900"].map({UIColor($0)})
    }
    
    public func closeIndicatorColors()-> [UIColor] {
        ["#EFC900"].map({UIColor($0)})
    }
    
    public func sarIndicatorColors()-> [UIColor]{
        ["#E14CEB"].map({UIColor($0)})
    }
    
    public func volIndicatorColors()-> [UIColor] {
        ["#E14CEB","#085CFF"].map({UIColor($0)})
    }
    
    public func macdIndicatorColors()-> [UIColor] {
        ["#E14CEB","#085CFF","#EFC900"].map({UIColor($0)})
    }
    
    public func kdjIndicatorColors()-> [UIColor] {
        ["#E14CEB","#085CFF","#EFC900"].map({UIColor($0)})
    }
    
    public func rsiIndicatorColors()-> [UIColor] {
        ["#E14CEB","#085CFF","#EFC900"].map({UIColor($0)})
    }
    
    public func wrIndicatorColors()-> [UIColor] {
        ["#E14CEB","#085CFF","#EFC900"].map({UIColor($0)})
    }
    
    public func obvIndicatorColors()-> [UIColor] {
        ["#E14CEB","#085CFF"].map({UIColor($0)})
    }
    
    public func rocIndicatorColors()-> [UIColor] {
        ["#E14CEB","#085CFF"].map({UIColor($0)})
    }
    
    public func cciIndicatorColors()-> [UIColor] {
        ["#E14CEB","#085CFF"].map({UIColor($0)})
    }
    
    public func stochRSIIndicatorColors()-> [UIColor] {
        ["#E14CEB","#085CFF"].map({UIColor($0)})
    }
    
    public func trixIndicatorColors()-> [UIColor] {
        ["#E14CEB","#085CFF"].map({UIColor($0)})
    }
    
    public func dmiIndicatorColors()-> [UIColor] {
        ["#085CFF","#E14CEB","#EFC900","#773BF7"].map({UIColor($0)})
    }
    
    public func klineLibDefaultBGColor()-> UIColor{
        switch KlineLibThemeManager.currentTheme {
        case .dark:
            return UIColor("#1B1F24")
        case .light:
            return UIColor("#FFFFFF")
        }
    }
    public func klineLibDefaultWhiteColor()->UIColor{
        return .white
    }
    public func klineLibDefaultTextColor()-> UIColor{
        switch KlineLibThemeManager.currentTheme {
        case .dark:
            return UIColor("#FFFFFF")
        case .light:
            return  UIColor("#0C0E11")
        }
        
    }
    public func klineLibDefaultTextReversalColor()-> UIColor{
        switch KlineLibThemeManager.currentTheme {
        case .dark:
            return  UIColor("#0C0E11")
        case .light:
            return UIColor("#FFFFFF")
        }
        
    }
    
    public func klineLibHihtLowPriceTextColor()-> UIColor{
        switch KlineLibThemeManager.currentTheme {
        case .dark:
            return  UIColor("#FFFFFF")
        case .light:
            return UIColor("#000000")
        }
        
    }
    public func klineLibSubTextColor()-> UIColor {
        UIColor("#818892")
    }
    
    public func drawLineColorList()->[UIColor]{
        return ["#F6465D","#E4B10C","#EFC900","#369562","#085CFF","#E14CEB"].map({UIColor($0)})
    }
    public func textTertiaryColor()->UIColor{
        switch KlineLibThemeManager.currentTheme {
        case .dark:
            UIColor("#4F555D")
        case .light:
            UIColor("#A6ABB3")
        }
    }
    public func dividerColor()->UIColor{
        switch KlineLibThemeManager.currentTheme {
        case .dark:
            UIColor("#22262B")
        case .light:
            UIColor("#EDEFF1")
        }
    }
    public func borderColor()->UIColor{
        switch KlineLibThemeManager.currentTheme {
        case .dark:
            UIColor("#272B31")
        case .light:
            UIColor("#E1E4E7")
        }
    }
    public func orangeColor()->UIColor{
        switch KlineLibThemeManager.currentTheme {
        case .dark:
            UIColor("#E4B10C")
        case .light:
            UIColor("#EFB90B")
        }
    }
    
    public func bgPrimary()->UIColor{
        
        switch KlineLibThemeManager.currentTheme {
        case .dark:
            UIColor("#0C0E11")
        case .light:
            UIColor("#FFFFFF")
        }
    }
    
    public func bgCard()->UIColor{
        switch KlineLibThemeManager.currentTheme {
        case .dark:
            UIColor("#1B1F24")
        case .light:
            UIColor("#FFFFFF")
        }
    }
    public func fillBgColor()->UIColor{
        switch KlineLibThemeManager.currentTheme {
        case .dark:
            UIColor("#1B1F24")
        case .light:
            UIColor("#F4F5F6")
        }
    }
    public func fillToastColor()->UIColor{
        switch KlineLibThemeManager.currentTheme {
        case .dark:
            UIColor("#22262B").withAlphaComponent(0.9)
        case .light:
            UIColor("#0C0E11").withAlphaComponent(0.9)
        }
    }
  public func textGreyColor()->UIColor{
    return UIColor.gray
  }
  public func buyingAndSellingPointColor()->UIColor{
    return .blue
  }
  public func klineAverageLineColor()->UIColor{
    return .yellow
  }
  public func klineBuySellPointImage()->UIImage{
    return UIImage(named: "buy_sell_point_svg") ?? UIImage()
  }
}
