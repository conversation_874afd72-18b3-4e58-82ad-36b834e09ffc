//
//  LocalizationTool.swift
//  beepay
//
//  Created by Mac2 on 2024/5/9.
//

import Foundation
public enum Language:String{
    case english = "en"
    case simplifiedChinese = "zh-<PERSON>"
}
public class LocalizationManager {
    public static let shared = LocalizationManager()
    
    private var currentBundle: Bundle = .module
    private var currentLanguage: String = "en"
    
    private init() {
        // 初始化时读取用户设置的语言
        if let preferredLanguage = UserDefaults.standard.stringArray(forKey: "AppleLanguages")?.first {
            setLanguage(language:preferredLanguage)
        }
    }
    public func setLanguageWith(language: Language) {
        setLanguage(language: language.rawValue)
    }
    private func setLanguage(language: String) {
        // 保存到UserDefaults
        UserDefaults.standard.set([language], forKey: "AppleLanguages")
        UserDefaults.standard.synchronize()
        
        // 重新加载Bundle
        reloadBundle(for: language)
        
        // 发送通知让界面刷新
        NotificationCenter.default.post(name: .languageDidChange, object: nil)
    }
    
    private func reloadBundle(for languageCode: String) {
        // 获取包内资源路径
        guard let path = Bundle.module.path(forResource: languageCode, ofType: "lproj"),
              let bundle = Bundle(path: path) else {
            // 如果找不到指定语言，回退到包默认语言
            currentBundle = .module
            currentLanguage = Bundle.module.preferredLocalizations.first ?? "en"
            return
        }
        
        currentBundle = bundle
        currentLanguage = languageCode
    }
    
    public func localizedString(forKey key: String, comment: String = "") -> String {
        return currentBundle.localizedString(forKey: key, value: comment, table: nil)
    }
    
    public func currentLocale() -> Locale {
        return Locale(identifier: currentLanguage)
    }
}

extension Notification.Name {
    public static let languageDidChange = Notification.Name("languageDidChange")
}

public extension String {
   
    func BPLocalized(_ default:String? = "",file:String? = "") -> String {
        
        return LocalizationManager.shared.localizedString(forKey: self)
    }
}
public extension String? {
   
    func BPLocalized(_ default:String? = "",file:String? = "") -> String? {
        guard let str = self else { return nil }
        return LocalizationManager.shared.localizedString(forKey: str)
    }
}
