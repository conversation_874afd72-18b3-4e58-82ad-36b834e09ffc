//
//  Timer+.swift
//  UseKlineToolsDemo
//
//  Created by <PERSON> on 2025/3/31.
//

import Foundation
// MARK: - 使用示例
/*
 
 class ExampleViewController {
 override func viewDidLoad() {
 super.viewDidLoad()
 
 // 1. 添加普通定时器
 TimerTool.addTimeObserver(target: self, duration: 1) { [weak self] timer in
 // 每秒执行一次
 self?.updateUI()
 }
 
 // 2. 添加带唯一键的定时器
 TimerTool.addTimeObserver(target: self,
 uniqueKey: "CountdownTimer",
 duration: 1) { [weak self] timer in
 // 每秒执行一次
 self?.updateCountdown()
 }
 }
 
 override func viewWillDisappear(_ animated: Bool) {
 super.viewWillDisappear(animated)
 
 // 3. 移除普通定时器
 TimerTool.removeTimeObserver(target: self)
 
 // 4. 移除带唯一键的定时器
 TimerTool.removeTimeObserver(target: self, uniqueKey: "CountdownTimer")
 }
 }
 
 */
/// 定时器管理类
public class TimerTool {
    /// 单例
    private static let shared = TimerTool()
    
    /// 定时器字典 [ObjectIdentifier: (Timer, String?)]
    private var timerDict: [ObjectIdentifier: (Timer, String?)] = [:]
    
    /// 私有初始化方法
    private init() {}
    
    /// 添加定时观察者
    /// - Parameters:
    ///   - target: 目标对象
    ///   - duration: 时间间隔（秒）
    ///   - handler: 定时回调
    public static func addTimeObserver(target: AnyObject,
                                       duration: TimeInterval = 1,
                                       handler: @escaping (Timer) -> Void) {
        addTimeObserver(target: target, uniqueKey: nil, duration: duration, handler: handler)
    }
    
    /// 添加定时观察者（带唯一键）
    /// - Parameters:
    ///   - target: 目标对象
    ///   - uniqueKey: 唯一键
    ///   - duration: 时间间隔（秒）
    ///   - handler: 定时回调
    public static func addTimeObserver(target: AnyObject,
                                       uniqueKey: String?,
                                       duration: TimeInterval = 1,
                                       handler: @escaping (Timer) -> Void) {
        let identifier = ObjectIdentifier(target)
        
        // 如果已存在定时器，先移除
        if let (existingTimer, _) = shared.timerDict[identifier] {
            existingTimer.invalidate()
        }
        
        // 创建新定时器
        let timer = Timer.scheduledTimer(withTimeInterval: duration, repeats: true) { timer in
            handler(timer)
        }
        
        // 添加到定时器字典
        shared.timerDict[identifier] = (timer, uniqueKey)
    }
    
    /// 移除定时观察者
    /// - Parameter target: 目标对象
    public static func removeTimeObserver(target: AnyObject) {
        let identifier = ObjectIdentifier(target)
        if let (timer, _) = shared.timerDict[identifier] {
            timer.invalidate()
            shared.timerDict.removeValue(forKey: identifier)
        }
    }
    
    /// 移除定时观察者（带唯一键）
    /// - Parameters:
    ///   - target: 目标对象
    ///   - uniqueKey: 唯一键
    public static func removeTimeObserver(target: AnyObject, uniqueKey: String) {
        let identifier = ObjectIdentifier(target)
        if let (timer, storedKey) = shared.timerDict[identifier], storedKey == uniqueKey {
            timer.invalidate()
            shared.timerDict.removeValue(forKey: identifier)
        }
    }
    
    /// 移除所有定时观察者
    public static func removeAllTimeObservers() {
        for (_, (timer, _)) in shared.timerDict {
            timer.invalidate()
        }
        shared.timerDict.removeAll()
    }
    
    /// 析构时清理所有定时器
    deinit {
        TimerTool.removeAllTimeObservers()
    }
}
