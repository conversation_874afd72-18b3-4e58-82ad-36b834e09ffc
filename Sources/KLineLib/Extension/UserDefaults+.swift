//
//  UserDefaults+.swift
//  UseKlineToolsDemo
//
//  Created by <PERSON> on 2025/3/31.
//

import Foundation


extension UserDefaults {
    
    /// 存K线类型数据
    static public let CachedKLineTypeKey = "CachedKLineTypeKey"
    static public var cachedKLineType: KLineType {
        get {
            guard let value = UDTool.value(key: CachedKLineTypeKey) as? String else { return .professional }
            guard let type = Int(value) else { return .professional }
            return KLineType(rawValue: type) ?? .professional
        }
        set {
            UDTool.setValue(value: "\(newValue.rawValue)", forKey: CachedKLineTypeKey)
        }
    }
    
    /// 存K线主视图指标数据
    static public let CachedKLineMainTypeKey = "CachedKLineMainTypeKey"
    static public var cachedKLineMainType: [KLineMainViewType] {
        get {
            // 老数据
            if let value = UDTool.value(key: CachedKLineMainTypeKey) as? String, let type = Int(value) {
                return [KLineMainViewType(rawValue: type) ?? .ma]
            }
            if let list = UDTool.value(key: CachedKLineMainTypeKey) as? [Int] {
                var retList: [KLineMainViewType] = []
                for item in list {
                    guard let type = KLineMainViewType(rawValue: item) else { continue }
                    retList.append(type)
                }
                return retList
            }
            return [.ma]
        }
        set {
            let list: [Int] = newValue.map { $0.rawValue }
            UDTool.setValue(value: list, forKey: CachedKLineMainTypeKey)
        }
    }
    
    /// 存K线第一个副视图指标数据
    private static let CachedKLineSubTypeKeyOne = "CachedKLineSubTypeKeyOne"
    private static var cachedKLineSubTypeOne: KLineSubViewType {
        guard let value = UDTool.value(key: CachedKLineSubTypeKeyOne) as? String else { return .close }
        guard let type = Int(value) else { return .close }
        return KLineSubViewType(rawValue: type) ?? .close
    }
    
    /// 存K线第二个副视图指标数据
    private static let CachedKLineSubTypeKeyTwo = "CachedKLineSubTypeKeyTwo"
    private static var cachedKLineSubTypeTwo: KLineSubViewType {
        guard let value = UDTool.value(key: CachedKLineSubTypeKeyTwo) as? String else { return .close }
        guard let type = Int(value) else { return .close }
        return KLineSubViewType(rawValue: type) ?? .close
    }
    
    /// 存K线第三个副视图指标数据
    private static let CachedKLineSubTypeKeyThree = "CachedKLineSubTypeKeyThrow"
    private static var cachedKLineSubTypeThree: KLineSubViewType {
        guard let value = UDTool.value(key: CachedKLineSubTypeKeyThree) as? String else { return .close }
        guard let type = Int(value) else { return .close }
        return KLineSubViewType(rawValue: type) ?? .close
    }
    
    /// 存K线副视图指标数组
    static public let CachedKLineSubTypeKeyList = "CachedKLineSubTypeKeyList"
    static public let CachedKLineSubTypeKeyCount = 4
    static public var cachedKLineSubTypeList: [KLineSubViewType] {
        get {
            guard let value = UDTool.value(key: CachedKLineSubTypeKeyList) as? [Int] else {
                /*
                 默认vol选中，为了兼容老版本，需要把老版本数据添加进来
                 */
                var ret: [KLineSubViewType] = []
                let subType1 = cachedKLineSubTypeOne
                let subType2 = cachedKLineSubTypeTwo
                let subType3 = cachedKLineSubTypeThree
                if subType1 != .close { ret.append(subType1) }
                if subType2 != .close { ret.append(subType2) }
                if subType3 != .close { ret.append(subType3) }
                ret.append(.vol)
                return ret
            }
            var list: [KLineSubViewType] = []
            for item in value {
                if let type = KLineSubViewType(rawValue: item) {
                    list.append(type)
                }
            }
            return list
        }
        set {
            let list: [Int] = newValue.map { $0.rawValue }
            UDTool.setValue(value: list, forKey: CachedKLineSubTypeKeyList)
        }
    }
    
    /// 存K线点击展示样式
    static public let KLineTapViewTypeKey = "KLineTapViewTypeKey"
    static public var cachedKLineTapViewType: KLineTapViewType {
        get {
            guard let value = UDTool.value(key: KLineTapViewTypeKey) as? String else { return .pop }
            guard let type = Int(value) else { return .pop }
            return KLineTapViewType(rawValue: type) ?? .pop
        }
        set {
            UDTool.setValue(value: "\(newValue.rawValue)", forKey: KLineTapViewTypeKey)
        }
    }
    
}
