//
//  UIView+Draw.swift
//  KLineLib
//
//  Created by <PERSON> on 2025/4/14.
//

import Foundation
import UIKit

extension UIView{
    /// 绘制网格
    public func drawGrid(context: CGContext, rect: CGRect, isMainView:Bool = false) {
        // 设置View的背景颜色
        context.clear(rect)
        context.setFillColor(UIColor.clear.cgColor)
        context.fill(rect)
        
        // 绘制 Y轴参考线
        let width = rect.size.width
        let height = rect.size.height
        let step = height / CGFloat(isMainView ? KLineConfig.verticalLineCount : 1)
        context.setLineWidth(0.5)
        let color = KLineConfig.themeManager.dividerColor()
        context.setStrokeColor(color.cgColor)
        
        for index in 0...KLineConfig.horizontalLineCount {
            let between: Array<CGPoint> = [
                CGPoint(x: 0, y: CGFloat(index) * step),
                CGPoint(x: width, y: CGFloat(index) * step)
            ]
            context.strokeLineSegments(between: between)
        }
        // 绘制 X轴参考线
        /* 因为需要时间 和竖线对对齐 所以竖线不在这里绘制了 在绘制时间的地方绘制竖线
        step = width / CGFloat(KLineConfig.verticalLineCount)
        
        for index in 1..<KLineConfig.horizontalLineCount {
            let between: Array<CGPoint> = [
                CGPoint(x: CGFloat(index) * step, y: 0),
                CGPoint(x: CGFloat(index) * step, y: height)
            ]
            context.strokeLineSegments(between: between)
        }
        */
    }
    
    
    /// 绘制网格for customGridModel
    public func drawGrid(context: CGContext,rect: CGRect,model:CustomGridModel ,isMainView:Bool = false) {
        guard model.keyPoints.count >= 2 else {return}
        // 设置View的背景颜色
        context.clear(rect)
        context.setFillColor(UIColor.clear.cgColor)
        context.fill(rect)
        
        // 绘制 Y轴参考线
        let width = rect.size.width
        let height = rect.size.height
        let step = height / CGFloat(isMainView ? model.horizontalLineCount : 1)
        context.setLineWidth(0.5)
        
        
        let color = model.dividerColor
        context.setStrokeColor(color.cgColor)
        
        for index in 0...model.horizontalLineCount {
            let between: [CGPoint] = [
                CGPoint(x: 0, y: CGFloat(index) * step),
                CGPoint(x: width, y: CGFloat(index) * step)
            ]
            context.strokeLineSegments(between: between)
        }
        // 绘制 X轴参考线
        //        step = width / CGFloat(KLineConfig.horizontalLineCount)
        
        let xpisitions = model.calculateXposition(width: width)
        xpisitions.forEach { x in
            let between: [CGPoint] = [
                CGPoint(x: x, y: 0),
                CGPoint(x: x, y: height)
            ]
            context.strokeLineSegments(between: between)
        }
    }
    public func drawPriceWith(rect: CGRect,model:CustomGridModel?,maxValue: Double,minValue: Double,scale: Int,previousDayClosePrice:Double?){
        
        guard let _ = UIGraphicsGetCurrentContext() else{return}
        let minY = KLineConfig.kLineMainViewMinY
        let maxY = rect.height - KLineConfig.kLineMainViewMaxY
        var unitValue:CGFloat = 1.0
        
        
        
        if maxY != minY && maxValue != minValue {
            unitValue = (maxValue - minValue) / CGFloat((maxY - minY))
        }
        
        let yAxisCount = model?.horizontalLineCount ?? KLineConfig.horizontalLineCount
        
        
        let tempMaxValue = (maxY - 1) * unitValue + minValue
        let tempMinValue = -KLineConfig.kLineMainViewMaxY * unitValue + minValue
        let priceStep = (tempMaxValue - tempMinValue) / CGFloat(yAxisCount)
        var priceList:[String] = []
        for index in 0...yAxisCount{
            let value = ShowNumber(String(tempMaxValue - priceStep * CGFloat(index)), .halfFullUp(scale))
            priceList.append(value)
        }
        
        
        
        let step = rect.height / CGFloat(yAxisCount)
        
        for index in 0...yAxisCount {
            let between: [CGPoint] = [
                CGPoint(x: 0, y: CGFloat(index) * step),
                CGPoint(x: width, y: CGFloat(index) * step)
            ]
          var labColor = KLineConfig.themeManager.klineLibSubTextColor()
          if KLineConfig.mainViewYaxisValueUseColor == true,let previousDayClosePrice = previousDayClosePrice{
            let target = priceList[index].replacingOccurrences(of: ",", with: "").doubleValue()
            if target > previousDayClosePrice{
              labColor = KLineConfig.themeManager.candleDeclineColor()
            }else if target < previousDayClosePrice{
              labColor = KLineConfig.themeManager.candleRiseColor()
            }
          }
            // swiftlint:disable:next all
            let dateStr = NSString(string: priceList[index])
            let font = UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular)
            let strHeight = dateStr.description.textHeight(font: font, maxWidth: rect.width)
            let strwidth = dateStr.description.textWidth(font: font, maxHeight: strHeight)
            let attr: [NSAttributedString.Key: Any] = [
                .font: font,
                .foregroundColor: labColor,
                //.backgroundColor:KLineConfig.themeManager.bgPrimary()
            ]
            var point =  between.first!
            switch KLineConfig.pricePosition{
            case .left(let offset):
                point.x += offset
            case .right(let offset):
                point = between.last!
                point.x -= (offset + strwidth)
            }
            switch index{
            case 0:
                break
            case yAxisCount:
                point.y -= strHeight
            default:
                point.y -= strHeight / 2.0
            }
            
            dateStr.draw(at: point, withAttributes: attr)
          
          //MARK: Michael: 添加涨跌幅
            if KLineConfig.displayMainViewYaxisChangeRate == true,let previousDayClosePrice = previousDayClosePrice{
            let target = priceList[index].replacingOccurrences(of: ",", with: "").doubleValue()
              var chgStr:NSString = NSString()
              if target > previousDayClosePrice{
                let value =  (target - previousDayClosePrice) /  previousDayClosePrice * 100.0
                chgStr = NSString(format: "+%.2f%%", value)
              }else if target < previousDayClosePrice{
                let value =  (previousDayClosePrice - target) /  previousDayClosePrice * 100.0
                chgStr = NSString(format: "-%.2f%%", value)
              }else{
                chgStr = NSString(string: "0.00%")
              }
              let width = chgStr.description.textWidth(font: font, maxHeight: 20)
              
              
              var rightPoint = CGPoint(x: rect.width - width, y: point.y)
              switch KLineConfig.pricePosition{
              case .left(let offset):
                break
              case .right(let offset):
                rightPoint = CGPoint(x: 0, y: point.y)
              }
              chgStr.draw(at: rightPoint, withAttributes: attr)
          }
          
          
          
        }
    }
}



extension UIView {
    /// 绘制时间
    public func drawTime(_ pointY: CGFloat, _ width: CGFloat, _ kLineModels: [KLineModel]) {
        
        guard let context = UIGraphicsGetCurrentContext() else { return }
        
        for kLineModel in kLineModels {
            let time = TimeInterval(kLineModel.timestamp)
            guard kLineModel.isShowTime,
                  let positionModel = kLineModel.positionModel else { continue }
            
            let font = UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular)
            var dateFormat = KLineConfig.timeType.dateFormat
            if KLineConfig.type == .fiveDayLine {
                dateFormat = "MM/dd"
            }
            let dateString = time.timestampToDateString(format: dateFormat)
            var point = CGPoint(x: positionModel.openPoint.x, y: pointY)
            let dateStringWidth = dateString.textWidth(font: font, maxHeight: KLineConfig.kLineTimeViewHeight)
            
            if KLineConfig.kLineTimeViewWithBottomView == .mainViewBottom {
                let tempPointY = pointY + 6.5 - KLineConfig.kLineTimeViewHeight / 2.0
                let between: Array<CGPoint> = [
                    CGPoint(x:point.x, y: 0),
                    CGPoint(x: point.x, y: tempPointY),
                    CGPoint(x: point.x, y: tempPointY + KLineConfig.kLineTimeViewHeight ),
                    CGPoint(x: point.x, y: height)
                ]
                let toppath = UIBezierPath()
                toppath.move(to: between[0])
                toppath.addLine(to: between[1])
                context.addPath(toppath.cgPath)
                
                let bottomPath = UIBezierPath()
                bottomPath.move(to: between[2])
                bottomPath.addLine(to: between[3])
                context.addPath(bottomPath.cgPath)
            } else {
                let between: Array<CGPoint> = [
                    CGPoint(x:point.x, y: 0),
                    CGPoint(x: point.x, y: height - KLineConfig.kLineTimeViewHeight)
                ]
                let path = UIBezierPath()
                path.move(to: between[0])
                path.addLine(to: between[1])
                context.addPath(path.cgPath)
            }
            
            context.setLineWidth(0.5)
            let color = KLineConfig.themeManager.dividerColor()
            context.setStrokeColor(color.cgColor)
            context.strokePath()
            
            // 让时间偏移宽度的一半 让其中心 和 竖线对齐
            point.x -= dateStringWidth / 2.0
            if KLineConfig.displayAllData || KLineConfig.type == .fiveDayLine {
                point.x = max(point.x, 0)
                // 解决最后一个时间显示不完整的情况
                if dateStringWidth + point.x >= width {
                    point.x = width - dateStringWidth
                }
            }
            
            let dateStr = NSString(string: dateString)
            let attr: [NSAttributedString.Key: Any] = [
                .font: font,
                .foregroundColor: KLineConfig.themeManager.klineLibSubTextColor()
            ]
            dateStr.draw(at: point, withAttributes: attr)
        }
    }
    
    
    /// 绘制时间
    public func drawTime(pointY: CGFloat,width: CGFloat,model:CustomGridModel) {
        // 绘制 X轴参考线
        let font = UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular)
        let resultModels = model.calculateSubModels(width: width)
        for i in 0..<resultModels.count{
            let model = resultModels[i]
            var temptimeStr = model.timeStr
            var x = model.x
            switch model.type {
            case .normal:
                temptimeStr = model.timeStr
            case .critical:
                let afterList = resultModels.dropFirst(i + 1)
                let beforList = resultModels.dropLast(resultModels.count - i)
                
                if let next = afterList.first(where: {$0.type == .critical && floor($0.x) == floor(model.x) }){
                    temptimeStr = "\(model.timeStr)/\(next.timeStr)"
                }else if let _ = beforList.first(where: {$0.type == .critical && floor($0.x) == floor(model.x) }){
                    temptimeStr = ""
                }
                
            case .discard:
                temptimeStr = ""
            }
            let strWidth = temptimeStr.textWidth(font: font, maxHeight: KLineConfig.kLineTimeViewHeight)
            switch i{
            case 0:
                break
            case resultModels.count - 1:
                x -= strWidth
            default:
                x -= strWidth/2.0
                break
            }
            
            
            let dateStr = NSString(string: temptimeStr)
            let attr: [NSAttributedString.Key: Any] = [
                .font: (font),
                .foregroundColor: KLineConfig.themeManager.klineLibSubTextColor()
            ]
            dateStr.draw(at: CGPoint(x: x, y: pointY), withAttributes: attr)
        }
        /*
         let times = model.keyPoints.compactMap({$0.timeIntervalSince1970 * 1000}).sorted(by: {$0 < $1})
         let font = UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular)
         let xpisitions = model.calculateXposition(width: width)
         
        for index in 0..<model.keyPoints.count {
            var timeStr = times[index].timestampToDateString(format: model.dateFormat)
            let strWidth = timeStr.textWidth(font: font, maxHeight: KLineConfig.kLineTimeViewHeight)
            if let _ = model.needHiddenIndexs.first(where: {$0 == index}){
                timeStr = ""
            }
            var x = xpisitions[index]
            switch index{
            case 0:
                break
            case model.keyPoints.count - 1:
                x -= strWidth
            default:
                x -= strWidth/2.0
                break
            }
            print("timeStr is \(timeStr) x is \(x)")
            let dateStr = NSString(string: timeStr)
            let attr: [NSAttributedString.Key: Any] = [
                .font: (font),
                .foregroundColor: KLineConfig.themeManager.klineLibSubTextColor()
            ]
            dateStr.draw(at: CGPoint(x: x, y: pointY), withAttributes: attr)
        }
        */
        // swiftlint:disable:next all
        
    }
}


extension UIView {
    /// 绘制柱子
    /// 副视图中的柱子
    public func drawColumn(context: CGContext,
                           rect: CGRect,
                           positionArray: [[CGPoint]],
                           color: [UIColor],
                           lineWidth: CGFloat) {
        if positionArray.count < 1 { return }
        var index = 0
        let templineWidth = calculateLineWidth(oldlineWidth: lineWidth, contentWidth: rect.width, count: positionArray.count)
        
        for solidPoints in positionArray {
            // 画中间较宽的线段-实体线
            context.setLineWidth(templineWidth)
            // 设置画笔颜色
            context.setStrokeColor(color[index].cgColor)
            // 画线
            context.strokeLineSegments(between: solidPoints)
            index += 1
        }
    }
    
    /// 绘制折线图
    /// 所有线条用的都是这个方法
    public func drawLineWith(context: CGContext,
                             rect: CGRect,
                             positionArray: [CGPoint],
                             color: UIColor,
                             width: CGFloat,
                             gradient: Bool,
                             curves: Bool = true) {
        if positionArray.count < 2 || width <= 0 { return }
        context.setStrokeColor(color.cgColor)
        context.setLineWidth(width)
        let nCurves = positionArray.count - 1
        let path: UIBezierPath = UIBezierPath()
        
        for i in 0..<nCurves {
            let value: CGPoint = positionArray[i]
            
            var curPt = CGPointZero,
                prevPt = CGPointZero,
                nextPt = CGPointZero,
                endPt = CGPointZero
            curPt = value
            
            if i == 0 {
                path.move(to: curPt)
            }
            
            var nexti = (i + 1) % positionArray.count
            var previ = (i - 1 < 0 ? positionArray.count - 1 : i - 1)
            
            prevPt = positionArray[previ]
            
            nextPt = positionArray[nexti]
            
            endPt = nextPt
            
            var ob, my: CGFloat
            
            if i > 0 {
                ob = (nextPt.x - curPt.x) * 0.5 + (curPt.x - prevPt.x) * 0.5
                my = (nextPt.y - curPt.y) * 0.5 + (curPt.y - prevPt.y) * 0.5
            } else {
                ob = (nextPt.x - curPt.x) * 0.5
                my = (nextPt.y - curPt.y) * 0.5
            }
            
            if curves == true {
                var ctrlPt1 = CGPointZero
                ctrlPt1.x = curPt.x + ob / 3.0
                ctrlPt1.y = curPt.y + my / 3.0
                curPt = positionArray[nexti]
                
                nexti = (nexti + 1) % positionArray.count
                previ = i
                
                prevPt = positionArray[previ]
                
                nextPt = positionArray[nexti]
                
                if i < nCurves - 1 {
                    ob = (nextPt.x - curPt.x) * 0.5 + (curPt.x - prevPt.x) * 0.5
                    my = (nextPt.y - curPt.y) * 0.5 + (curPt.y - prevPt.y) * 0.5
                } else {
                    ob = (curPt.x - prevPt.x) * 0.5
                    my = (curPt.y - prevPt.y) * 0.5
                }
                
                var ctrlPt2: CGPoint = .zero
                ctrlPt2.x = curPt.x - ob / 3.0
                ctrlPt2.y = curPt.y - my / 3.0
                path.addCurve(to: endPt, controlPoint1: ctrlPt1, controlPoint2: ctrlPt2)
            } else {
                path.addLine(to: endPt)
            }
        }
        context.addPath(path.cgPath)
        
        if gradient {
            context.strokePath()
            // 画背景色渐变和阴影
            // swiftlint:disable:next all
            let lastPoint: CGPoint = positionArray.last!
            // swiftlint:disable:next all
            let firstPoint: CGPoint = positionArray.first!
            let height: CGFloat = rect.height
            path.addLine(to: CGPoint(x: lastPoint.x, y: height))
            path.addLine(to: CGPoint(x: firstPoint.x, y: height))
            path.addLine(to: firstPoint)
            context.addPath(path.cgPath)
//            context.setShadow(offset: CGSize(width: -4.0, height: -4.0), blur: 10.0, color: UIColor.gray.cgColor)
            path.addClip()
            
            // 获得一个CGRect
            let clip: CGRect = path.bounds
            
            // 剪切到合适的大小
            let beginColor: UIColor = color.withAlphaComponent(0.0)
            let midTopColor: UIColor = color.withAlphaComponent(0.05)
            let midDownColor: UIColor = color.withAlphaComponent(0.1)
            let endColor: UIColor = color.withAlphaComponent(0.01)
            let colorArray = [beginColor, midTopColor, midDownColor, endColor]
            let locationArray = KLineConfig.themeManager.mountainGradient()
            let startPoint = CGPoint(x: clip.minX, y: clip.minY)
            let endPoint = CGPoint(x: clip.minX, y: clip.maxY)
            let space: CGColorSpace = CGColorSpaceCreateDeviceRGB()
            
            // Convert NSNumber *locations array to CGFloat *
            var locations: [CGFloat] = []
            
            for item in locationArray {
                locations.append(CGFloat(fminf(fmaxf(Float(item), 0), 1)))
            }
            
            // Convert colors array to (id) CGColorRef
            var colorRefArray: [CGColor] = []
            
            for color in colorArray {
                colorRefArray.append(color.cgColor)
            }
            
            // Build the internal gradient
            guard let gradient = CGGradient(colorsSpace: space,
                                            colors: colorRefArray as CFArray,
                                            locations: locations) else { return }
            context.drawLinearGradient(gradient,
                                       start: startPoint,
                                       end: endPoint,
                                       options: CGGradientDrawingOptions.drawsBeforeStartLocation)
        }
        context.strokePath()
    }
    
  public func drawFiveDaysVwapLineWith(context: CGContext,
                           rect: CGRect,
                           positionArrays: [[CGPoint]],
                           color: UIColor,
                           width: CGFloat) {
      if positionArrays.count < 1 || width <= 0 { return }
      context.setStrokeColor(color.cgColor)
      context.setLineWidth(width)
    positionArrays.forEach { positionArray in
      let nCurves = positionArray.count - 1
      let path: UIBezierPath = UIBezierPath()
      
      for i in 0..<nCurves {
          let value: CGPoint = positionArray[i]
          
          var curPt = CGPointZero,
              prevPt = CGPointZero,
              nextPt = CGPointZero,
              endPt = CGPointZero
          curPt = value
          
          if i == 0 {
              path.move(to: curPt)
          }
          
          var nexti = (i + 1) % positionArray.count
          var previ = (i - 1 < 0 ? positionArray.count - 1 : i - 1)
          
          prevPt = positionArray[previ]
          
          nextPt = positionArray[nexti]
          
          endPt = nextPt
          
          var ob, my: CGFloat
          
          if i > 0 {
              ob = (nextPt.x - curPt.x) * 0.5 + (curPt.x - prevPt.x) * 0.5
              my = (nextPt.y - curPt.y) * 0.5 + (curPt.y - prevPt.y) * 0.5
          } else {
              ob = (nextPt.x - curPt.x) * 0.5
              my = (nextPt.y - curPt.y) * 0.5
          }
          
              var ctrlPt1 = CGPointZero
              ctrlPt1.x = curPt.x + ob / 3.0
              ctrlPt1.y = curPt.y + my / 3.0
              curPt = positionArray[nexti]
              
              nexti = (nexti + 1) % positionArray.count
              previ = i
              
              prevPt = positionArray[previ]
              
              nextPt = positionArray[nexti]
              
              if i < nCurves - 1 {
                  ob = (nextPt.x - curPt.x) * 0.5 + (curPt.x - prevPt.x) * 0.5
                  my = (nextPt.y - curPt.y) * 0.5 + (curPt.y - prevPt.y) * 0.5
              } else {
                  ob = (curPt.x - prevPt.x) * 0.5
                  my = (curPt.y - prevPt.y) * 0.5
              }
              
              var ctrlPt2: CGPoint = .zero
              ctrlPt2.x = curPt.x - ob / 3.0
              ctrlPt2.y = curPt.y - my / 3.0
              path.addCurve(to: endPt, controlPoint1: ctrlPt1, controlPoint2: ctrlPt2)
          
      }
      context.addPath(path.cgPath)
      context.strokePath()
    }
      
  }
    /// 绘制折线图
    /// 所有线条用的都是这个方法
    public func drawFiveDayLineWith(context: CGContext,
                                    rect: CGRect,
                                    positionArray: [CGPoint],
                                    color: UIColor,
                                    width: CGFloat,
                                    gradient: Bool,
                                    curves: Bool = true) {
        if positionArray.count < 2 || width <= 0 { return }
        context.setStrokeColor(color.cgColor)
        context.setLineWidth(width)
        let nCurves = positionArray.count - 1
        let path: UIBezierPath = UIBezierPath()
        
        for i in 0..<nCurves {
            let value: CGPoint = positionArray[i]
            
            var curPt = CGPointZero,
                prevPt = CGPointZero,
                nextPt = CGPointZero,
                endPt = CGPointZero
            curPt = value
            
            if i == 0 {
                path.move(to: curPt)
            }
            
            var nexti = (i + 1) % positionArray.count
            var previ = (i - 1 < 0 ? positionArray.count - 1 : i - 1)
            
            prevPt = positionArray[previ]
            
            nextPt = positionArray[nexti]
            
            endPt = nextPt
            
            var ob, my: CGFloat
            
            if i > 0 {
                ob = (nextPt.x - curPt.x) * 0.5 + (curPt.x - prevPt.x) * 0.5
                my = (nextPt.y - curPt.y) * 0.5 + (curPt.y - prevPt.y) * 0.5
            } else {
                ob = (nextPt.x - curPt.x) * 0.5
                my = (nextPt.y - curPt.y) * 0.5
            }
            
            if curves == true {
                var ctrlPt1 = CGPointZero
                ctrlPt1.x = curPt.x + ob / 3.0
                ctrlPt1.y = curPt.y + my / 3.0
                curPt = positionArray[nexti]
                
                nexti = (nexti + 1) % positionArray.count
                previ = i
                
                prevPt = positionArray[previ]
                
                nextPt = positionArray[nexti]
                
                if i < nCurves - 1 {
                    ob = (nextPt.x - curPt.x) * 0.5 + (curPt.x - prevPt.x) * 0.5
                    my = (nextPt.y - curPt.y) * 0.5 + (curPt.y - prevPt.y) * 0.5
                } else {
                    ob = (curPt.x - prevPt.x) * 0.5
                    my = (curPt.y - prevPt.y) * 0.5
                }
                
                var ctrlPt2: CGPoint = .zero
                ctrlPt2.x = curPt.x - ob / 3.0
                ctrlPt2.y = curPt.y - my / 3.0
                path.addCurve(to: endPt, controlPoint1: ctrlPt1, controlPoint2: ctrlPt2)
            } else {
                path.addLine(to: endPt)
            }
        }
        context.addPath(path.cgPath)
        
        if gradient {
            context.strokePath()
            // 画背景色渐变和阴影
            // swiftlint:disable:next all
            let lastPoint: CGPoint = positionArray.last!
            // swiftlint:disable:next all
            let firstPoint: CGPoint = positionArray.first!
            let height: CGFloat = rect.height
            path.addLine(to: CGPoint(x: lastPoint.x, y: height))
            path.addLine(to: CGPoint(x: firstPoint.x, y: height))
            path.addLine(to: firstPoint)
            context.addPath(path.cgPath)
            path.addClip()
            
            // 获得一个CGRect
            let clip: CGRect = path.bounds
            
            // 剪切到合适的大小
            let beginColor: UIColor = color.withAlphaComponent(0.0)
            let midTopColor: UIColor = color.withAlphaComponent(0.05)
            let midDownColor: UIColor = color.withAlphaComponent(0.1)
            let endColor: UIColor = color.withAlphaComponent(0.01)
            let colorArray = [beginColor, midTopColor, midDownColor, endColor]
            let locationArray = KLineConfig.themeManager.mountainGradient()
            let startPoint = CGPoint(x: clip.minX, y: clip.minY)
            let endPoint = CGPoint(x: clip.minX, y: clip.maxY)
            let space: CGColorSpace = CGColorSpaceCreateDeviceRGB()
            
            // Convert NSNumber *locations array to CGFloat *
            var locations: [CGFloat] = []
            
            for item in locationArray {
                locations.append(CGFloat(fminf(fmaxf(Float(item), 0), 1)))
            }
            
            // Convert colors array to (id) CGColorRef
            var colorRefArray: [CGColor] = []
            
            for color in colorArray {
                colorRefArray.append(color.cgColor)
            }
            
            // Build the internal gradient
            guard let gradient = CGGradient(colorsSpace: space,
                                            colors: colorRefArray as CFArray,
                                            locations: locations) else { return }
            context.drawLinearGradient(gradient,
                                       start: startPoint,
                                       end: endPoint,
                                       options: CGGradientDrawingOptions.drawsBeforeStartLocation)
        }
        context.strokePath()
    }
    
  ///绘制分段五日分时
  public func drawFiveDayLineWith(context: CGContext,
                                  rect: CGRect,
                                  positionArrays: [[CGPoint]],
                                  color: UIColor,
                                  width: CGFloat,
                                  gradient: Bool = true,
                                  curves: Bool = true) {
    if positionArrays.count < 1 || width <= 0 { return }
    context.setStrokeColor(color.cgColor)
    context.setLineWidth(width)
    
    positionArrays.forEach { positionArray in
      // 保存状态
      context.saveGState()
      let nCurves = positionArray.count - 1
      let path: UIBezierPath = UIBezierPath()
      
      for i in 0..<nCurves {
        let value: CGPoint = positionArray[i]
        
        var curPt = CGPointZero,
            prevPt = CGPointZero,
            nextPt = CGPointZero,
            endPt = CGPointZero
        curPt = value
        
        if i == 0 {
          path.move(to: curPt)
        }
        
        var nexti = (i + 1) % positionArray.count
        var previ = (i - 1 < 0 ? positionArray.count - 1 : i - 1)
        
        prevPt = positionArray[previ]
        
        nextPt = positionArray[nexti]
        
        endPt = nextPt
        
        var ob, my: CGFloat
        
        if i > 0 {
          ob = (nextPt.x - curPt.x) * 0.5 + (curPt.x - prevPt.x) * 0.5
          my = (nextPt.y - curPt.y) * 0.5 + (curPt.y - prevPt.y) * 0.5
        } else {
          ob = (nextPt.x - curPt.x) * 0.5
          my = (nextPt.y - curPt.y) * 0.5
        }
        
        if curves == true {
          var ctrlPt1 = CGPointZero
          ctrlPt1.x = curPt.x + ob / 3.0
          ctrlPt1.y = curPt.y + my / 3.0
          curPt = positionArray[nexti]
          
          nexti = (nexti + 1) % positionArray.count
          previ = i
          
          prevPt = positionArray[previ]
          
          nextPt = positionArray[nexti]
          
          if i < nCurves - 1 {
            ob = (nextPt.x - curPt.x) * 0.5 + (curPt.x - prevPt.x) * 0.5
            my = (nextPt.y - curPt.y) * 0.5 + (curPt.y - prevPt.y) * 0.5
          } else {
            ob = (curPt.x - prevPt.x) * 0.5
            my = (curPt.y - prevPt.y) * 0.5
          }
          
          var ctrlPt2: CGPoint = .zero
          ctrlPt2.x = curPt.x - ob / 3.0
          ctrlPt2.y = curPt.y - my / 3.0
          path.addCurve(to: endPt, controlPoint1: ctrlPt1, controlPoint2: ctrlPt2)
        } else {
          path.addLine(to: endPt)
        }
      }
      
      context.addPath(path.cgPath)
      if gradient {
        context.strokePath()
        // 画背景色渐变和阴影
        // swiftlint:disable:next all
        let lastPoint: CGPoint = positionArray.last!
        // swiftlint:disable:next all
        let firstPoint: CGPoint = positionArray.first!
        let height: CGFloat = rect.height
        path.addLine(to: CGPoint(x: lastPoint.x, y: height))
        path.addLine(to: CGPoint(x: firstPoint.x, y: height))
        path.addLine(to: firstPoint)
        context.addPath(path.cgPath)
        path.addClip()
        
        // 获得一个CGRect
        let clip: CGRect = path.bounds
        
        // 剪切到合适的大小
        let beginColor: UIColor = color.withAlphaComponent(0.0)
        let midTopColor: UIColor = color.withAlphaComponent(0.05)
        let midDownColor: UIColor = color.withAlphaComponent(0.1)
        let endColor: UIColor = color.withAlphaComponent(0.01)
        let colorArray = [beginColor, midTopColor, midDownColor, endColor]
        let locationArray = KLineConfig.themeManager.mountainGradient()
        let startPoint = CGPoint(x: clip.minX, y: clip.minY)
        let endPoint = CGPoint(x: clip.minX, y: clip.maxY)
        let space: CGColorSpace = CGColorSpaceCreateDeviceRGB()
        
        // Convert NSNumber *locations array to CGFloat *
        var locations: [CGFloat] = []
        
        for item in locationArray {
          locations.append(CGFloat(fminf(fmaxf(Float(item), 0), 1)))
        }
        
        // Convert colors array to (id) CGColorRef
        var colorRefArray: [CGColor] = []
        
        for color in colorArray {
          colorRefArray.append(color.cgColor)
        }
        
        // Build the internal gradient
        guard let gradient = CGGradient(colorsSpace: space,
                                        colors: colorRefArray as CFArray,
                                        locations: locations) else { return }
        context.drawLinearGradient(gradient,
                                   start: startPoint,
                                   end: endPoint,
                                   options: CGGradientDrawingOptions.drawsBeforeStartLocation)
      }
      context.restoreGState()
    }
    
    
    
  }
    
    /// 为自定义最大最下值等比例缩放做的划线方法
    /// - Parameters:
    ///   - context: -
    ///   - rect: 所画视图的尺寸
    ///   - positionArray: point List
    ///   - color: 线条颜色
    ///   - width: 线条宽度
    ///   - maxValue: 需要显示的最大值 将 实际point 中的y值等比例缩放
    ///   - minValue: 需要显示的最小值  将 实际point 中的y值等比例缩放
    public func drawLineForReferenceWith(context: CGContext,
                                         rect: CGRect,
                                         positionArray: [CGPoint],
                                         color: UIColor,
                                         width: CGFloat,
                                         curves:Bool = true) {
        if positionArray.count < 2 || width <= 0 { return }
        context.setStrokeColor(color.cgColor)
        context.setLineWidth(width)
        let nCurves = positionArray.count - 1
        let path: UIBezierPath = UIBezierPath()
        
        let tempPositionArray = positionArray.map({CGPoint(x: $0.x, y: $0.y)})
        for i in 0..<nCurves {
            let value: CGPoint = tempPositionArray[i]
            
            var curPt = CGPointZero,
                prevPt = CGPointZero,
                nextPt = CGPointZero,
                endPt = CGPointZero
            curPt = value
            
            if i == 0 {
                path.move(to: curPt)
            }
            
            var nexti = (i + 1) % tempPositionArray.count
            var previ = (i - 1 < 0 ? tempPositionArray.count - 1 : i - 1)
            
            prevPt = tempPositionArray[previ]
            
            nextPt = tempPositionArray[nexti]
            
            endPt = nextPt
            
            var ob, my: CGFloat
            
            if i > 0 {
                ob = (nextPt.x - curPt.x) * 0.5 + (curPt.x - prevPt.x) * 0.5
                my = (nextPt.y - curPt.y) * 0.5 + (curPt.y - prevPt.y) * 0.5
            } else {
                ob = (nextPt.x - curPt.x) * 0.5
                my = (nextPt.y - curPt.y) * 0.5
            }
            if curves {
                
                
                var ctrlPt1 = CGPointZero
                ctrlPt1.x = curPt.x + ob / 3.0
                ctrlPt1.y = curPt.y + my / 3.0
                curPt = tempPositionArray[nexti]
                
                nexti = (nexti + 1) % tempPositionArray.count
                previ = i
                
                prevPt = tempPositionArray[previ]
                
                nextPt = tempPositionArray[nexti]
                
                if i < nCurves - 1 {
                    ob = (nextPt.x - curPt.x) * 0.5 + (curPt.x - prevPt.x) * 0.5
                    my = (nextPt.y - curPt.y) * 0.5 + (curPt.y - prevPt.y) * 0.5
                } else {
                    ob = (curPt.x - prevPt.x) * 0.5
                    my = (curPt.y - prevPt.y) * 0.5
                }
                
                var ctrlPt2: CGPoint = .zero
                ctrlPt2.x = curPt.x - ob / 3.0
                ctrlPt2.y = curPt.y - my / 3.0
                path.addCurve(to: endPt, controlPoint1: ctrlPt1, controlPoint2: ctrlPt2)
            }else{
                path.addLine(to: endPt)
            }
        }
        context.addPath(path.cgPath)
        
        context.strokePath()
    }
}

/// 针对 显示全部数据时 重新计算lineWidth
/// - Parameters:
///   - oldlineWidth: 原本的lineWidth
///   - contentWidth: 容器宽度
///   - count: 数据条数
/// - Returns: new lineWidth
public func calculateLineWidth(oldlineWidth:CGFloat,contentWidth:CGFloat,count:Int)->CGFloat{
    var templineWidth = oldlineWidth
    if KLineConfig.displayAllData{
        if KLineConfig.expectedTotalDataCount == nil {
            templineWidth = contentWidth / CGFloat(count)
        }else if  let expectedCount = KLineConfig.expectedTotalDataCount ,expectedCount >= count{
            templineWidth = contentWidth / CGFloat(expectedCount)
        }else{
            templineWidth = contentWidth / CGFloat(count)
        }
        templineWidth *= 0.8 //再缩小一点 才能显示出间隙
    }
    return templineWidth
}
