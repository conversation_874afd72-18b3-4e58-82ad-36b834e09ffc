//
//  UIImage+.swift
//  KLineDemo
//
//  Created by <PERSON> on 2025/3/31.
//

import Foundation
import UIKit

extension UIImage {
    public func blendImage() -> UIImage {
        let color = KLineConfig.themeManager.klineLibDefaultTextColor()
        UIGraphicsBeginImageContextWithOptions(self.size, false, self.scale)
        let context = UIGraphicsGetCurrentContext()
        context?.translateBy(x: 0, y: self.size.height)
        context?.scaleBy(x: 1.0, y: -1.0)
        context?.setBlendMode(.normal)
        let rect = CGRect(x: 0, y: 0, width: self.size.width, height: self.size.height)
        context?.clip(to: rect, mask: self.cgImage!)
        color.setFill()
        context?.fill(rect)
        let newImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return newImage ?? self
    }
}
extension UIImage {
    public func blendColor(_ color: UIColor) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(self.size, false, self.scale)
        let context = UIGraphicsGetCurrentContext()!
        
        // 绘制原始图像
        self.draw(in: CGRect(origin: .zero, size: self.size))
        
        // 设置混合模式
        context.setBlendMode(.sourceIn)
        
        // 设置填充颜色
        color.setFill()
        
        // 绘制颜色
        context.fill(CGRect(origin: .zero, size: self.size))
        
        // 获取混合后的图像
        let blendedImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        return blendedImage
    }
}
