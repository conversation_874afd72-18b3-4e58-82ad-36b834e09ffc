//
//  UIColor+.swift
//  KLineDemo
//
//  Created by <PERSON> on 2025/3/31.
//

import Foundation
import UIKit
extension UIColor {
    public convenience init(_ hexString: String, _ alpha: CGFloat = 1.0) {
        var hex = hexString.trimmingCharacters(in:.whitespacesAndNewlines)
        hex = hex.replacingOccurrences(of: "#", with: "")

        var rgbValue: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&rgbValue)

        let r = CGFloat((rgbValue & 0xFF0000) >> 16) / 255.0
        let g = CGFloat((rgbValue & 0x00FF00) >> 8) / 255.0
        let b = CGFloat(rgbValue & 0x0000FF) / 255.0

        self.init(red: r, green: g, blue: b, alpha: alpha)
    }
    
    /// 将UIColor 转Hex
    /// - Parameter includeAlpha: 是否保留透明度
    /// - Returns: hex Str
    public func toHex(includeAlpha: Bool = true) -> String {
           guard let components = cgColor.components, components.count >= 3 else {
               return ""
           }

           let r = Float(components[0])
           let g = Float(components[1])
           let b = Float(components[2])
           var a = Float(1.0)

           if components.count >= 4 {
               a = Float(components[3])
           }

           if includeAlpha {
               return String(format: "#%02lX%02lX%02lX%02lX",
                             lroundf(r * 255),
                             lroundf(g * 255),
                             lroundf(b * 255),
                             lroundf(a * 255))
           } else {
               return String(format: "#%02lX%02lX%02lX",
                             lroundf(r * 255),
                             lroundf(g * 255),
                             lroundf(b * 255))
           }
       }
}
