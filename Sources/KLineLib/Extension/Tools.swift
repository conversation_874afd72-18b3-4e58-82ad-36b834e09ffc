//
//  Tools.swift
//  UseKlineToolsDemo
//
//  Created by <PERSON> on 2025/3/31.
//

import Foundation
public func asyncOnMain(execute: @escaping () -> Void) {
    DispatchQueue.main.async() {
        execute()
    }
}
public func DispatchAfter(_ delay: TimeInterval, execute: @escaping () -> Void) {
    let deadline = DispatchTime.now() + delay
    DispatchQueue.main.asyncAfter(deadline: deadline) {
        execute()
    }
}
