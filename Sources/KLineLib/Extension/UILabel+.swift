//
//  UILabel+.swift
//  UseKlineToolsDemo
//
//  Created by <PERSON> on 2025/3/31.
//

import Foundation
import UIKit
extension UILabel {
    /// 设置文本颜色.
    @discardableResult
    public func textColor(_ color: UIColor) -> UILabel { self.textColor = color; return self }
    /// 设置文本.
    @discardableResult
    public func text(_ text: String) -> UILabel { self.text = text; return self }
    /// 设置文本对齐方式.
    @discardableResult
    public func textAlignment(_ align: NSTextAlignment) -> UILabel { self.textAlignment = align; return self }
    /// 设置背景色.
    @discardableResult
    public func bgColor(_ color: UIColor) -> UILabel { self.backgroundColor = color; return self }
    /// 设置显示行数.
    @discardableResult
    public func numberOfLines(_ line: Int) -> UILabel { self.numberOfLines = line; return self }
    /// 设置字体.
    @discardableResult
    public func textFont(_ font: UIFont) -> UILabel { self.font = font; return self }
}
