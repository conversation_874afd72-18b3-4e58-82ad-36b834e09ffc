//
//  UIView+Extension.swift
//  Ourbit
//
//  Created by pippen on 2024/1/12.
//

import UIKit


extension UIView {
    // MARK: 坐标尺寸
    var origin: CGPoint {
        get {
            return self.frame.origin
        }
        set(newValue) {
            var rect = self.frame
            rect.origin = newValue
            self.frame = rect
        }
    }
    
    var size: CGSize {
        get {
            return self.frame.size
        }
        set(newValue) {
            var rect = self.frame
            rect.size = newValue
            self.frame = rect
        }
    }
    
    var width: CGFloat {
        get {
            return self.size.width
        }
        set(newValue) {
            var rect = self.frame
            rect.size.width = newValue
            self.frame = rect
        }
    }
    
    var height: CGFloat {
        get {
            return self.size.height
        }
        set(newValue) {
            var rect = self.frame
            rect.size.height = newValue
            self.frame = rect
        }
    }
    
    var left: CGFloat {
        get {
            return self.frame.origin.x
        }
        set(newValue) {
            var rect = self.frame
            rect.origin.x = newValue
            self.frame = rect
        }
    }
    
    var top: CGFloat {
        get {
            return self.frame.origin.y
        }
        set(newValue) {
            var rect = self.frame
            rect.origin.y = newValue
            self.frame = rect
        }
    }
    
    var right: CGFloat {
        get {
            return (self.frame.origin.x + self.frame.size.width)
        }
        set(newValue) {
            var rect = self.frame
            rect.origin.x = (newValue - self.frame.size.width)
            self.frame = rect
        }
    }
    
    var bottom: CGFloat {
        get {
            return (self.frame.origin.y + self.frame.size.height)
        }
        set(newValue) {
            var rect = self.frame
            rect.origin.y = (newValue - self.frame.size.height)
            self.frame = rect
        }
    }
    
    var cornerRadius: CGFloat {
        get {
            return layer.cornerRadius
        }
        set(newValue) {
            layer.cornerRadius = newValue
            clipsToBounds = true
        }
    }
    
    var borderColor: UIColor? {
        get {
            if let layerColor = layer.borderColor {
                return UIColor(cgColor: layerColor)
            }
            return .clear
        }
        set(newValue) {
            layer.borderColor = newValue?.cgColor
        }
    }
    
    var borderWidth: CGFloat {
        get {
            return layer.borderWidth
        }
        set(newValue) {
            layer.borderWidth = newValue
        }
    }
    
    // MARK: - 位移
    // 移动到指定中心点位置
    func moveToPoint(point: CGPoint) {
        var center = self.center
        center.x = point.x
        center.y = point.y
        self.center = center
    }
    
    func ratate(angle: CGFloat) {
        let radians = angle / 180.0 * CGFloat.pi
        let rotation = self.transform.rotated(by: radians)
        self.transform = rotation
    }
    
    // 缩放到指定大小
    func scaleToSize(scale: CGFloat) {
        var rect = self.frame
        rect.size.width *= scale
        rect.size.height *= scale
        self.frame = rect
    }
    
    // MARK: - 毛玻璃效果
    // 毛玻璃
    func effectViewWithAlpha(_ style: UIBlurEffect.Style, _ alpha: CGFloat) {
        let effect = UIBlurEffect(style: style)
        let effectView = UIVisualEffectView(effect: effect)
        effectView.frame = self.bounds
        effectView.alpha = alpha
        self.addSubview(effectView)
    }
    
    // MARK: - 边框属性
    
    // 圆角边框设置
    func layer(radius: CGFloat, borderWidth: CGFloat, borderColor: UIColor) {
        if 0.0 < radius {
            self.layer.cornerRadius = radius
            self.layer.masksToBounds = true
            self.clipsToBounds = true
        }
        
        if 0.0 < borderWidth {
            self.layer.borderColor = borderColor.cgColor
            self.layer.borderWidth = borderWidth
        }
    }
    
    // MARK: - 翻转
    
    // 旋转 旋转180度 M_PI
    func transformWithRotation(rotation: CGFloat) {
        self.transform = CGAffineTransform(rotationAngle: rotation)
    }
    
    // 缩放
    func scaleWithSize(size: CGFloat) {
        self.transform.scaledBy(x: size, y: size)
    }
    
    // 水平，或垂直翻转
    func flip(isHorizontal: Bool) {
        if isHorizontal { // 水平
            self.transform.scaledBy(x: -1.0, y: 1.0)
        } else { // 垂直
            self.transform.scaledBy(x: 1.0, y: -1.0)
        }
    }
}


public typealias TapGestureBlock = (UITapGestureRecognizer) -> Void
extension UITapGestureRecognizer {
    /// 用于存储闭包的关联对象
    private struct AssociatedKeys {
        static var tapGestureInvokeKey = "tapGestureInvokeKey"
    }

    /// 添加TapCallback
    /// ⚠️添加target:action和tapInvoke不要同时使用，会相互覆盖
    public var extTapInvoke: TapGestureBlock? {
        get {
            return objc_getAssociatedObject(self, &AssociatedKeys.tapGestureInvokeKey) as? TapGestureBlock
        }
        set {
            objc_setAssociatedObject(
                self,
                &AssociatedKeys.tapGestureInvokeKey,
                newValue,
                .OBJC_ASSOCIATION_RETAIN_NONATOMIC
            )
            addTarget(self, action: #selector(_extTapAction(_:)))
        }
    }

    // 用于处理手势的方法
    @objc private func _extTapAction(_ sender: UITapGestureRecognizer) {
        extTapInvoke?(sender)
    }
}

public extension UIView {
    /// 添加Tap手势
    func addTapGestureRecognizer(_ invoke: @escaping TapGestureBlock) {
        isUserInteractionEnabled = true
        let gesture = UITapGestureRecognizer()
        gesture.extTapInvoke = invoke
        addGestureRecognizer(gesture)
    }
    
    func removeTapGesture() {
        guard let gestureRecognizers = gestureRecognizers else { return }
        for gesture in gestureRecognizers {
            if let tapGesture = gesture as? UITapGestureRecognizer,
               tapGesture.extTapInvoke != nil {
                tapGesture.extTapInvoke = nil
                removeGestureRecognizer(tapGesture)
            }
        }
    }
}

public extension UIView {
    enum StrockDirection {
        case horizontal
        case vertical
    }
    /// 绘制虚线
    ///
    /// - Parameters:
    ///   - direction: 方向
    ///   - length: 虚线长度
    ///   - spacing 间隔长度
    ///   - color 虚线颜色
    func drawStrockLine(direction: StrockDirection, length: CGFloat, spacing: CGFloat, color: UIColor) {
        self.layer.sublayers?.removeAll()
        let shapeLayer = CAShapeLayer()
        shapeLayer.bounds = self.bounds
        shapeLayer.position = CGPoint(x: self.frame.width / 2, y: self.frame.height / 2)
        shapeLayer.fillColor = UIColor.clear.cgColor
        shapeLayer.strokeColor = color.cgColor
        shapeLayer.lineWidth = direction == .horizontal ? self.frame.height : self.frame.width
        shapeLayer.lineJoin = .round
        shapeLayer.lineDashPhase = 0
        
        shapeLayer.lineDashPattern = [NSNumber(value: length), NSNumber(value: spacing)]
        
        let path = CGMutablePath()
        
        if direction == .horizontal {
            path.move(to: CGPoint(x: 0, y: self.height / 2))
            path.addLine(to: CGPoint(x: self.width, y: self.height / 2))
        } else {
            path.move(to: CGPoint(x: self.width / 2, y: 0))
            path.addLine(to: CGPoint(x: self.width / 2, y: self.height))
        }
        
        shapeLayer.path = path
        self.layer.addSublayer(shapeLayer)
    }
    
    func drawImage() -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(self.frame.size, true, 0)
        guard let context = UIGraphicsGetCurrentContext() else { return nil }
        self.layer.render(in: context)
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return image
    }
}

public extension UIView {
    // 添加横向渐变图层
    func setColorLayerHorizontal(leftColor: UIColor, rightColor: UIColor, frame: CGRect) {
        let gradient = CAGradientLayer()
        gradient.colors = [leftColor.cgColor, rightColor.cgColor]
        gradient.locations = [0, 1]
        gradient.frame = frame
        gradient.startPoint = CGPoint(x: 0.00, y: 0.50)
        gradient.endPoint = CGPoint(x: 1.0, y: 0.50)
        self.layer.insertSublayer(gradient, at: 0)
    }
    
    // 添加纵向渐变图层
    func setColorLayerVertical(topColor: UIColor, bottomColor: UIColor, frame: CGRect) {
        let gradient = CAGradientLayer()
        gradient.frame = frame
        gradient.startPoint = CGPoint(x: 0.50, y: 0.00)
        gradient.endPoint = CGPoint(x: 0.50, y: 1.00)
        gradient.colors = [topColor.cgColor, bottomColor.cgColor]
        gradient.locations = [0, 1]

        self.layer.insertSublayer(gradient, at: 0)
    }
}

/// 部分View不依赖ViewController，直接是在Window上呈现的
/// 为了让PopView跟ViewController的响应链保持联系， 提供Get方法， 返回关联的View
protocol PopViewAttachable {
    var attachedView: UIView? { get }
}

extension UIView: PopViewAttachable {
    /// 默认为nil， 表示是自己
    var attachedView: UIView? {
        return nil
    }
}

public extension UIView {
    /// 返回当前view所属的最近的Controller
    /// excludeTCVc: 排除UITableViewController 和 UICollectionController
    public func closestController(block: ((UIViewController) -> Bool), excludeTCVc: Bool = false) -> UIViewController? {
        return p_closestController(block: block, excludeTCVc: excludeTCVc)
    }
    
    /// 返回当前view所属的最近的Controller
    /// excludeTCVc: 排除UITableViewController 和 UICollectionController
    public func closestController(cls: AnyClass? = nil, excludeTCVc: Bool = false) -> UIViewController? {
        if let selfVc = self.p_closestController(cls: cls, excludeTCVc: excludeTCVc) {
            return selfVc
        }
        if let attachedView = self.attachedView {
            return attachedView.p_closestController(cls: cls, excludeTCVc: excludeTCVc)
        }
        return nil
    }
    
    /// 返回当前view所属的最近的Controller
    private func p_closestController(cls: AnyClass? = nil, excludeTCVc: Bool = false) -> UIViewController? {
        return p_closestController(block: { vc in
            if let targetClass = cls { return vc.isMember(of: targetClass) }
            return true
        }, excludeTCVc: excludeTCVc)
    }
    
    /// 返回当前view所属的最近的Controller
    private func p_closestController(block: (UIViewController) -> Bool,
                                     excludeTCVc: Bool = false) -> UIViewController? {
        var currObj: UIResponder = self
        while let nextRsp = currObj.next {
            if let vc = nextRsp as? UIViewController {
                let isTableVc = vc.isKind(of: UITableViewController.self)
                let isColVc = vc.isKind(of: UICollectionViewController.self)
                if excludeTCVc && (isTableVc || isColVc) {
                    currObj = nextRsp
                    continue
                }
                if block(vc) {
                    return vc
                } else {
                    currObj = nextRsp
                }
            } else {
                currObj = nextRsp
            }
        }
        return nil
    }
    
    func resetConstraints() {
        removeConstraintsAboutSelfInSuperView()
        removeConstraintsAboutSelfInSelf()
        /* removeConstraintsAboutSelfInSelf()会将系统自带的自动撑开的约束一并移除
         故重新设置translatesAutoresizingMaskIntoConstraints = true添加自动撑开的约束
         */
        let oldValue = translatesAutoresizingMaskIntoConstraints
        translatesAutoresizingMaskIntoConstraints = true
        translatesAutoresizingMaskIntoConstraints = oldValue
    }
    
    func removeConstraintsAboutSelfInSuperView() {
        guard let superview = self.superview else { return }
        for item in superview.constraints where item.firstItem === self {
            item.isActive = false
            superview.removeConstraint(item)
        }
    }
    
    func removeConstraintsAboutSelfInSelf() {
        for item in constraints where item.firstItem === self {
            item.isActive = false
            removeConstraint(item)
        }
    }
}

extension UIView {
    public func setShadow(sColor: UIColor, offset: CGSize, opacity: Float, radius: CGFloat) {
        // 设置阴影颜色
        self.layer.shadowColor = sColor.cgColor
        // 设置透明度
        self.layer.shadowOpacity = opacity
        // 设置阴影半径
        self.layer.shadowRadius = radius
        // 设置阴影偏移量
        self.layer.shadowOffset = offset
    }
}
