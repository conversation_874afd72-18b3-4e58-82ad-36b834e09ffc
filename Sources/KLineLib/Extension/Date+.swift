//
//  Date+.swift
//  KLineDemo
//
//  Created by <PERSON> on 2025/3/17.
//

import Foundation
extension Date{
    public func stringWithFormat(format:String)->String{
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = format
        // 设置时区（可选，默认使用系统时区）
        dateFormatter.timeZone = KLineConfig.timeAxisTimeZone
        // 设置地区（可选，确保格式一致性）
        dateFormatter.locale = KLineConfig.timeAxisLocale
        let result = dateFormatter.string(from: self)
        return result
        
    }
}
extension TimeInterval{
    public func timestampToDateString(format: String = "yyyy-MM-dd HH:mm:ss") -> String {
        let date = Date(timeIntervalSince1970: self / 1000)
        let dateFormatter = DateFormatter()
        
        // 设置日期格式
        dateFormatter.dateFormat = format
        // 设置时区（可选，默认使用系统时区）
        dateFormatter.timeZone = KLineConfig.timeAxisTimeZone
        // 设置地区（可选，确保格式一致性）
        dateFormatter.locale = KLineConfig.timeAxisLocale
        
        return dateFormatter.string(from: date)
    }
}
