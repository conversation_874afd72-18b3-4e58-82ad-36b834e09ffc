//
//  UDTool.swift
//  UseKlineToolsDemo
//
//  Created by <PERSON> on 2025/3/31.
//

import Foundation
// UDTool 类实现
public class UDTool {
    private static let userDefaults = UserDefaults.standard

    /// 根据键获取值
    public static func value(key: String) -> Any? {
        return userDefaults.value(forKey: key)
    }

    /// 设置键值对
    public static func setValue(value: Any, forKey key: String) {
        userDefaults.set(value, forKey: key)
    }
    
    
    /// 根据键获取值
    public static func bool(key: String) -> Bool {
        return userDefaults.bool(forKey: key)
    }
    /// 设置键值对
    public static func setBool(value: Bool, forKey key: String) {
        userDefaults.set(value, forKey: key)
    }
    /// 根据键移除值
    public static func remove(key: String) {
        userDefaults.removeObject(forKey: key)
    }
}
