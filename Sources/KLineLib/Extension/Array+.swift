//
//  Array.swift
//  KLineDemo
//
//  Created by <PERSON> on 2025/3/18.
//

import Foundation
extension Array{
    public func forEachIndex<T>(_ complet:(T,Int)->Void){
        for index in 0..<self.count{
            let value = self[index] as! T
            complet(value, index)
        }
    }
}
extension Array{
    public subscript(safe index: Int) -> Element? {
            return indices.contains(index) ? self[index] : nil
        }

}
extension Array {
    public mutating func safeSwap(_ index1: Int, _ index2: Int) {
        guard index1 != index2,
              indices.contains(index1),
              indices.contains(index2) else { return }
        self.swapAt(index1, index2)
    }
}

extension Sequence where Element: Hashable {
    
    /// 去重
    /// - Returns: 去重结果
    func uniqued() -> [Element] {
        var seen = Set<Element>()
        return filter { seen.insert($0).inserted }
    }
}

extension Array where Element: Equatable {
    
    /// 去重连续重复数据
    /// - Returns: 结果
    func removeConsecutiveDuplicates() -> [Element] {
        guard !isEmpty else { return self }
        var result = [Element]()
        result.reserveCapacity(count)  // 预分配空间提高性能
        
        var previous = self[0]
        result.append(previous)
        
        for element in self.dropFirst() {
            if element != previous {
                result.append(element)
                previous = element
            }
        }
        
        return result
    }
}
