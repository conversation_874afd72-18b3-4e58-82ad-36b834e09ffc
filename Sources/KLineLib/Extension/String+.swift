//
//  String+.swift
//  KLineDemo
//
//  Created by <PERSON> on 2025/3/17.
//

import Foundation
import UIKit

extension String{
    //MARK: Michael: 传入文字Font 及宽度计算文字所需高度
    public func textHeight(font: UIFont, maxWidth: CGFloat)->CGFloat{
        return self.backWidthAndHeightWith(font: font,
                                           width: maxWidth,
                                           height: CGFloat.greatestFiniteMagnitude).height
    }
    //MARK: Michael: 传入文字Font 及高度计算文字所需宽度
    public func textWidth(font: UIFont, maxHeight: CGFloat)->CGFloat{
        return self.backWidthAndHeightWith(font: font,
                                           width: CGFloat.greatestFiniteMagnitude,
                                           height: maxHeight).width
    }
    fileprivate  func backWidthAndHeightWith(font:UIFont,
                                            width:CGFloat,
                                            height:CGFloat)->CGSize{
        let rect  = self.boundingRect(with: CGSize(width: width, height: height),
                                      options: .usesLineFragmentOrigin,
                                      attributes: [NSAttributedString.Key.font:font],
                                      context: nil)
        return rect.size
    }
}

extension String{
    //MARK: Michael: 暂时不知道有没有 Hex 转Color的方法 后面补充完整
    public func hexColor()->UIColor{
        return UIColor(self)
    }
}
extension String {
    public func doubleValue() -> Double {
        return Double(self) ?? 0.0
    }
    public func intValue() -> Int? {
        return Int(self)
    }
    
    public func asDouble() -> Double? {
        return Double(self)
    }
}
