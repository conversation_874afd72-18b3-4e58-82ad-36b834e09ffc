//
//  File.swift
//  
//
//  Created by pippen on 2024/1/11.
//

import Foundation
import UIKit

public typealias NotiInvoke = (Notification) -> Void

/// 封装的用于Notification的简易处理.
public class CusstomNotification {
    public typealias NotiKey = String
    public typealias TargetDesc = String
    
    var notiHashMap = [NotiKey: [TargetDesc: NotiInvoke]]()
    
    static let shared = CusstomNotification()
    
    /// post notification on main queue.
    public class func post(_ key: String, _ obj: Any? = nil) {
        assert(!key.isEmpty, "Notification key is nil")
        if Thread.isMainThread == false {
            DispatchQueue.main.sync { self.post(key, obj) }
            return
        }
        let name = Notification.Name(rawValue: key)
        NotificationCenter.default.post(name: name, object: obj)
    }
    
    public class func remove(_ target: Any, _ key: NotiKey) {
        if Thread.isMainThread == false {
            DispatchQueue.main.sync { self.remove(target, key) }
            return
        }
        
        guard let targetDesc = shared.targetDesc(target) else {
            assert(false, "target is not anyObject"); return
        }
        //LoggerTool.debug(tag: LogType.data, "targetDesc:\(targetDesc)")
        
        let name = Notification.Name(rawValue: key)
        
        if var map = shared.notiHashMap[key] {
            map.removeValue(forKey: targetDesc)
            shared.notiHashMap[key] = map
            if map.isEmpty { NotificationCenter.default.removeObserver(shared, name: name, object: nil) }
        }
    }
    
    public class func observe(_ target: Any, _ key: NotiKey, _ invoke: @escaping NotiInvoke, _ obj: Any? = nil) {
        guard let targetDesc = shared.targetDesc(target) else {
            assert(false, "target is not anyObject"); return
        }

        DispatchQueue.main.async {
            let noti = NotificationCenter.default
            let name = Notification.Name(rawValue: key)
            
            var map = shared.notiHashMap[key]
            if map == nil { map = [String: NotiInvoke]() }
            
            if let map = map, map.keys.isEmpty {
                noti.addObserver(shared, selector: #selector(notiHandle), name: name, object: obj)
            }
            map![targetDesc] = invoke
            shared.notiHashMap[key] = map
            
        }
    }
    
    static var ind: Int = 0
    @objc private func notiHandle(noti: Notification) {
        DispatchQueue.main.async { [weak self] in
            let key = noti.name.rawValue
            if let map = self?.notiHashMap[key] {
                map.values.forEach { $0(noti) }
            }
        }
    }
    
    func targetDesc(_ obj: Any) -> String? {
        let AO = obj as AnyObject
        return "\((Unmanaged<AnyObject>.passUnretained(AO).toOpaque()))"
    }
}

public enum Noti {
    
   
    public enum Data {
        public static let klineThemeSkinDidChanged = "noti.OBKlineThemeSkinDidChanged"
        public static let klineDrawModeDidChanged = "noti.OBKlineDrawModeDidChanged"
    }
    

}
