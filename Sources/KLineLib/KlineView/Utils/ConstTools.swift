import UIKit

/// 全局常量定义
public struct ConstTools {

//    // MARK: - 时间格式
//    /// 日期格式：年月日
    public static let dateFormatYMD = "yyyy-MM-dd"
    /// 日期格式：年月日时分
    public static let dateFormatYMDHM = "yyyy-MM-dd HH:mm"
    /// 日期格式：年月日时分秒
    public static let dateFormatYMDHMS = "yyyy-MM-dd HH:mm:ss"
    /// 日期格式：月日
    public static let dateFormatMD = "MM-dd"
//    /// 日期格式：时分
    public static let dateFormatHM = "HH:mm"
//    /// 日期格式：时分秒
    public static let dateFormatHMS = "HH:mm:ss"
//
}

func FORMAT(_ format: String, _ args: CVarArg...) -> String {
    return String(format: format, arguments: args)
}
