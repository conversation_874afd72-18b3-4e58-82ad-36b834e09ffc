//
//  AccuracyManager.swift
//  KLineDemo
//
//  Created by <PERSON> on 2025/3/18.
//

import Foundation
open class AccuracyManager{
    public static func quantityNum(accuracy: Double?, quantity: Double?)->String{
      let formatter = NumberFormatter()
      formatter.numberStyle = .decimal
      formatter.minimumFractionDigits = Int(accuracy ?? 3.0)   // 最少3位小数
      formatter.maximumFractionDigits = Int(accuracy ?? 3.0) // 最多3位小数
      formatter.roundingMode = .halfUp     // 四舍五入
      return formatter.string(from: NSNumber(value:  quantity ?? 0.0)) ?? "0.0"
    }
    public static func quantityNum(accuracy: Int, quantity: String)->String{
      let formatter = NumberFormatter()
      formatter.numberStyle = .decimal
      formatter.minimumFractionDigits = accuracy   // 最少3位小数
      formatter.maximumFractionDigits = accuracy // 最多3位小数
      formatter.roundingMode = .halfUp     // 四舍五入
      return formatter.string(from: NSNumber(value: Double(quantity) ?? 0.0)) ?? "0.0"
    }
    
    public static func formatbAbreviationQuantityNum(accuracy: Int, quantity: Double)->String{
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        formatter.minimumFractionDigits = accuracy   // 最少3位小数
        formatter.maximumFractionDigits = accuracy // 最多3位小数
        formatter.roundingMode = .halfUp     // 四舍五入
        
        var newWord: String = ""
        if (quantity / 1_000_000_000_000) >= 1 {
            if let word = formatter.string(from: NSNumber(value: quantity / 1_000_000_000_000)) {
                newWord = word + "T"
            }
        } else if (quantity / 1_000_000_000) >= 1 {
            if let word = formatter.string(from: NSNumber(value: quantity / 1_000_000_000)) {
                newWord = word + "B"
            }
        } else if (quantity / 1_000_000) >= 1 {
            if let word = formatter.string(from: NSNumber(value: quantity / 1_000_000)) {
                newWord = word + "M"
            }
        } else if (quantity / 1_000) >= 1 {
            if let word = formatter.string(from: NSNumber(value: quantity / 1_000)) {
                newWord = word + "K"
            }
        }
        if newWord.isEmpty {
            newWord = formatter.string(from: NSNumber(value: quantity)) ?? ""
        }
        return newWord
    }
}
/*
 /// 价格精度便捷方法.
 class AccuracyManager: NSObject {
     /// 设置现货价格精度
     static func setPrice(_ tradingPair: String, _ accuracy: Int) {
         AccuracyManager.setPriceAccuracyWith("spot", tradingPair, accuracy)
     }
     /// 获取现货价格精度
     static func getPrice(_ tradingPair: String) -> Int {
         return AccuracyManager.getPriceAccuracyWith("spot", tradingPair)
     }
     
     /// 设置现货数量精度
     static func setQuantity(_ tradingPair: String, _ accuracy: Int) {
         AccuracyManager.setQuantityAccuracyWith("spot", tradingPair, accuracy)
     }
     /// 获取现货数量精度
     static func getQuantity(_ tradingPair: String) -> Int {
         return AccuracyManager.getQuantityAccuracyWith("spot", tradingPair)
     }
     
     /// 设置合约价格精度
     static func setContractPrice(_ symbol: String, _ accuracy: Int) {
         AccuracyManager.setQuantityAccuracyWith("contract", symbol, accuracy)
     }
     /// 获取合约价格精度
     static private func _getContractPrice(_ symbol: String) -> Int {
         return AccuracyManager.getPriceAccuracyWith("contract", symbol)
     }
     
     /// 设置合约合并后显示价格精度
     static func setContractCombinedPrice(_ symbol: String, _ accuracy: Int) {
         let pair = AccuracyManager.createSaveTradingPair(symbol)
         OBUD.setValue(value: accuracy, forKey: "contract_combined_\(pair)")
     }
     /// 获取合约合并后显示价格精度
     static func getContractCombinedPrice(_ symbol: String,
                                         combinedDecimals: [Int]) -> Int {
         let pair = AccuracyManager.createSaveTradingPair(symbol)
         let accuracy = OBUD.value(key: "contract_combined_\(pair)") as? Int
         return accuracy ?? combinedDecimals.first ?? AccuracyManager.defaultAccuracy
     }
     
     static func checkContractSelectedCombinedPrice(_ symbol: String) -> Int? {
         let pair = AccuracyManager.createSaveTradingPair(symbol)
         return OBUD.value(key: "contract_combined_\(pair)") as? Int
     }
     
     /// 交易对数量精度
     static func quantityNumFor(pair p: String? = nil,
                                quantity q: String) -> String {
         let numAccuracy = p == nil ? q.decimalPlaces() : Self.getQuantity(p!)
         return quantityNum(accuracy: numAccuracy, quantity: q)
     }
     
     /// 数量精度处理
     /// - Parameters:
     ///   - accuracy: 精度-数量小于1000时浮点数精度，并且不进行转换；否则使用默认精度，进行KMB转换
     ///   - q: 数量
     /// - Returns: 处理后的数量
     static func quantityNum(accuracy: Int = OBNumAccuracy,
                             quantity q: String) -> String {
         if q.intValue() < OBNumK {
             return OBShowNumber(q, .fullDown(accuracy))
         } else {
             return OBShowNumber(q, .fullDown(OBNumAccuracy), true)
         }
     }
 }

 */
