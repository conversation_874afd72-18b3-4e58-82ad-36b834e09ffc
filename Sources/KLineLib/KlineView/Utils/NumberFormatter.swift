import Foundation

/// 数字格式化工具
public class NumberFormatterTool {
    /// 共享实例
    public static let shared = NumberFormatterTool()
    
    /// 数字格式化器
    private let numberFormatter: NumberFormatter
    
    /// 百分比格式化器
    private let percentFormatter: NumberFormatter
    
    /// 科学计数格式化器
    private let scientificFormatter: NumberFormatter
    
    private init() {
        // 初始化数字格式化器
        numberFormatter = NumberFormatter()
        numberFormatter.numberStyle = .decimal
        numberFormatter.minimumFractionDigits = 0
        numberFormatter.maximumFractionDigits = 8
        numberFormatter.groupingSeparator = ","
        numberFormatter.decimalSeparator = "."
        numberFormatter.roundingMode = .halfUp
        // 初始化百分比格式化器
        percentFormatter = NumberFormatter()
        percentFormatter.numberStyle = .percent
        percentFormatter.minimumFractionDigits = 2
        percentFormatter.maximumFractionDigits = 2
        
        // 初始化科学计数格式化器
        scientificFormatter = NumberFormatter()
        scientificFormatter.numberStyle = .scientific
        scientificFormatter.maximumFractionDigits = 2
    }
    
    // MARK: - 格式化方法
    
    /// 格式化数字（自动处理精度）
    /// - Parameters:
    ///   - number: 要格式化的数字
    ///   - scale: 小数位数（如果为负数则自动判断）
    ///   - minDigits: 最小小数位数
    /// - Returns: 格式化后的字符串
    public func format(_ number: Double, scale: Int = -1, minDigits: Int = 0) -> String {
        // 处理 0
        if abs(number) < Double.ulpOfOne {
            return "0"
        }
        
        // 处理无效数字
        if number.isNaN || number.isInfinite {
            return "--"
        }
        
        // 确定精度
        let precision = scale >= 0 ? scale : calculatePrecision(number)
        
        numberFormatter.minimumFractionDigits = minDigits
        numberFormatter.maximumFractionDigits = precision
        
        // 使用科学计数法的阈值
        let scientificThreshold = 1_000_000_000_000.0 // 1万亿
        
        if abs(number) >= scientificThreshold {
            return scientificFormatter.string(from: NSNumber(value: number)) ?? "\(number)"
        }
        
        return numberFormatter.string(from: NSNumber(value: number)) ?? "\(number)"
    }
    
    /// 格式化百分比
    /// - Parameters:
    ///   - number: 要格式化的数字（原始值，如 0.1234 表示 12.34%）
    ///   - scale: 小数位数（默认2位）
    /// - Returns: 格式化后的百分比字符串
    public func formatPercent(_ number: Double, scale: Int = 2) -> String {
        if number.isNaN || number.isInfinite {
            return "--"
        }
        
        percentFormatter.maximumFractionDigits = scale
        return percentFormatter.string(from: NSNumber(value: number)) ?? "\(number * 100)%"
    }
    
    /// 格式化价格
    /// - Parameters:
    ///   - price: 价格
    ///   - scale: 小数位数（如果为负数则自动判断）
    /// - Returns: 格式化后的价格字符串
    public func formatPrice(_ price: Double, scale: Int = -1) -> String {
        return format(price, scale: scale, minDigits: 2)
    }
    
    /// 格式化数量
    /// - Parameters:
    ///   - volume: 数量
    ///   - scale: 小数位数（默认为2）
    /// - Returns: 格式化后的数量字符串
    public func formatVolume(_ volume: Double, scale: Int = 2) -> String {
        if volume >= 100_000_000 { // 亿
            return format(volume / 100_000_000, scale: scale) + "亿"
        } else if volume >= 10_000 { // 万
            return format(volume / 10_000, scale: scale) + "万"
        } else {
            return format(volume, scale: scale)
        }
    }
    
    // MARK: - 私有辅助方法
    
    /// 计算合适的精度
    private func calculatePrecision(_ number: Double) -> Int {
        let absNumber = abs(number)
        
        if absNumber >= 1000 {
            return 2
        } else if absNumber >= 100 {
            return 3
        } else if absNumber >= 10 {
            return 4
        } else if absNumber >= 1 {
            return 5
        } else if absNumber >= 0.1 {
            return 6
        } else if absNumber >= 0.01 {
            return 7
        } else {
            return 8
        }
    }
}

// MARK: - 便捷扩展
extension Double {
    /// 格式化为字符串
    public func formatted(scale: Int = -1, minDigits: Int = 0) -> String {
        return NumberFormatterTool.shared.format(self, scale: scale, minDigits: minDigits)
    }
    
    /// 格式化为百分比
    public func formattedPercent(scale: Int = 2) -> String {
        return NumberFormatterTool.shared.formatPercent(self, scale: scale)
    }
    
    /// 格式化为价格
    public func formattedPrice(scale: Int = -1) -> String {
        return NumberFormatterTool.shared.formatPrice(self, scale: scale)
    }
    
    /// 格式化为数量
    public func formattedVolume(scale: Int = 2) -> String {
        return NumberFormatterTool.shared.formatVolume(self, scale: scale)
    }
}

// MARK: - 使用示例
/*
 
 // 1. 使用共享实例
 let price = 1234.5678
 let formatted = OBNumberFormatter.shared.format(price) // "1,234.57"
 
 // 2. 格式化百分比
 let percent = 0.1234
 let percentStr = OBNumberFormatter.shared.formatPercent(percent) // "12.34%"
 
 // 3. 格式化价格
 let price = 1234.5678
 let priceStr = OBNumberFormatter.shared.formatPrice(price) // "1,234.57"
 
 // 4. 格式化数量
 let volume = 123456789
 let volumeStr = OBNumberFormatter.shared.formatVolume(volume) // "1.23亿"
 
 // 5. 使用 Double 扩展
 let number = 1234.5678
 let str1 = number.formatted() // "1,234.57"
 let str2 = number.formattedPrice() // "1,234.57"
 let str3 = (0.1234).formattedPercent() // "12.34%"
 let str4 = (123456.0).formattedVolume() // "12.35万"
 
 */ 

/// 数字显示格式
public enum ShowNumberAccuracy {
    /// 向下取整到指定小数位
    case fullDown(_ scale: Int)
    /// 向上取整到指定小数位
    case fullUp(_ scale: Int)
    /// 向下取整到指定小数位，但只保留有效数字
    case validDown(_ scale: Int)
    /// 向上取整到指定小数位，但只保留有效数字
    case validUp(_ scale: Int)
    /// 向下取整到指定小数位，但只保留一半有效数字
    case halfFullDown(_ scale: Int)
    /// 向上取整到指定小数位，但只保留一半有效数字
    case halfFullUp(_ scale: Int)
    /// 向下取整到指定小数位
    case down(_ scale: Int)
    /// 向上取整到指定小数位
    case up(_ scale: Int)
}

/// 格式化数字显示
/// - Parameters:
///   - number: 要格式化的数字（可以是 String 或 Double 类型）
///   - accuracy: 精度要求
/// - Returns: 格式化后的字符串
public func ShowNumber(_ number: Any, _ accuracy: ShowNumberAccuracy = .fullDown(2)) -> String {
    // 处理无效输入
    if let str = number as? String, str.isEmpty || str == "--" {
        return "--"
    }
    
    // 转换为 Double
    var value: Double = 0
    if let doubleValue = number as? Double {
        value = doubleValue
    } else if let stringValue = number as? String, let doubleValue = Double(stringValue) {
        value = doubleValue
    } else {
        return "--"
    }
    
    // 处理特殊值
    if value.isNaN || value.isInfinite {
        return "--"
    }
    
    // 根据精度要求格式化
    switch accuracy {
    case .fullDown(let scale), .down(let scale):
        return NumberFormatterTool.shared.format(value, scale: scale,minDigits: 3)
        
    case .fullUp(let scale), .up(let scale):
        // 向上取整，先将数字乘以 10^scale，向上取整，再除以 10^scale
        let multiplier = pow(10.0, Double(scale))
        let roundedValue = ceil(value * multiplier) / multiplier
        return NumberFormatterTool.shared.format(roundedValue, scale: scale)
        
    case .validDown(let scale):
        // 只保留有效数字，去除末尾的0
        let formatted = NumberFormatterTool.shared.format(value, scale: scale)
        return formatted.replacingOccurrences(of: "\\.?0+$", with: "", options: .regularExpression)
        
    case .validUp(let scale):
        let multiplier = pow(10.0, Double(scale))
        let roundedValue = ceil(value * multiplier) / multiplier
        let formatted = NumberFormatterTool.shared.format(roundedValue, scale: scale)
        return formatted.replacingOccurrences(of: "\\.?0+$", with: "", options: .regularExpression)
        
    case .halfFullDown(let scale):
        // 保留一半有效数字，向下取整
        let effectiveScale = min(scale, Int(floor(Double(scale) / 2.0)))
        return NumberFormatterTool.shared.format(value, scale: effectiveScale)
        
    case .halfFullUp(let scale):
        // 保留一半有效数字，向上取整
        let effectiveScale = scale//min(scale, Int(floor(Double(scale) / 2.0)))
        let multiplier = pow(10.0, Double(effectiveScale))
        let roundedValue = ceil(value * multiplier) / multiplier
        return NumberFormatterTool.shared.format(roundedValue, scale: effectiveScale)
    }
}

/// 格式化百分比显示
/// - Parameters:
///   - number: 要格式化的数字（可以是 String 或 Double 类型）
///   - accuracy: 精度要求
///   - hundredTimes: 是否需要乘以100（默认false）
///   - positivePrefix: 正数前缀（默认为空）
///   - negativePrefix: 负数前缀（默认为"-"）
///   - zeroPrefix: 零值前缀（默认为空）
/// - Returns: 格式化后的百分比字符串
public func ShowPercent(_ number: Any,
                         _ accuracy: ShowNumberAccuracy = .down(2),
                         hundredTimes: Bool = false,
                         positivePrefix: String = "",
                         negativePrefix: String = "-",
                         zeroPrefix: String = "") -> String {
    // 处理无效输入
    if let str = number as? String, str.isEmpty || str == "--" {
        return "--"
    }
    
    // 转换为 Double
    var value: Double = 0
    if let doubleValue = number as? Double {
        value = doubleValue
    } else if let stringValue = number as? String, let doubleValue = Double(stringValue) {
        value = doubleValue
    } else {
        return "--"
    }
    
    // 处理特殊值
    if value.isNaN || value.isInfinite {
        return "--"
    }
    
    // 如果需要乘以100
    if hundredTimes {
        value *= 100
    }
    
    // 确定前缀
    let prefix: String
    if abs(value) < Double.ulpOfOne {
        prefix = zeroPrefix
    } else if value > 0 {
        prefix = positivePrefix
    } else {
        prefix = negativePrefix
    }
    
    // 格式化数值部分
    let absValue = abs(value)
    let formattedNumber = ShowNumber(absValue, accuracy)
    
    // 组合最终结果
    return prefix + formattedNumber + "%"
} 
