//
//  AlertPopView.swift
//  KLineDemo
//
//  Created by <PERSON> on 2025/3/13.
//

import UIKit

public typealias ConfirmBlock = () -> Void

open class AlertPopView: UIView {
    var titleLabel: UILabel!
    var descTV: UITextView!
    var confirmButton: UIButton!
    var leftConfirmButton: UIButton!
    private var confirmBlock: ConfirmBlock?

    public init(title: String, desc: String, confirm: String, leftConfirm: String, theme: Any?) {
        super.init(frame: .zero)

        setupUI()
        titleLabel.text = title
        descTV.text = desc
        confirmButton.setTitle(confirm, for: .normal)
        leftConfirmButton.setTitle(leftConfirm, for: .normal)

        // 可以根据 theme 进行一些样式设置
        // 这里暂时不做具体处理，你可以根据实际需求添加逻辑
    }

    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        backgroundColor = UIColor.white
        layer.cornerRadius = 10
        layer.masksToBounds = true

        titleLabel = UILabel()
        titleLabel.font = UIFont.boldSystemFont(ofSize: 18)
        titleLabel.textAlignment = .center
        addSubview(titleLabel)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: topAnchor, constant: 20),
            titleLabel.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -20)
        ])

        descTV = UITextView()
        descTV.font = UIFont.systemFont(ofSize: 14)
        descTV.isEditable = false
        descTV.isSelectable = false
        addSubview(descTV)
        descTV.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            descTV.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 10),
            descTV.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 20),
            descTV.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -20)
        ])

        let buttonStackView = UIStackView()
        buttonStackView.axis = .horizontal
        buttonStackView.spacing = 10
        buttonStackView.distribution = .fillEqually
        addSubview(buttonStackView)
        buttonStackView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            buttonStackView.topAnchor.constraint(equalTo: descTV.bottomAnchor, constant: 20),
            buttonStackView.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 20),
            buttonStackView.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -20),
            buttonStackView.bottomAnchor.constraint(equalTo: bottomAnchor, constant: -20),
            buttonStackView.heightAnchor.constraint(equalToConstant: 40)
        ])

        leftConfirmButton = UIButton(type: .system)
        leftConfirmButton.backgroundColor = UIColor.lightGray
        leftConfirmButton.setTitleColor(UIColor.white, for: .normal)
        leftConfirmButton.layer.cornerRadius = 5
        leftConfirmButton.addTarget(self, action: #selector(leftConfirmButtonTapped), for: .touchUpInside)
        buttonStackView.addArrangedSubview(leftConfirmButton)

        confirmButton = UIButton(type: .system)
        confirmButton.backgroundColor = UIColor.blue
        confirmButton.setTitleColor(UIColor.white, for: .normal)
        confirmButton.layer.cornerRadius = 5
        confirmButton.addTarget(self, action: #selector(confirmButtonTapped), for: .touchUpInside)
        buttonStackView.addArrangedSubview(confirmButton)
    }

    @objc private func confirmButtonTapped() {
        confirmBlock?()
        removeFromSuperview()
    }

    @objc private func leftConfirmButtonTapped() {
        removeFromSuperview()
    }

    public func show(confirmBlock: ConfirmBlock?) {
        self.confirmBlock = confirmBlock
        if let window = UIApplication.shared.windows.first {
            window.addSubview(self)
            self.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                self.centerXAnchor.constraint(equalTo: window.centerXAnchor),
                self.centerYAnchor.constraint(equalTo: window.centerYAnchor),
                self.widthAnchor.constraint(equalToConstant: 300),
                self.heightAnchor.constraint(equalToConstant: 200)
            ])
        }
    }
}
