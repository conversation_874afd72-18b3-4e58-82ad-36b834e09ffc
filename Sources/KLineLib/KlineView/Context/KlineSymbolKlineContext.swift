//
//  KlineSymbolKlineContext.swift
//  UseKlineToolsDemo
//
//  Created by <PERSON> on 2025/3/31.
//

import Foundation
open class KlineSymbolKlineContext {
    public weak var delegate: KDCDelegate?
    /// 跨线程还是使用一个信号量来控制读写串行
    private var p_mutex = DispatchSemaphore(value: 1)
    /// K线数据
    public var kLineList = [KLineModel]()
    private let queue: DispatchQueue
    /// 标识是否销毁
    public var isDestory = false
    public init(queue: DispatchQueue) {
        self.queue = queue
    }
    //MARK: Michael 定义一个方法 解决报错问题
    public func readKlineData(complettion:([KLineModel])->Void){
        complettion(self.kLineList)
    }
    public func destoryContext() {
        isDestory = true
    }

    //MARK: Michael: 暂时不知道闭包传入什么值
    public func reloadKLineTimePostion(contentSize:CGSize,completion:@escaping ([KLineModel])->Void){
        
        queue.async { [weak self] in
            guard let self = self else { return }
            
            
            self.kLineList.forEach { $0.isShowTime = false }
            if KLineConfig.type == .fiveDayLine{
                let timestamp = self.kLineList.map({$0.timestamp}).map({$0.timestampToDateString(format: "MM,dd")}).uniqued()
                timestamp.forEach { target in
                    if let index = self.kLineList.firstIndex(where: {$0.timestamp.timestampToDateString(format: "MM,dd") == target}){
                        self.kLineList[index].isShowTime = true
                    }
                }
                
            }else{
                if KLineConfig.displayAllData{
                    let step = kLineList.count / KLineConfig.verticalLineCount
                    for i in 0..<self.kLineList.count {
                        let index = step * i
                        if index < self.kLineList.count {
                            self.kLineList[index].isShowTime = true
                        }
                    }
                }else{
                    // K线宽度
                    let step = contentSize.width / CGFloat(KLineConfig.verticalLineCount)
                    let klineWidth = (KLineConfig.kLineGap + KLineConfig.kLineWidth)
                    for i in 0..<self.kLineList.count {
                        let index = Int(step * CGFloat(i) / klineWidth)
                        if index < self.kLineList.count {
                            self.kLineList[index].isShowTime = true
                        }
                    }
                }
                
            }

            self.p_mutex.wait()
            DispatchQueue.main.async {
                completion(self.kLineList)
                self.p_mutex.signal()
            }
        }
    
    }
    public func reloadKLineIndicator() {
        queue.async { [weak self] in
            guard let self = self else { return }
            self.p_notifyKLineListDidChange(redraw: false)
        }
    }
    private func p_notifyKLineListDidChange(redraw: Bool) {
        queue.async { [weak self] in
            guard let self = self else { return }
            self.p_klineModelDidUpdate()
            asyncOnMain { [weak self] in
                guard self?.isDestory == false else { return }
                self?.delegate?.kLineListDidUpdate(needRewDraw: redraw)
            }
        }
    }
    private func p_klineModelDidUpdate() {
        KLineIndicatorTools.calculationIndicator(models: self.kLineList)
    }
}
