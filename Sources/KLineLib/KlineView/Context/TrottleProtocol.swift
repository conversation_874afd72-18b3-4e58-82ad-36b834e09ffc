//
//  TrottleProtocol.swift
//  UseKlineToolsDemo
//
//  Created by <PERSON> on 2025/3/31.
//

import Foundation
/// 使用Trottle的结构遵循的协议
public protocol TrottleProtocol {}

public typealias ThrottleBlock = () -> Void

private var throttleMap = [String: ThrottleBlock]()

extension TrottleProtocol {
    /// 外层必须保证key是唯一的.
    public func throttle(_ delay: TimeInterval,
                         _ respondQueue: DispatchQueue = DispatchQueue.main,
                         _ block: @escaping ThrottleBlock,
                         _ file: String = #file,
                         _ function: String = #function,
                         _ line: Int = #line) {
        asyncOnMain {
            let key = file + function + "\(line)"
            if throttleMap[key] == nil {
                throttleMap[key] = block
                respondQueue.async { block() }
                
                DispatchAfter(delay) {
                    throttleMap.removeValue(forKey: key)
                }
            }
        }
    }
    
    public func throttle(delay: TimeInterval,
                         uniqueKey: String,
                         respondQueue: DispatchQueue = DispatchQueue.main,
                         block: @escaping ThrottleBlock) {
        asyncOnMain {
            if throttleMap[uniqueKey] == nil {
                throttleMap[uniqueKey] = block
                respondQueue.async { block() }
                DispatchAfter(delay) {
                    throttleMap.removeValue(forKey: uniqueKey)
                }
            }
        }
    }
    
    public static func classThrottle(delay: TimeInterval,
                                     uniqueKey: String,
                                     respondQueue: DispatchQueue = DispatchQueue.main,
                                     block: @escaping ThrottleBlock) {
        asyncOnMain {
            if throttleMap[uniqueKey] == nil {
                throttleMap[uniqueKey] = block
                respondQueue.async { block() }
                DispatchAfter(delay) {
                    throttleMap.removeValue(forKey: uniqueKey)
                }
            }
        }
    }
}
open class Debouncer {
    private let delay: TimeInterval
    private var workItem: DispatchWorkItem?
    
    public init(delay: TimeInterval) {
        self.delay = delay
    }
    
    public func debounce(action: @escaping () -> Void) {
        workItem?.cancel()
        let newWorkItem = DispatchWorkItem(block: action)
        workItem = newWorkItem
        DispatchQueue.main.asyncAfter(deadline: .now() + delay, execute: newWorkItem)
    }
}
