//
//  FILE.swift
//  Ourbit
//
//  Created by pippen on 2024.
//

import UIKit

/// 呼吸灯效果
open class GradientCycleView: UIView {
    public var animationAlpha: CGFloat = 0.6
    public var needAnimation = false
    public var isAnimationing = false
    
    public lazy var gradientView: GradantView = {
        let v = GradantView()
        v.alpha = self.animationAlpha
        return v
    }()
    
    public lazy var pointView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        v.layer.cornerRadius = 4.0
        v.layer.masksToBounds = true
        return v
    }()
    
    public override init(frame: CGRect) {
        super.init(frame: frame)
        
        self.setupUI()
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    open override func layoutSubviews() {
        super.layoutSubviews()
        
        self.layer.cornerRadius = self.frame.size.width / 2.0
    }
    
    public func setupUI() {
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(willEnterForeground),
                                               name: UIApplication.willEnterForegroundNotification,
                                               object: nil)
        self.backgroundColor = UIColor.clear
        self.layer.masksToBounds = true
        self.clipsToBounds = true
        
        self.addSubview(gradientView)
        gradientView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        self.addSubview(pointView)
        pointView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(4.0)
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    @objc public func willEnterForeground() {
        self.stopAnimation()
        self.startAnimation()
    }
    
    public func startAnimation() {
        self.needAnimation = true
        
        if self.isAnimationing == false {
            self.playAnimation()
        }
        self.isAnimationing = true
    }
    
    public func stopAnimation() {
        self.needAnimation = false
        self.isAnimationing = false
        self.gradientView.alpha = self.animationAlpha
    }
    
    public func playAnimation() {
        // swiftlint:disable:next all
        UIView.animate(withDuration: 0.2, delay: 0.2, options: UIView.AnimationOptions.curveEaseInOut) {
            if self.gradientView.alpha == 0.0 {
                self.gradientView.alpha = self.animationAlpha
            } else {
                self.gradientView.alpha = 0.0
            }
        } completion: { finish in
            if finish && self.needAnimation {
                self.playAnimation()
            }
        }
    }
}
