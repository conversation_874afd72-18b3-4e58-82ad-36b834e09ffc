//
//  FILE.swift
//  Ourbit
//
//  Created by pippen on 2024.
//

import UIKit

/// 渐变圆
open class GradantView: UIView {
    public override init(frame: CGRect) {
        super.init(frame: frame)
        
        self.backgroundColor = UIColor.clear
        self.layer.masksToBounds = true
        self.clipsToBounds = true
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // Only override draw() if you perform custom drawing.
    // An empty implementation adversely affects performance during animation.
    open override func draw(_ rect: CGRect) {
        // Drawing code
        self.layer.cornerRadius = rect.size.width / 2.0
        // 创建色彩空间对象
        let rgb = CGColorSpaceCreateDeviceRGB()
        // 创建起点和终点颜色分量的数组
        let colors = [
            UIColor("#8A96A4", 1.0).cgColor,
            UIColor("#8A96A4", 0.0).cgColor
        ]
        let locations: [CGFloat] = [0.0, 1.0]
        // 形成梯形，渐变的效果
        guard let gradient = CGGradient(colorsSpace: rgb, colors: colors as CFArray, locations: locations) else { return }
        // 起点颜色起始圆心
        let startPoint = CGPoint(x: rect.size.width / 2.0, y: rect.size.height / 2.0)
        // 终点颜色起始圆心
        let endPoint = CGPoint(x: rect.size.width / 2.0, y: rect.size.height / 2.0)
        // 起点颜色圆形半径
        let startRadius = 0.0
        // 终点颜色圆形半径
        let endRadius: CGFloat = rect.size.width / 2.0 - 2.0
        // 获取上下文
        guard let graCtx = UIGraphicsGetCurrentContext() else { return }
        // 创建一个径向渐变
        // swiftlint:disable:next all
        let options = CGGradientDrawingOptions.init(rawValue: CGGradientDrawingOptions.drawsBeforeStartLocation.rawValue | CGGradientDrawingOptions.drawsAfterEndLocation.rawValue)
        graCtx.drawRadialGradient(gradient,
                                  startCenter: startPoint,
                                  startRadius: startRadius,
                                  endCenter: endPoint,
                                  endRadius: endRadius,
                                  options: options)
    }
}
