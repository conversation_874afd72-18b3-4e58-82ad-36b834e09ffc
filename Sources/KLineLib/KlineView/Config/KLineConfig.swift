//
//  FILE.swift
//  Ourbit
//
//  Created by pippen on 2024.
//

import Foundation
import UIKit
public enum kLineTimeViewPosition:Int{
    case mainViewBottom = 0
    case subViewBottom
}
public enum KLinePricePosition{
    case right(offset:CGFloat = 0)
    case left(offset:CGFloat = 0)
}
/// K线类型
public enum KLineType: Int {
    /// 简单版
    case simple
    /// 专业版
    case professional
}

/// 时间显示类型
public enum KLineTimeType {
    /// 年月日时分
    case YMDHM
    /// 月日时分
    case MDHM
    /// 时分
    case HM
    /// 年月日
    case YMD
    /// 月日
    case MD
    /// 时分秒
    case HMS
    /// 自定义
    case Other(String)
    
    public var dateFormat: String {
        switch self {
        case .YMDHM:    return "yy-MM-dd HH:mm"
        case .MDHM:     return "MM-dd HH:mm"
        case .HM:       return "HH:mm"
        case .YMD:      return "yy-MM-dd"
        case .MD:       return "MM-dd"
        case .HMS:       return "HH:mm:ss"
        case .Other(let format): return format
        }
    }
}

/// K线展示类型
public enum KLineViewType:Int {
    /// 分时线
    case timeLine = 0
    /// K线柱状线
    case kline = 1
    /// 美国线
    case americanLine
    /// 空心蜡烛
    case hollowCandle
    /// 折线图
    case brokenLine
    /// 五日分时
    case fiveDayLine
}

/// K线弹窗类型
public enum KLineTapViewType: Int {
    /// 浮动弹窗
    case pop
    /// 顶部弹窗
    case top
    /// 不显示弹窗
    case no
}

/// K线主视图展示类型
public enum KLineMainViewType: Int {
    /// MA线
    case ma
    /// EMA线
    case ema
    /// WMA线
    case wma
    
    /// 关闭线
    case close
    /// SAR线
    case sar
    /// BOLL线
    case boll
    ///均价线
    case vwap
    public var title: String {
        switch self {
        case .ma: return "SMA"
        case .ema: return "EMA"
        case .wma: return "WMA"
        case .boll: return "BOLL"
        case .close: return "CLOSE"
        case .sar: return "SAR"
        case .vwap: return "VWAP"
        }
    }
    
    public var key: String {
        switch self {
        case .sar: return "SAR(APP)"
        default: return self.title
        }
    }
    
    public var max: Double {
        switch self {
        case .sar: return 1
        default: return 999
        }
    }
}

/// K线副视图展示类型
public enum KLineSubViewType: Int {
    /// 成交量
    case vol = 0
    /// RSI线
    case rsi
    /// MACD线
    case macd
    /// KDJ线
    case kdj
    
    /// WR线
    case wr
    
    /// OBV线
    case obv
    /// ROC线
    case roc
    /// CCI线
    case cci
    /// StochRSI线
    case stochRSI
    /// TRIX指标
    case trix
    /// DMI指标
    case dmi
    
    /// 关闭线
    case close

    public var title: String {
        switch self {
        case .macd: return "MACD"
        case .kdj: return "KDJ"
        case .rsi: return "RSI"
        case .wr: return "WR"
        case .close: return "CLOSE"
        case .obv: return "OBV"
        case .stochRSI: return "StochRSI"
        case .roc: return "ROC"
        case .cci: return "CCI"
        case .vol: return "VOL"
        case .trix: return "TRIX"
        case .dmi: return "DMI"
        }
    }
    
    public var key: String { return self.title }
}

// swiftlint:disable:next all
open class KLineConfig {
    /// K线展示类型
    public static var kLineType: KLineType{
        set{
            UserDefaults.cachedKLineType = newValue
        }
        get{
            return UserDefaults.cachedKLineType
        }
        
    }
    public static var themeManager: ThemeColorProtocol = KLineLibDefaultThemeColor.shared
    
    /// 主图中价格位置
    public static var pricePosition:KLinePricePosition = .left(offset: 5)
    /// K线距上顶最小边界距离
    public static var kLineMainViewMinY: CGFloat = 7
    /// K线距下底最小边界距离
    public static var kLineMainViewMaxY: CGFloat = 7
    /// 背景网格横多少格
    public static var horizontalLineCount: Int = 5
    /// 背景网格竖多少格
    public static var verticalLineCount: Int = 5
    
    /// 是否将指标值显示到对于的View上
    public static var displayindexValueOnView:Bool = true
    /// 是否将所以数据完整显示 默认false
    public static var displayAllData:Bool = false
    
    /// 所以副视图距离上一个视图的之间的间隙
    public static var subVeiwHeaderHeight:CGFloat = 0.0
    /// 显示完整数据时数据未完全加载时 预计的总条数 为传入预计总条数时将已有总条数铺满整个界面 需将 displayAllData 设为True 才会生效
    public static var expectedTotalDataCount:Int? = nil
    /// 自定义垂直网格线 及时间轴（时间轴不随滚动）
    public static var customGridModel:CustomGridModel? = nil
    /// K线主视图最小高度比
    public static var kLineMainViewRatio: CGFloat = 0.6
    /// K线副视图高度
    public static var kLineSubViewHeight: CGFloat = 62.0
    /// 时间显示类型
    public static var timeType: KLineTimeType = .HM
    /// K线展示类型
    public static var type: KLineViewType = .timeLine{
        didSet{
            switch type {
                //MARK: Michael: 当type为 fiveDayLine 时 需自动显示 所有数据
            case  .fiveDayLine:
                KLineConfig.displayAllData = true
              DispatchQueue.main.async {
                    if KLineConfig.expectedTotalDataCount == nil || (KLineConfig.expectedTotalDataCount ?? 0) <= 0{
                        assert(false, "未设置五日分时的总数据量,会导致显示错误")
                    }
                }
            default:break
            }
        }
    }
    /// K线主视图展示类型
    public static var mainViewType: [KLineMainViewType] = UserDefaults.cachedKLineMainType
    /// K线副视图展示类型
    public static var subViewType: [KLineSubViewType] {
        get {
            return UserDefaults.cachedKLineSubTypeList
        }
        set {
            UserDefaults.cachedKLineSubTypeList = newValue
        }
    }
    
    /// 点击弹窗显示类型
    public static var tapType: KLineTapViewType {
        get {
            let type = UserDefaults.cachedKLineTapViewType
            if liteKLineStyle, type == .top { return .pop }
            return type
        }
        set {
            UserDefaults.cachedKLineTapViewType = newValue
        }
    }
    /// 柱间距
    public static var kLineGap: CGFloat = 4.0
    /// 柱宽度
    public static var kLineWidth: CGFloat = 7.0
    /// 柱最小宽度
    public static var kLineMinWidth: CGFloat = 1.0
    /// 柱最大宽度
    public static var kLineMaxWidth: CGFloat = 30.0
    /// K线缩放因子
    public static var kLineScaleFactor: CGFloat = 0.03
    /// K线缩放界限
    public static var kLineScaleBound: CGFloat = 0.03
    /// 时间高度
    public static var kLineTimeViewHeight: CGFloat = 20.0
    
    /// 时间轴视图放在哪个View的下方
    public static var kLineTimeViewWithBottomView:kLineTimeViewPosition = .mainViewBottom
    /// K线右边界距离
    public static var kLineRightSpaceRatio: CGFloat = 2.0
    
    /// 默认MA
    public static var MAArray:[Int] = [5, 10, 30, 60]
    /// 默认EMA
    public static var EMAArray:[Int] = [6, 12, 20]
    public static var WMAArray:[Int] = [5, 10, 12]
    
    /// 默认指标颜色 主视图的 MA用的都是这个颜色数组
    public static var MAHexColor = themeManager.maIndicatorColors()
    /// 默认指标颜色 主视图的 EMA 用的都是这个颜色数组
    public static var EMAHexColor = themeManager.emaIndicatorColors()
    
    /// 默认指标颜色 主视图的 WMA 用的都是这个颜色数组
    public static var WMAHexColor = themeManager.wmaIndicatorColors()
    
    /// 量MA线
    public static var volMAArrayDefault: [Int] = [5, 10]
    public static var volMAArray: [Int] = [5, 10]
    public static var volMAColorArray: [UIColor] = themeManager.volIndicatorColors()
    /// BOLL线
    public static var bollArrayDefault: [Int] = [20, 2]
    public static var bollArray: [Int] = [20, 2]
    public static var bollColorArray: [UIColor] = themeManager.bollIndicatorColors()
    /// SAR线
    public static var sarArrayDefault: [String] = ["0.02", "0.2"]
    public static var sarArray: [String] = ["0.02", "0.2"]
    public static var sarColorArray: [UIColor] = themeManager.sarIndicatorColors()
    /// MACD线
    /*
     
     参数组合    适用场景    敏感度
     (12, 26, 9)    日线级别    中
     (6, 13, 5)    短线交易    高
     (24, 52, 18)    周线级别    低
     
     */
    public static var macdArrayDefault: [Int] = [12, 26, 9]
    public static var macdArray: [Int] = [12, 26, 9]
    public static var macdColorArray: [UIColor] = themeManager.macdIndicatorColors()
    /// KDJ线
    public static var kdjArrayDefault: [Int] = [9, 3, 3]
    public static var kdjArray: [Int] = [9, 3, 3]
    public static var kdjColorArray: [UIColor] = themeManager.kdjIndicatorColors()
    /// RSI线
    public static var rsiArrayDefault: [Int] = [6, 12, 24]
    public static var rsiArray: [Int] = [6, 12, 24]
    public static var rsiColorArray: [UIColor] = themeManager.rsiIndicatorColors()
    /// WR线
    public static var wrArrayDefault: [Int] = [6, 10, 14]
    public static var wrArray: [Int] = [6, 10, 14]
    public static var wrColorArray: [UIColor] = themeManager.wrIndicatorColors()
    /// StochRSI线
    public static var stochRSIArrayDefault: [Int] = [14, 14, 3, 3]
    public static var stochRSIArray: [Int] = [14, 14, 3, 3]
    public static var stochRSIColorArray: [UIColor] = themeManager.stochRSIIndicatorColors()
    
    /// OBV线
    public static var obvArrayDefault: [Int] = [30]
    public static var obvArray: [Int] = [30]
    public static var obvColorArray: [UIColor] = themeManager.obvIndicatorColors()
    
    /// ROC线
    public static var rocArrayDefault: [Int] = [12, 6]
    public static var rocArray: [Int] = [12, 6]
    public static var rocColorArray: [UIColor] = themeManager.rocIndicatorColors()
    
    /// CCI线
    public static var cciArrayDefault: [Int] = [14]
    public static var cciArray: [Int] = [14]
    public static var cciColorArray: [UIColor] = themeManager.cciIndicatorColors()
    
    /// TRIX线
    public static var trixArrayDefault: [Int] = [12]
    public static var trixArray: [Int] = [12]
    public static var trixColorArray: [UIColor] = themeManager.trixIndicatorColors()
    
    /// DMI线
    public static var dmiArrayDefault: [Int] = [14, 14]
    public static var dmiArray: [Int] = [14, 14]
    public static var dmiColorArray: [UIColor] = themeManager.dmiIndicatorColors()
    
    public static var liteKLineStyle = false
    
    
    /// 需要显示参考线的配置
    public static var references:[SubReferenceLineModel]? = nil
    
    /// 当前价格背景颜色
    public static func priceBgColor() -> UIColor {
        return themeManager.mainGreenColor().withAlphaComponent(0.1)
    }
    
    /// 自动隐藏infoView 的时间 0 不自动隐藏  > 0 延迟n s隐藏
    public static var autoHiddeninfoViewTime:TimeInterval = 2
    
    /// 是否开启单击 显示infoView
    public static var clickToDisplayInfoView:Bool = false
    
    
    /// 是否对infoView进行单独控制（使用默认info View 视图时）
    public static var infoViewSetSeparatelyHorizontalLine = true
    
    /// 时间轴
    public static var timeAxisTimeZone:TimeZone = .current
    
    /// 时间轴
    public static var timeAxisLocale:Locale = .current
  
  /// 最新价颜色是否根据前一个交易日收市价变化 false:一直显示主题绿色 ture:根据当前价与前一个交易日收市价比较 显示涨跌颜色
    public static var latestPriceColorWithPreviousDay = false
  
  /// 主视图Y轴参考线是否使用颜色
    public static var mainViewYaxisValueUseColor:Bool = false
  
  /// 主视图Y轴是否显示涨跌幅Value
    public static var displayMainViewYaxisChangeRate:Bool = false
}


