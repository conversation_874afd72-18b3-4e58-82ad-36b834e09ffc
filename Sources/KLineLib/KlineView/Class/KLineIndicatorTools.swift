//
//  KLineIndicatorViewModel.swift
//  UseKlineToolsDemo
//
//  Created by <PERSON> on 2025/3/31.
//

import Foundation
//MARK: Michael: 这个类是K线图 中计算主视图 和副视图 中线条的主要类
open class KLineIndicatorTools {
    
    public init() {}
    
    /// 计算K线指标
    public static func calculationIndicator(models: [KLineModel]) {
      
        for type in KLineConfig.mainViewType {
            switch type {
            case .vwap:
              ChiefCalculateTools.calculateAveragePriceWith(klineModels: models)
            case .ma:
                ChiefCalculateTools.calculateMainSMAOptimizeds(klineModes: models, periods: KLineConfig.MAArray)
            case .ema:
                ChiefCalculateTools.calculateEMAs(klineModes: models, periods: KLineConfig.EMAArray)
            case .wma:
                ChiefCalculateTools.calculateWMAs(klineModes: models, periods: KLineConfig.WMAArray)
            case .boll:
                let period =  KLineConfig.bollArray[0]
                let multiplier = KLineConfig.bollArray[1]
                ChiefCalculateTools.calculationBOLL(klineModes: models, period: period, stdDevMultiplier: multiplier)
            case .sar:
                let acc = KLineConfig.sarArray[0].doubleValue()
                let maxAcc = KLineConfig.sarArray[1].doubleValue()
                let initial: Double? = KLineConfig.sarArray[safe: 2]?.doubleValue()
                ChiefCalculateTools.calculationSAR(klineModes:models,defaultAF: acc,maxAF: maxAcc,initialValue: initial)
            default: break
            }
        }
        
        // 小k线不用计算副指标
        guard !KLineConfig.liteKLineStyle else { return }
        
      ChiefCalculateTools.calculateSubSMAOptimizeds(klineModes: models, periods: [5,10])
        
        for item in KLineConfig.subViewType {
            switch item {
            case .macd:
                let shortPeriod = KLineConfig.macdArray[0]
                let longPeriod = KLineConfig.macdArray[1]
                let signalPeriod = KLineConfig.macdArray[2]
                
                ChiefCalculateTools.calculateMACD(klineModes: models, shortPeriod: shortPeriod, longPeriod: longPeriod, signalPeriod: signalPeriod)
            case .kdj:
                let n = KLineConfig.kdjArray[0]
                let m1 = KLineConfig.kdjArray[1]
                let m2 = KLineConfig.kdjArray[2]
                ChiefCalculateTools.calculationKDJ(klineModes: models, n: n, m1: m1, m2: m2)
            case .rsi:
                ChiefCalculateTools.calculateRSI(klineModes: models, periods: KLineConfig.rsiArray)
            case .wr:
                ChiefCalculateTools.calculateWROptimized(klineModes: models, periods: KLineConfig.wrArray)
            case .obv:
                let periods = KLineConfig.obvArray[0]
                ChiefCalculateTools.calculationModelOBV(klineModes: models, periods:periods )
            case .stochRSI:
                let period = KLineConfig.stochRSIArray[0]
                let rsiPeriod = KLineConfig.stochRSIArray[1]
                let kPeriod = KLineConfig.stochRSIArray[2]
                let dPeriod = KLineConfig.stochRSIArray[3]
                
                ChiefCalculateTools.calculationStochRSI(klineModes: models, period: period, rsiPeriod: rsiPeriod, kPeriod: kPeriod, dPeriod: dPeriod)
            case .roc:
                let period = KLineConfig.rocArray[0]
                let maPeriod = KLineConfig.rocArray[1]
                ChiefCalculateTools.calculationModelROC(klineModes: models, period: period, maPeriod: maPeriod)
            case .cci:
                let period = KLineConfig.cciArray[0]
                ChiefCalculateTools.calculationModelCCI(klineModes: models, period: period)
            case .dmi:
                let di = KLineConfig.dmiArray[0]
                let adx = KLineConfig.dmiArray[1]
                ChiefCalculateTools.calculationDMI(klineModes: models, di: di, adx: adx)
            case .trix:
                let period = KLineConfig.trixArray[0]
                ChiefCalculateTools.calculationTRIX(klineModes: models, period: period)
            default: break
            }
        }
    }
  /// 获取上一个交易日的最后一条数据
  /// - Parameter targetModel: 基准Model
  /// - Returns: 上一个交易日的最后一条数据
  public static func getPreviousTradingDayLastModel(kLineModels:[KLineModel],targetModel:KLineModel)->KLineModel?{
    guard kLineModels.count > 0 else{return nil}
    let targetDate = targetModel.timestamp.timestampToDateString(format: "yyyy,MM,dd")
    if let index = kLineModels.firstIndex(where: {$0.timestamp.timestampToDateString(format: "yyyy,MM,dd") == targetDate}),
       index - 1 >= 0{
      let previousTradingDayLastModel = kLineModels[index - 1]
      return previousTradingDayLastModel
    }
    return nil
  }
}
