//
//  ChiefCalculateTools.swift
//  UseKlineToolsDemo
//
//  Created by <PERSON> on 2025/4/2.
//

import Foundation

/// 最新的计算线条数据logic
open class ChiefCalculateTools{
  
  /// 计算均价
  /// - Parameter klineModels: -
  public static func calculateAveragePriceWith(klineModels:[KLineModel]){
    var sunAmount = 0.0
    var sunVolume = 0.0
    var tempDate = ""
    var lastAveragePrice = 0.0
    klineModels.forEach { model in
      let currentDate = model.timestamp.timestampToDateString(format: "MM,dd")
      if currentDate != tempDate{
        sunAmount = 0
        sunVolume = 0.0
        tempDate = currentDate
      }
      if model.amount != 0 , model.volume != 0 {
        sunAmount += model.amount
        sunVolume += model.volume
        lastAveragePrice = sunAmount / sunVolume
        model.averagePrice = lastAveragePrice
      }else{
        model.averagePrice = lastAveragePrice
      }
    }
  }
    /*================================================================*/
    /*================================================================*/
    //主视图
    /*================================================================*/
    /*================================================================*/
    /// 计算多条 主视图 MA 数据
    /// - Parameters:
    ///   - klineModes: klineModes
    ///   - periods: 计算的周期数组
    public static func calculateMainSMAOptimizeds(klineModes:[KLineModel], periods: [Int]) {
        let closes = klineModes.map { $0.close }//.reversed() // 时间倒序
        var resultList:[String:[String]] = [:]
        for period in periods {
            let current = calculateSMAOptimized(prices: closes, period: period)
            resultList["\(period)"] = current.map({ value in
                if let value = value{
                    return "\(value)"
                }else{
                    return ""
                }
            })
        }
        
        asyncOnMain {
            var index = 0
            klineModes.forEach { model in
                var dic:[String:String] = [:]
                for period in periods {
                    if let array = resultList["\(period)"]{
                        dic["\(period)"] = array[safe: index]
                    }
                }
                model.maDictionary = dic
                index += 1
            }
        }
    }
    /// 计算多条 副视图 MA 数据
    /// - Parameters:
    ///   - klineModes: klineModes
    ///   - periods: 计算的周期数组
    public static func calculateSubSMAOptimizeds(klineModes:[KLineModel], periods: [Int]) {
        let closes = klineModes.map { $0.volume }//.reversed() // 时间倒序
        var resultList:[String:[String]] = [:]
        for period in periods {
            let current = calculateSMAOptimized(prices: closes, period: period)
            resultList["\(period)"] = current.map({ value in
                if let value = value{
                    return "\(value)"
                }else{
                    return ""
                }
            })
        }
        
        asyncOnMain {
            var index = 0
            klineModes.forEach { model in
                var dic:[String:String] = [:]
                for period in periods {
                    if let array = resultList["\(period)"]{
                        dic["\(period)"] = array[safe: index]
                    }
                }
                model.volMADictionary = dic
                index += 1
            }
        }
    }
    /// 计算MA数据
    /// - Parameters:
    ///   - prices: 需要计算的数组
    ///   - period: 计算周期
    /// - Returns: -
    public static func calculateSMAOptimized(prices: [Double], period: Int) -> [Double?] {
        guard prices.count >= period, period > 0 else {
            return Array(repeating: nil, count: prices.count)
        }
        
        var result: [Double?] = Array(repeating: nil, count: prices.count)
        var windowSum = prices[0..<period].reduce(0, +)
        result[period-1] = windowSum / Double(period)
        
        for i in period..<prices.count {
            windowSum += prices[i] - prices[i - period]
            result[i] = windowSum / Double(period)
        }
        return result
    }
    /*================================================================*/
    
    /// 计算多周期EMA
    public static func calculateEMAs(klineModes:[KLineModel],periods: [Int]) {
        let closes = klineModes.map { $0.close }
        var resultList:[String:[String]] = [:]
        for period in periods {
            let current = calculateEMA(prices: closes, period: period)
            resultList["\(period)"] = current.map({ value in
                if let value = value{
                    return "\(value)"
                }else{
                    return ""
                }
            })
        }
        
        asyncOnMain {
            var index = 0
            klineModes.forEach { model in
                var dic:[String:String] = [:]
                for period in periods {
                    if let array = resultList["\(period)"]{
                        dic["\(period)"] = array[safe: index]
                    }
                }
                model.emaDictionary = dic
                index += 1
            }
        }
        
    }
    
    
    /// 计算EMA（指数移动平均）
    /// - Parameters:
    ///   - prices: 价格数组（时间顺序：旧→新）
    ///   - period: 计算周期
    /// - Returns: EMA数组（与输入长度相同，首项为SMA）
    public static func calculateEMA(prices: [Double], period: Int) -> [Double?] {
        guard prices.count >= period, period > 0 else {
            return Array(repeating: nil, count: prices.count)
        }
        
        let alpha = 2.0 / Double(period + 1)
        var emaValues: [Double] = []
        
        // 首项使用SMA
        let initialSMA = prices.prefix(period).reduce(0, +) / Double(period)
        emaValues.append(initialSMA)
        
        // 递推计算后续EMA
        for i in period..<prices.count {
            let prevEMA = emaValues.last!
            let currentEMA = (prices[i] * alpha) + (prevEMA * (1 - alpha))
            emaValues.append(currentEMA)
        }
        
        // 填充前period-1个nil（与常见图表软件一致）
        return Array(repeating: nil, count: period - 1) + emaValues
    }
    /*================================================================*/
    /// 计算多条 WMA 均线
    public static func calculateWMAs(klineModes:[KLineModel], periods: [Int]) {
        let closes = klineModes.map { $0.close }//.reversed() // 时间倒序
        var resultList:[String:[String]] = [:]
        for period in periods {
            let current = calculateWMASeries(prices: closes, period: period)
            resultList["\(period)"] = current.map({ value in
                if let value = value{
                    return "\(value)"
                }else{
                    return ""
                }
            })
        }
        
        asyncOnMain {
            var index = 0
            klineModes.forEach { model in
                var dic:[String:String] = [:]
                for period in periods {
                    if let array = resultList["\(period)"]{
                        dic["\(period)"] = array[safe: index]
                    }
                }
                model.wmaDictionary = dic
                index += 1
            }
        }
        
    }
    /// 计算整个序列的 WMA 数组
    public static func calculateWMASeries(prices: [Double], period: Int) -> [Double?] {
        guard prices.count >= period else {
            return Array(repeating: nil, count: prices.count)
        }

        return prices.indices.map { index in
            if index < period - 1 {
                return nil
            }
            let window = Array(prices[(index - period + 1)...index])
            return calculateWMA(prices: window, period: period)
        }
    }
    /// 计算加权移动平均 (WMA)
    /// - Parameters:
    ///   - prices: 价格数组（时间倒序，最新数据在前）
    ///   - period: 计算周期
    /// - Returns: WMA 值（数据不足时返回 nil）
    public static func calculateWMA(prices: [Double], period: Int) -> Double? {
        guard prices.count >= period, period > 0 else { return nil }
        
        let weights = (1...period).map { Double($0) } // 生成权重 [1, 2, ..., N]
        let selectedPrices = Array(prices.prefix(period)) // 取最近N个数据
        
        let weightedSum = zip(selectedPrices, weights)
            .map { $0 * $1 }
            .reduce(0, +)
        
        let weightSum = weights.reduce(0, +)
        return weightedSum / weightSum
    }
    
    
    
    /*================================================================*/
    /// 计算BOLL线
    /// BOLL_MD=标准差 二次方根【 下的 (n-1)天的 C-MA二次方 和】
    /// BOLL_MB=n-1 天的 MA
    /// BOLL_UP=MB + k * MD
    /// BOLL_DN=MB - k * MD
    /// BOLL_SUBMD_SUM=n 个 ( Cn - MA20)的平方和
    /// BOLL_SUBMD=当前的 ( Cn - MA20)的平方
    /// period: 平均日数(中轴)
    /// stdDevMultiplier:  标准差倍数
    public static func calculationBOLL(klineModes: [KLineModel], period: Int, stdDevMultiplier: Int)
    {
        guard period > 0, klineModes.count >= period else { return }
        klineModes.forEach {
            $0.mb = nil
            $0.up = nil
            $0.dn = nil
        }

        var rollingSum  = 0.0     // Σ close
        var rollingSumSq = 0.0    // Σ close²

        for i in 0..<klineModes.count {
            let c = klineModes[i].close
            rollingSum   += c
            rollingSumSq += c * c

            if i >= period {
                let expired = klineModes[i - period].close
                rollingSum   -= expired
                rollingSumSq -= expired * expired
            }

            if i >= period - 1 {
                let ma = rollingSum / Double(period)
                let variance = max(rollingSumSq / Double(period) - ma * ma, 0)
                let md = sqrt(variance)

                let model = klineModes[i]
                model.mb = String(ma)
                model.up = String(ma + Double(stdDevMultiplier) * md)
                model.dn = String(ma - Double(stdDevMultiplier) * md)
            }
        }
    }
    /*================================================================*/
    
    
    /// 计算SAR
    /// - Parameters:
    ///   - defaultAF: 加速因子
    ///   - maxAF: 加速因子上限
    ///   - initialValue: 初始值
    public static func calculationSAR(klineModes: [KLineModel], defaultAF:Double = 0.2, maxAF:Double = 2.0, initialValue: Double? = nil) {
        
        
        let highs = klineModes.map({$0.high})
        let lows = klineModes.map({$0.low})
        
        let result = calculateSAR(high: highs, low: lows, accelerationFactor: defaultAF, maxAccelerationFactor: maxAF, initialValue: initialValue)
        
        for i in 0..<klineModes.count {
            let model = klineModes[i]
            if i < result.count {
                model.sar = "\(result[i])"
            }
        }
    }
    //
    
    public static func calculateSAR(high: [Double],
                                    low: [Double],
                                    accelerationFactor: Double,
                                    maxAccelerationFactor: Double,
                                    initialValue: Double? = nil) -> [Double] {
        let length = high.count
        var sar = Array(repeating: 0.0, count: length)
        var trend = Array(repeating: 0.0, count: length)
        var ep = Array(repeating: 0.0, count: length)
        var acceleration = accelerationFactor
        let maxAcceleration = maxAccelerationFactor
        
        var currentHigh = 0.0
        var currentLow = 0.0
        
        for i in 0..<length {
            if i == 0 {
                trend[i] = 1
                // 使用初始值参数（如果提供），否则使用第一个K线的最低价
                sar[i] = initialValue ?? low[i]
                currentHigh = high[i]
                currentLow = low[i]
            } else {
                if trend[i - 1] == 1 {
                    if low[i] <= sar[i - 1] {
                        trend[i] = -1
                        sar[i] = currentHigh
                        currentLow = low[i]
                        acceleration = accelerationFactor
                    } else {
                        trend[i] = 1
                        sar[i] = sar[i - 1] + acceleration * (currentHigh - sar[i - 1])
                        if sar[i] > low[i - 1] {
                            sar[i] = low[i - 1]
                        }
                        if currentHigh < high[i] {
                            currentHigh = high[i]
                            ep[i] = currentHigh
                            acceleration += accelerationFactor
                            if acceleration > maxAcceleration {
                                acceleration = maxAcceleration
                            }
                        } else {
                            ep[i] = currentHigh
                        }
                    }
                } else {
                    if high[i] >= sar[i - 1] {
                        trend[i] = 1
                        sar[i] = currentLow
                        currentHigh = high[i]
                        acceleration = accelerationFactor
                    } else {
                        trend[i] = -1
                        sar[i] = sar[i - 1] + acceleration * (currentLow - sar[i - 1])
                        if sar[i] < high[i - 1] {
                            sar[i] = high[i - 1]
                        }
                        if currentLow > low[i] {
                            currentLow = low[i]
                            ep[i] = currentLow
                            acceleration += accelerationFactor
                            if acceleration > maxAcceleration {
                                acceleration = maxAcceleration
                            }
                        } else {
                            ep[i] = currentLow
                        }
                    }
                }
            }
        }
        
        return sar
    }
    
    /*================================================================*/
    /*================================================================*/
    //副视图
    /*================================================================*/
    /*================================================================*/
    
    /// 计算MACD线
    /// DIF=EMA（12）-EMA（26）
    /// 今日的DEA值（即MACD值）=前一日DEA*8/10+今日DIF*2/10
    /// MACD=2*（今日DIF-今日DEA）
    public static func calculateMACD(klineModes: [KLineModel],
                                     shortPeriod: Int,
                                     longPeriod: Int,
                                     signalPeriod: Int){
        
        //        let shortPeriod = KLineConfig.macdArray[0]
        //        let longPeriod = KLineConfig.macdArray[1]
        //        let signalPeriod = KLineConfig.macdArray[2]
        var index = 0
        var sumValue: Double = 0.0
        
        guard shortPeriod >= 0, longPeriod >= 0, signalPeriod >= 0 else { return}
      klineModes.forEach({
        $0.dif = 0
        $0.dea = 0
        $0.macd = 0
      })
        klineModes.forEach { model in
            sumValue += model.close
            model.sumValue = sumValue
            
            if model.dif == 0 || model.dea == 0  || model.macd == 0  {
                if index == 0 {
                    model.ema1 = model.close
                    model.ema2 = model.close
                } else {
                    let preEma1 = klineModes[index - 1].ema1
                    let value1 = (Double(2.0) * model.close + Double(shortPeriod - 1) *
                                  preEma1) / Double(shortPeriod + 1)
                    model.ema1 = value1
                    let preEma2 = klineModes[index - 1].ema2
                    let value2 = (Double(2.0) * model.close + Double(longPeriod - 1) *
                                  preEma2) / Double(longPeriod + 1)
                    model.ema2 = value2
                }
                
                model.dif = model.ema1 - model.ema2
                
                if model.dif != 0 {
                    let num = Double(signalPeriod + 1)
                    if index == 0 {
                        model.dea = model.dif * Double(2.0) / num
                    } else {
                        let preDea = klineModes[index - 1].dea
                        model.dea = (preDea * Double(signalPeriod - 1) + model.dif * Double(2.0)) / num
                    }
                    model.macd = Double(2.0) * (model.dif - model.dea)
                }
            }
            index += 1
        }
        
        
    }
    
    
    /*================================================================*/
    /// 计算KDJ线
    /// KDJ(9,3.3),下面以该参数为例说明计算方法。
    /// 9，3，3代表指标分析周期为9天，K值D值为3天
    /// RSV(9)=（今日收盘价－9日内最低价）÷（9日内最高价－9日内最低价）×100
    /// K(3日)=（当日RSV值+2*前一日K值）÷3
    /// D(3日)=（当日K值+2*前一日D值）÷3
    /// J=3K－2D
    public static func calculationKDJ(klineModes: [KLineModel],n:Int,m1:Int,m2:Int) {
        var index = 0
        guard m1 > 0, m2 > 0 else { return }
      klineModes.forEach({
        $0.j = 0
        $0.d = 0
        $0.k = 0
      })
        klineModes.forEach { model in
            if model.j == 0 || model.d == 0  || model.k == 0  {
                var rsv: Double = 0.0
                
                if index >= n - 1 {
                    var count = index
                    var nClocksMinPrice = model.low
                    var nClocksMaxPrice = model.high
                    
                    // swiftlint:disable:next all
                    while count >= index - n + 1, count >= 0 {
                        let tempModel = klineModes[count]
                        
                        if nClocksMinPrice > tempModel.low {
                            nClocksMinPrice = tempModel.low
                        }
                        
                        if nClocksMaxPrice < tempModel.high {
                            nClocksMaxPrice = tempModel.high
                        }
                        count -= 1
                    }
                    
                    if nClocksMinPrice == nClocksMaxPrice {
                        rsv = 0.0
                    } else {
                        rsv = (model.close - nClocksMinPrice) * Double(100.0) /
                        (nClocksMaxPrice - nClocksMinPrice)
                    }
                    
                    var preK = "50"
                    var preD = "50"
                    if index > 0 {
                        preK = "\(klineModes[index - 1].k)"
                        preD = "\(klineModes[index - 1].d)"
                    }
                    model.k = (rsv + Double(m1 - 1) * preK.doubleValue()) / Double(m1)
                    model.d = (model.k + Double(m2 - 1) * preD.doubleValue()) / Double(m2)
                    model.j = Double(3) * model.k - Double(2) * model.d
                }
            }
            index += 1
        }
    }
    
    
    /*================================================================*/
    
    /// 计算多期数RSI
    /// - Parameters:
    ///   - klineModes: -
    ///   - periods: 计算的周期数组
    public static func calculateRSI(klineModes: [KLineModel], periods:[Int]) {
        let closes = klineModes.map { $0.close }//.reversed() // 时间倒序
        var resultList:[String:[String]] = [:]
        for period in periods {
            let current = calculateRSI(prices: closes, period: period)
            resultList["\(period)"] = current.map({ value in
                if let value = value{
                    return "\(value)"
                }else{
                    return ""
                }
            })
        }
        
        asyncOnMain {
            var index = 0
            klineModes.forEach { model in
                var dic:[String:String] = [:]
                for period in periods {
                    if let array = resultList["\(period)"]{
                        dic["\(period)"] = array[safe: index]
                    }
                }
                model.rsiDictionary = dic
                index += 1
            }
        }
    }
    /// 计算单组RSI
    /// - Parameters:
    ///   - prices: 价格数组
    ///   - period: 计算周期
    /// - Returns: RSI值数组，对应位置无法计算时为nil
    public static func calculateRSI(prices: [Double], period: Int) -> [Double?] {
        // 确保数据量足够计算RSI
        guard prices.count >= period + 1, period > 0 else {
            return Array(repeating: nil, count: prices.count)
        }

        var rsiValues = Array<Double?>(repeating: nil, count: prices.count)
        
        // 预先计算所有价格变化
        let priceChanges = zip(prices.dropFirst(), prices).map { $0 - $1 }
        
        // 使用滑动窗口计算初始RSI
        let initialChanges = Array(priceChanges.prefix(period))
        let initialGains = initialChanges.map { max($0, 0) }
        let initialLosses = initialChanges.map { max(-$0, 0) }
        
        var avgGain = initialGains.reduce(0, +) / Double(period)
        var avgLoss = initialLosses.reduce(0, +) / Double(period)
        
        // 计算第一个RSI值
        let calculateRSIValue = { (gain: Double, loss: Double) -> Double in
            if loss.isZero {
                return 100.0
            } else {
                let rs = gain / loss
                return 100.0 - (100.0 / (1.0 + rs))
            }
        }
        
        rsiValues[period] = calculateRSIValue(avgGain, avgLoss)
        
        // 使用Wilder平滑方法计算后续RSI值
        let smoothingFactor = Double(period - 1) / Double(period)
        
        for i in period..<prices.count - 1 {
            let currentChange = priceChanges[i]
            let currentGain = max(currentChange, 0)
            let currentLoss = max(-currentChange, 0)
            
            // 应用平滑计算
            avgGain = avgGain * smoothingFactor + currentGain / Double(period)
            avgLoss = avgLoss * smoothingFactor + currentLoss / Double(period)
            
            rsiValues[i + 1] = calculateRSIValue(avgGain, avgLoss)
        }
        
        return rsiValues
    }
    
    
    /*================================================================*/
    
    /// 计算多周期WR
    /// - Parameters:
    ///   - klineModes: -
    ///   - periods: 周期数组
    public static func calculateWROptimized(klineModes: [KLineModel], periods: [Int]) {
        
        var resultList:[String:[String]] = [:]
        for period in periods {
            let current = calculateWROptimized(klineModes: klineModes, period: period)
            resultList["\(period)"] = current.map({ value in
                if let value = value{
                    return "\(value)"
                }else{
                    return ""
                }
            })
        }
        
        asyncOnMain {
            var index = 0
            klineModes.forEach { model in
                var dic:[String:String] = [:]
                for period in periods {
                    if let array = resultList["\(period)"]{
                        dic["\(period)"] = array[safe: index]
                    }
                }
                model.wrDictionary = dic
                index += 1
            }
        }
    }
    
    /// 计算单周期WR
    /// - Parameters:
    ///   - klineModes: -
    ///   - period: 周期
    /// - Returns: -
    public static func calculateWROptimized(klineModes: [KLineModel], period: Int) -> [Double?] {
        guard klineModes.count >= period else { return [] }
        
        var wrValues: [Double?] = []
        var windowHighs: [Double] = []
        var windowLows: [Double] = []
        
        // 初始窗口
        let initialWindow = klineModes[0..<period]
        windowHighs = initialWindow.map { $0.high }
        windowLows = initialWindow.map { $0.low }
        
        for _ in 1..<period{
            wrValues.append(nil)
        }
        // 计算第一个WR值
        if let maxHigh = windowHighs.max(),
           let minLow = windowLows.min() {
            let wr = calculateWRSingle(high: maxHigh, low: minLow, close: klineModes[period-1].close)
            wrValues.append(wr)
        }
        
        // 滑动窗口计算
        for i in period..<klineModes.count {
            // 移除最旧的数据
            windowHighs.removeFirst()
            windowLows.removeFirst()
            
            // 添加最新数据
            windowHighs.append(klineModes[i].high)
            windowLows.append(klineModes[i].low)
            
            // 计算WR
            if let maxHigh = windowHighs.max(),
               let minLow = windowLows.min() {
                let wr = calculateWRSingle(high: maxHigh, low: minLow, close: klineModes[i].close)
                wrValues.append(wr)
            }
        }
        
        return wrValues
    }

    private static func calculateWRSingle(high: Double, low: Double, close: Double) -> Double {
        let denominator = high - low
        return denominator != 0 ? ((high - close) / denominator) * 100 : 0
    }
    
    /*================================================================*/
    
    /// 计算OBV 只能计算单个周期的OBV
    /// - Parameters:
    ///   - klineModes: -
    ///   - periods: -
    public static func calculationModelOBV(klineModes: [KLineModel], periods: Int){
        
       let list = klineModes.map({KLineStockData(volume: $0.volume, closePrice: $0.close)})
        
     
        let result = calculateOBV(data: list, maPeriod: periods)
        
        let startIndex1 = klineModes.count - result.obv.count
        let startIndex2 = klineModes.count - result.ma.count
        
        asyncOnMain {
            for i in 0..<klineModes.count {
                let model = klineModes[i]
                var obvDictionary = model.obvDictionary
                if i - startIndex1 < result.obv.count, i - startIndex1 >= 0 {
                    obvDictionary["0"] = result.obv[i - startIndex1]
                }
                
                if i - startIndex2 < result.ma.count, i - startIndex2 >= 0 {
                    obvDictionary["1"] = result.ma[i - startIndex2]
                }
                model.obvDictionary = obvDictionary
            }
        }
        
    }
    
    public static func calculateOBV(data: [KLineStockData], maPeriod: Int) -> (obv: [Double], ma: [Double]) {
        var obvValues: [Double] = []
        var obv: Double = 0
        var maValues: [Double] = []
        var obvSum: Double = 0
        
        for i in 0..<data.count {
            let currentData = data[i]
            let previousData = i > 0 ? data[i - 1] : currentData
            
            let weight = currentData.closePrice == previousData.closePrice ? 0 :
                currentData.closePrice > previousData.closePrice ? currentData.volume : -currentData.volume
            
            obv += weight
            obvValues.append(obv)
            obvSum += obv
            
            if i >= maPeriod {
                if maPeriod > 0 {
                    let maValue = obvSum / Double(maPeriod)
                    maValues.append(maValue)
                } else {
                    maValues.append(obvSum)
                }
                obvSum -= obvValues[i - maPeriod]
            }
        }
        
        return (obvValues, maValues)
    }
    
    /*================================================================*/
    
    
    /// 计算ROC
    /// - Parameters:
    ///   - klineModes: -
    ///   - period: -
    ///   - maPeriod: -
    
    public static func calculationModelROC(klineModes: [KLineModel],period:Int,maPeriod:Int) {

        let prices = klineModes.map({$0.close})
        
        let result = calculateROC(closePrices: prices, period: period, maPeriod: maPeriod)

        let startIndex1 = klineModes.count - result.roc.count
        let startIndex2 = klineModes.count - result.ma.count
        
        asyncOnMain {
            for i in 0..<klineModes.count {
                let model = klineModes[i]
                var rocDictionary = model.rocDictionary
                if i - startIndex1 < result.roc.count, i - startIndex1 >= 0 {
                    rocDictionary["0"] = result.roc[i - startIndex1]
                }
                if i - startIndex2 < result.ma.count, i - startIndex2 >= 0 {
                    rocDictionary["1"] = result.ma[i - startIndex2]
                }
                model.rocDictionary = rocDictionary
            }
        }
    }
    
    
    public static func calculateROC(closePrices: [Double], period: Int, maPeriod: Int) -> (roc: [Double], ma: [Double]) {
        guard period > 0, period < closePrices.count, maPeriod > 0 else { return ([], []) }
        var rocValues = [Double]()
        var rocMAValues = [Double]()
        
        var rocSum: Double = 0
        for i in period..<closePrices.count {
            let roc = ((closePrices[i] - closePrices[i - period]) / closePrices[i - period]) * 100.0
            rocValues.append(roc)
            rocSum += roc
            if i >= period + maPeriod {
                let maValue = rocSum / Double(maPeriod)
                rocMAValues.append(maValue)
                rocSum -= rocValues[i - maPeriod - period]
            }
        }
        
        return (rocValues, rocMAValues)
    }
    
    
    /*================================================================*/
    /// 计算CCI
    public static func calculationModelCCI(klineModes: [KLineModel],period:Int) {
        guard KLineConfig.cciArray.isEmpty == false else { return }
        
        let prices = klineModes.map({$0.close})
        let highs = klineModes.map({$0.high})
        let lows = klineModes.map({$0.low})
        
        let result = calculateCCI(prices, highs, lows, period)
        
        for i in 0..<klineModes.count {
            let model = klineModes[i]
            if i < result.count, let v = result[i] {
                model.cci = v
            }
        }
    }
    
    
    public static func calculateCCI(_ close: [Double], _ high: [Double], _ low: [Double], _ period: Int) -> [Double?] {
        guard period > 0, period < close.count else { return [] }
        let length = close.count
        var typicalPrice = [Double](repeating: 0.0, count: length)
        var smaTypicalPrice = [Double](repeating: 0.0, count: length)
        var meanDeviation = [Double](repeating: 0.0, count: length)
        var cci = [Double?](repeating: nil, count: length)
        
        for i in 0..<length {
            typicalPrice[i] = (high[i] + low[i] + close[i]) / 3.0
        }

        for i in (period - 1)..<length {
            var sumTypicalPrice = 0.0

            for j in (i - period + 1)...i {
                sumTypicalPrice += typicalPrice[j]
            }

            smaTypicalPrice[i] = sumTypicalPrice / Double(period)
        }

        for i in (period - 1)..<length {
            var sumMeanDeviation = 0.0

            for j in (i - period + 1)...i {
                sumMeanDeviation += abs(typicalPrice[j] - smaTypicalPrice[i])
            }

            meanDeviation[i] = sumMeanDeviation / Double(period)
        }

        for i in (period - 1)..<length {
            // swiftlint:disable:next all
            if meanDeviation[i] != 0 {
                cci[i] = (typicalPrice[i] - smaTypicalPrice[i]) / (0.015 * meanDeviation[i])
            }
        }

        return cci
    }
    
    
    /*================================================================*/
    /// 计算StochRSI
    
    public static func calculationStochRSI(klineModes: [KLineModel],period:Int,rsiPeriod:Int,kPeriod:Int,dPeriod:Int) {
        guard KLineConfig.stochRSIArray.count > 3 else { return }
        
        
       
        let prices = klineModes.map({$0.close})
        
        
        let rsis = calculationStochRSI(prices: prices, period: rsiPeriod)
        
        if rsis.isEmpty == false {
            let k = calculateStochasticKLine(rsiValues: rsis, stochPeriod: period)
            
            let d1 = calculateStochasticDLine(stochKLine: k, kSmooth: kPeriod)
            let d2 = calculateSmoothedStochDLine(stochDLine: d1, dSmooth: dPeriod)
            
            let startIndex1 = klineModes.count - d1.count
            let startIndex2 = klineModes.count - d2.count
            
            asyncOnMain {
                for i in 0..<klineModes.count {
                    let model = klineModes[i]
                    var stochRSIDictionary = model.stochRSIDictionary
                    if i - startIndex1 < d1.count, i - startIndex1 >= 0 {
                        stochRSIDictionary["0"] = d1[i - startIndex1]
                    }
                    if i - startIndex2 < d2.count, i - startIndex2 >= 0 {
                        stochRSIDictionary["1"] = d2[i - startIndex2]
                    }
                    model.stochRSIDictionary = stochRSIDictionary
                }
            }
        }
    }
    
    public static func calculationStochRSI(prices: [Double], period: Int) -> [Double] {
        var rsiValues: [Double] = []
        
        if period > 0, prices.isEmpty == true, prices.count <= period {
            return rsiValues
        }
        
        let priceChanges = calculatePriceChanges(prices: prices)
        if priceChanges.isEmpty == true { return [] }
        
        var avgGain = calculateAverageGain(priceChanges: priceChanges, period: period)
        var avgLoss = calculateAverageLoss(priceChanges: priceChanges, period: period)
        
        var rs = (avgLoss == 0 ? 0 : avgGain / avgLoss)
        var rsi = 100 - (100 / (1 + rs))
        rsiValues.append(rsi)
        
        if period > priceChanges.count { return [] }
        
        for i in period..<priceChanges.count {
            if let priceChange = priceChanges[safe: i] {
                let gain = max(priceChange, 0)
                let loss = abs(min(priceChange, 0))
                
                avgGain = ((avgGain * Double(period - 1)) + gain) / Double(period)
                avgLoss = ((avgLoss * Double(period - 1)) + loss) / Double(period)
                
                rs = avgGain / avgLoss
                rsi = 100 - (100 / (1 + rs))
                rsiValues.append(rsi)
            } else {
                rsiValues.append(0)
            }
        }
        
        return rsiValues
    }
    
    private static func calculatePriceChanges(prices: [Double]) -> [Double] {
        guard prices.count > 1 else { return [] }
        var priceChanges: [Double] = []
        
        for i in 1..<prices.count {
            let priceChange = prices[i] - prices[i - 1]
            priceChanges.append(priceChange)
        }
        
        return priceChanges
    }
    
    private static func calculateAverageGain(priceChanges: [Double], period: Int) -> Double {
        var sum: Double = 0
        
        for i in 0..<period {
            if i >= priceChanges.count { break }
            if let priceChange = priceChanges[safe: i], priceChange > 0 {
                sum += priceChange
            }
        }
        
        return sum / Double(period)
    }
    
    private static func calculateAverageLoss(priceChanges: [Double], period: Int) -> Double {
        var sum: Double = 0
        
        for i in 0..<period {
            if i >= priceChanges.count { break }
            if let priceChange = priceChanges[safe: i], priceChange < 0 {
                sum += abs(priceChange)
            }
        }
        
        return sum / Double(period)
    }
    
    
    // 计算 Stochastic Oscillator 的 K 线
    public static func calculateStochasticKLine(rsiValues: [Double], stochPeriod: Int) -> [Double] {
        var stochKLine: [Double] = []
        
        if rsiValues.count >= stochPeriod, stochPeriod > 0 {
            for i in (stochPeriod - 1)..<rsiValues.count {
                let subList = Array(rsiValues[(i - stochPeriod + 1)...i])
                let minRSI = getMinValue(list: subList)
                let maxRSI = getMaxValue(list: subList)
                let stochK = (rsiValues[i] - minRSI) / (maxRSI - minRSI) * 100
                stochKLine.append(stochK)
            }
        }
        
        return stochKLine
    }
    
    private static func getMinValue(list: [Double]) -> Double {
        var min = Double.greatestFiniteMagnitude
        for value in list {
            // swiftlint:disable:next all
            if value < min {
                min = value
            }
        }
        return min
    }
    
    private static func getMaxValue(list: [Double]) -> Double {
        var max = Double.leastNormalMagnitude
        for value in list {
            // swiftlint:disable:next all
            if value > max {
                max = value
            }
        }
        return max
    }
    
    // 计算 Stochastic Oscillator 的 D 线
    public static func calculateStochasticDLine(stochKLine: [Double], kSmooth: Int) -> [Double] {
        var stochDLine: [Double] = []
        
        if stochKLine.count >= kSmooth, kSmooth > 0 {
            for i in (kSmooth - 1)..<stochKLine.count {
                let subList = Array(stochKLine[(i - kSmooth + 1)...i])
                let stochD = getAverage(list: subList)
                stochDLine.append(stochD)
            }
        }
        
        return stochDLine
    }
    
    private static func getAverage(list: [Double]) -> Double {
        guard !list.isEmpty else { return 0 }
        let sum = list.reduce(0.0, +)
        return sum / Double(list.count)
    }
    
    // 平滑 Stochastic Oscillator 的 D 线
    public  static func calculateSmoothedStochDLine(stochDLine: [Double], dSmooth: Int) -> [Double] {
        var smoothedStochDLine: [Double] = []
        
        if stochDLine.count >= dSmooth, dSmooth > 0 {
            for i in (dSmooth - 1)..<stochDLine.count {
                let subList = Array(stochDLine[(i - dSmooth + 1)...i])
                let smoothedStochD = getAverage(list: subList)
                smoothedStochDLine.append(smoothedStochD)
            }
        }
        
        return smoothedStochDLine
    }
    
    
    /*================================================================*/
    
    /// 计算TRIX
    
    public static func calculationTRIX(klineModes: [KLineModel],period:Int) {
        let prices = klineModes.map({$0.close})
        let result = calculateTRIX(forClosePrices: prices, n: period)
        for i in 0..<klineModes.count {
            let model = klineModes[i]
            if i < result.count {
                model.trix = result[i]
            }
        }
    }
    
    public static func calculateTRIX(forClosePrices closePrices: [Double], n: Int) -> [Double?] {
        let ema = calculateEMA(data: closePrices, period: n)
        let dema = calculateEMA(data: ema, period: n)
        let tema = calculateEMA(data: dema, period: n)
        
        var trixValues = [Double?](repeating: nil, count: closePrices.count - tema.count + 1)
        for i in 0..<tema.count {
            guard i > 0 else { continue }
            let value = tema[i]
            let previousTEMA = tema[i - 1]
            let trix = ((value - previousTEMA) / previousTEMA) * 100.0
            trixValues.append(trix)
        }

        return trixValues
    }
    
    public static func calculateEMA(data: [Double], period: Int) -> [Double] {
        guard period > 1, data.count >= period else {
            return []
        }

        let multiplier = 2.0 / Double(period + 1)
        var emaValues: [Double] = []

        // Calculate the initial SMA (Simple Moving Average) for the first period
        let sma = data.prefix(period).reduce(0, +) / Double(period)
        emaValues.append(sma)

        // Calculate EMA for the remaining data points
        for i in period..<data.count {
            // swiftlint:disable:next all
            let ema = (data[i] - emaValues.last!) * multiplier + emaValues.last!
            emaValues.append(ema)
        }

        return emaValues
    }
    /*================================================================*/
    
    /// 计算DMI
    
    public static func calculationDMI(klineModes: [KLineModel],di:Int,adx:Int) {
        
        
        guard di > 1 else { return }
        var priceData: [(high: Double, low: Double, close: Double)] = []
        var high: [Double] = []
        var low: [Double] = []
        for i in 0..<klineModes.count {
            let h = klineModes[i].high
            let c = klineModes[i].close
            let l = klineModes[i].low
            high.append(h)
            low.append(l)
            priceData.append((h, l, c))
        }

        let atrArray = calculateATR(data: priceData, period: di)
        let dmArray = calculateSmoothedDirectionalMovement(highs: high, lows: low, period: di)

        let atrResult = calculateMovingAverage(inputArray: atrArray, windowSize: di)

        let v1 = calculateMovingAverage(inputArray: dmArray.smootherDmPlus, windowSize: di)
        let v2 = calculateMovingAverage(inputArray: dmArray.smootherDmMinus, windowSize: di)

        var dxArray: [Double] = [0]
        var pdi: [Double] = []
        var mdi: [Double] = []
        for i in 0..<atrResult.count {
            let dm1 = (v1[i] * 100) / atrResult[i]
            let dm2 = (v2[i] * 100) / atrResult[i]
            let dx = fabs(dm1 - dm2) / (dm1 + dm2) * 100
            dxArray.append(dx)
            pdi.append(dm1)
            mdi.append(dm2)
        }
        let adx = calculateMovingAverage(inputArray: dxArray, windowSize: adx)
        let adxr = calculateADXR(adxValues: adx, period: di)
        
        asyncOnMain {
            let pdiIndex = klineModes.count - pdi.count
            let mdiIndex = klineModes.count - mdi.count
            let adxIndex = klineModes.count - adx.count
            let adxrIndex = klineModes.count - adxr.count
            for i in 0..<klineModes.count {
                var dmiDictionary = [String: Double]()
                let model = klineModes[i]
                // pdi
                if i - pdiIndex < pdi.count, i - pdiIndex >= 0 {
                    let index = i - pdiIndex
                    dmiDictionary["0"] = pdi[index]
                }
                // mdi
                if i - mdiIndex < mdi.count, i - mdiIndex >= 0 {
                    let index = i - mdiIndex
                    dmiDictionary["1"] = mdi[index]
                }
                // adr
                if i - adxIndex < adx.count, i - adxIndex >= 0 {
                    let index = i - adxIndex
                    dmiDictionary["2"] = adx[index]
                }
                // adrx
                if i - adxrIndex < pdi.count, i - adxrIndex >= 0 {
                    let index = i - adxrIndex
                    dmiDictionary["3"] = adxr[index]
                }
                model.dmiDictionary = dmiDictionary
            }
        }
    }
    
    
    public static func calculateATR(data: [(high: Double, low: Double, close: Double)], period: Int) -> [Double] {
        var atrValues: [Double] = []

        guard period > 1 else {
            return atrValues
        }
        if data.count < period {
            return atrValues
        }
        
        let periodCount = min(period, data.count - 1)
        guard periodCount >= 1 else { return atrValues }
        
        atrValues.append(0)
        for i in 1...periodCount {
            // swiftlint:disable:next all
            let tr = max(data[i].high - data[i].low, abs(data[i].high - data[i - 1].close), abs(data[i].low - data[i - 1].close))
            atrValues.append(tr)
        }

        guard data.count > period + 1 else {
            return atrValues
        }
        
        for i in (period + 1)..<data.count {
            // swiftlint:disable:next all
            let tr = max(data[i].high - data[i].low, abs(data[i].high - data[i - 1].close), abs(data[i].low - data[i - 1].close))
            atrValues.append(tr)
        }

        return atrValues
    }
    
    public static func calculateSmoothedDirectionalMovement(highs: [Double], lows: [Double], period: Int) -> (smootherDmPlus: [Double], smootherDmMinus: [Double]) {
        guard highs.count == lows.count && period > 0 && period <= highs.count else {
            return ([], [])
        }
        
        let periodCount = min(period, highs.count - 1)
        guard periodCount >= 1 else { return ([], []) }
        var smootherDmPlus: [Double] = []
        var smootherDmMinus: [Double] = []
        
        smootherDmPlus.append(0)
        smootherDmMinus.append(0)
        for i in 1...periodCount {
            // swiftlint:disable:next all
            let (dmPlus, dmMinus) = calculateDirectionalMovement(high: highs[i], low: lows[i], prevHigh: highs[i - 1], prevLow: lows[i - 1])
     
            smootherDmPlus.append(dmPlus)
            smootherDmMinus.append(dmMinus)
        }
        
        guard highs.count > period + 1 else {
            return (smootherDmPlus, smootherDmMinus)
        }
        
        for i in (period + 1)..<highs.count {
            let (dmPlus, dmMinus) = calculateDirectionalMovement(high: highs[i],
                                                                 low: lows[i],
                                                                 prevHigh: highs[i - 1],
                                                                 prevLow: lows[i - 1])
            smootherDmPlus.append(dmPlus)
            smootherDmMinus.append(dmMinus)
        }
        
        return (smootherDmPlus, smootherDmMinus)
    }
    
    public static func calculateMovingAverage(inputArray: [Double], windowSize: Int) -> [Double] {
        guard windowSize > 0 && windowSize <= inputArray.count else {
            return []
        }
        
        var movingAverageArray: [Double] = []
        let window: [Double] = Array(inputArray.prefix(windowSize + 1))
        
        let initialSum: Double = window.reduce(0, +) / Double(windowSize)
        movingAverageArray.append(initialSum)
        
        guard inputArray.count > windowSize + 1 else {
            return movingAverageArray
        }
        
        for index in (windowSize + 1)..<inputArray.count {
            // swiftlint:disable:next all
            let updatedSum = (movingAverageArray.last! * Double(windowSize - 1) + inputArray[index]) / Double(windowSize)
            movingAverageArray.append(updatedSum)
        }
        
        return movingAverageArray
    }
    
    public static func calculateADXR(adxValues: [Double], period: Int) -> [Double] {
        if period - 1 >= adxValues.count { return [] }
        var adxrValues: [Double] = []

        for i in (period - 1)..<adxValues.count {
            let adxr = (adxValues[i] + adxValues[i - period + 1]) / 2.0
            adxrValues.append(adxr)
        }
        return adxrValues
    }
    
    public static func calculateDirectionalMovement(high: Double, low: Double, prevHigh: Double, prevLow: Double) -> (dmPlus: Double, dmMinus: Double) {
        let upMove = high - prevHigh
        let downMove = prevLow - low
        return ((upMove > downMove && upMove > 0) ? upMove : 0, (downMove > upMove && downMove > 0) ? downMove : 0)
    }
}


public struct KLineStockData {
    public let volume: Double
    public let closePrice: Double
}
