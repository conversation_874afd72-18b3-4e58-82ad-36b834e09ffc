//
//  File.swift
//  KLineLib
//
//  Created by <PERSON> on 2025/6/5.
//

import Foundation
import UIKit
public  protocol KlineAnimationProtocol:NSObjectProtocol{
  
  /// 记录上次动态计算结果
  var lastTimeClose:Double?{get set}
  
  /// 记录上次计算的位置
  var lastPositionModel:KLinePositionModel?{get set}
  
  /// 屏幕刷新率同步
  var displayLink: CADisplayLink?{get set}
  
  var needDrawKLineModels:[KLineModelStruct]{get set}
//  func startAnimation()->Void
//  func stopAnimation()->Void
//  func updateAnimation(complet:(_ maxValue:Double,_ minValue:Double,_ price:String)->Void)
}
public extension KlineAnimationProtocol where Self:UIView{
  /*//MARK: Michael:Copy 下方方法到需要动画的地方
  func startAnimation() {
    //MARK: Michael: 防止最后一条闪屏 将最后一次的位置模型赋值给空的那条
    if let index = self.needDrawKLineModels.lastIndex(where: {$0.positionModel == nil}){
      self.needDrawKLineModels[index].positionModel = lastPositionModel
    }
    guard displayLink == nil else {return}
    displayLink = CADisplayLink(target: self, selector: #selector(<#@objc 方法内调用updateAnimation()#>))
    displayLink?.add(to: .main, forMode: .common)
    
  }
  */
  func stopAnimation()->Void{
    displayLink?.invalidate()
    displayLink = nil
  }
  func updateAnimation(complet:(_ maxValue:Double,_ minValue:Double,_ price:String)->Void,animationComplet:()->Void) {
    var animationFinished = true
    
    for index in 0..<self.needDrawKLineModels.count {
      if self.needDrawKLineModels[index].needAnimation,self.needDrawKLineModels[index].animationProgress < 1{
        let targetClose = self.needDrawKLineModels[index].targetClose
        if lastTimeClose == targetClose{
          animationComplet()
        }
        // 更新动画进度
        self.needDrawKLineModels[index].animationProgress += 0.01
        
        if self.needDrawKLineModels[index].animationProgress >= 1 {
          // 动画完成
          self.needDrawKLineModels[index].close = targetClose
          self.needDrawKLineModels[index].needAnimation = false
          //lastTimeClose = nil
          animationComplet()
        } else {
          // 计算中间值
          let startClose = lastTimeClose ?? self.needDrawKLineModels[index].open
          let progress = self.needDrawKLineModels[index].animationProgress
          lastTimeClose = startClose + (targetClose - startClose) * Double(progress)
          self.needDrawKLineModels[index].close = lastTimeClose ?? self.needDrawKLineModels[index].open
          
          let (models, maxValue, minValue, _) = convertToKLinePositionModels(containerSize: self.bounds.size, kLineModels: self.needDrawKLineModels)
          if let last = models.last,let positionModel = last.positionModel{
            self.lastPositionModel = positionModel
            self.needDrawKLineModels[index].positionModel = positionModel
          }
          
          complet(maxValue,minValue,"\(lastTimeClose!)")
          animationFinished = false
          
        }
      }
    }
    
    setNeedsDisplay()
    guard animationFinished else{return}
    self.stopAnimation()
  }
  
}

public extension KlineAnimationProtocol{
  /// 将model转化为Position模型
   func convertToKLinePositionModels(containerSize:CGSize,
                                               kLineModels: [KLineModelStruct]) -> (kLineModels: [KLineModelStruct], maxValue: Double, minValue: Double, scale: Int) {
      guard kLineModels.first != nil else { return (kLineModels, 0.0, 0.0, 0) }
    var kLineModels = kLineModels
      let isPro = KLineConfig.kLineType == .professional
      var minValue = Double.greatestFiniteMagnitude
      var maxValue: Double = 0.0
    var  allValue:[Double] = []
    let high = kLineModels.map({$0.high})
    let low = kLineModels.map({$0.low})
    let open = kLineModels.map({$0.open})
    let close = kLineModels.map({$0.close})
    let targetClose = kLineModels.map({$0.targetClose})
    
    //MARK: Michael: 应该不需要再管order price 暂时这里先留着 需要的话直接打开即可
    //let order = kLineModels.map({$0.orderModels}).compactMap({$0}).flatMap({$0}).map({$0.price})
    //allValue.append(contentsOf: order)
    
    allValue.append(contentsOf: targetClose)
    allValue.append(contentsOf: close)
    
    if isPro {
      allValue.append(contentsOf: high)
      allValue.append(contentsOf: low)
      allValue.append(contentsOf: open)
      
      var mainValueList:[Double] = []
      KLineConfig.mainViewType.forEach { type in
        switch type {
        case .ma:
          let list =  kLineModels.map({$0.maDictionary}).map({$0.values}).flatMap({$0}).compactMap({$0.asDouble()})
          mainValueList.append(contentsOf: list)
        case .ema:
          let list =  kLineModels.map({$0.emaDictionary}).map({$0.values}).flatMap({$0}).compactMap({$0.asDouble()})
          mainValueList.append(contentsOf: list)
        case .wma:
          let list =  kLineModels.map({$0.wmaDictionary}).map({$0.values}).flatMap({$0}).compactMap({$0.asDouble()})
          mainValueList.append(contentsOf: list)
        case .boll:
          let mb = kLineModels.map({$0.mb}).compactMap({$0}).map({$0.asDouble()}).compactMap({$0})
          let up = kLineModels.map({$0.up}).compactMap({$0}).map({$0.asDouble()}).compactMap({$0})
          let dn = kLineModels.map({$0.dn}).compactMap({$0}).map({$0.asDouble()}).compactMap({$0})
          
          mainValueList.append(contentsOf: mb)
          mainValueList.append(contentsOf: up)
          mainValueList.append(contentsOf: dn)
        case .close:
          break
        case .sar:
          let list =  kLineModels.map({$0.sar}).compactMap({$0}).compactMap({$0.asDouble()})
          mainValueList.append(contentsOf: list)
        case .vwap:
          let list =  kLineModels.map({$0.averagePrice}).compactMap({$0}).filter({$0 != 0})
          mainValueList.append(contentsOf: list)
        }
      }
      
      allValue.append(contentsOf: mainValueList)
    }
    let resultAllValues = allValue.compactMap({$0})
    if let max = resultAllValues.max(),let min = resultAllValues.min(){
      maxValue = max
      minValue = min
    }
      let minY = KLineConfig.kLineMainViewMinY
      let maxY = containerSize.height - KLineConfig.kLineMainViewMaxY
      var unitValue = 1.0
      
      
      if maxY != minY && maxValue != minValue && maxY != minY {
          unitValue = (maxValue - minValue) / Double((maxY - minY))
      }
      let lineGap = KLineConfig.kLineGap
      let lineWidth = KLineConfig.kLineWidth
      //左侧需空出的空间
      var tempIndex = 0
      //MARK: Michael: 当当前为五日分时时 计算需要空出的左侧空间
      if KLineConfig.type == .fiveDayLine ,let estimate = KLineConfig.expectedTotalDataCount{
          //一天的数据条数
          let oneDayCount = estimate / 5
          //当前数据大概占几天
          let currentDays = ceil(Double(kLineModels.count) / Double(oneDayCount))
          //左侧需空出的空间
          tempIndex = (5 - Int(currentDays)) * oneDayCount
      }
      
      var idx = 0
      var scale = 14
      
      let templineWidth = calculateLineWidth(oldlineWidth: lineWidth, contentWidth: containerSize.width, count: kLineModels.count)
      
    for i in 0..<kLineModels.count {
      
          var xPosition:CGFloat = .zero
          if KLineConfig.displayAllData{
              if let expectedCount = KLineConfig.expectedTotalDataCount {
                  if expectedCount < kLineModels.count{
                      xPosition = CGFloat(idx + tempIndex) *  containerSize.width / CGFloat(kLineModels.count)
                  }else{
                      xPosition = CGFloat(idx + tempIndex) *  containerSize.width / CGFloat(expectedCount)
                  }
              }else{
                  xPosition = CGFloat(idx + tempIndex) *  containerSize.width / CGFloat(kLineModels.count)
              }
              xPosition += templineWidth
          }else{
              xPosition = KLineViewModel.startXPosition() + CGFloat(idx + tempIndex) * (lineGap + lineWidth)
          }
          var openPoint = CGPoint(x: xPosition,
                                  y: (maxY - CGFloat((kLineModels[i].open  - minValue) / unitValue)))
          var closePointY = (maxY - CGFloat((kLineModels[i].close  - minValue) / unitValue))
          scale = kLineModels[i].priceScale
          if scale < 0 { scale = 2 }
          
          // 防止出现柱子特别细，看不清问题
          if abs(closePointY - openPoint.y) < KLineConfig.kLineMinWidth {
              if openPoint.y > closePointY {
                  openPoint.y = closePointY + KLineConfig.kLineMinWidth
              } else if openPoint.y < closePointY {
                  closePointY = openPoint.y + KLineConfig.kLineMinWidth
              } else {
                  if idx > 0 {
                      let preKLineModel = kLineModels[idx - 1]
                      
                      if kLineModels[i].open  > preKLineModel.close  {
                          openPoint.y = closePointY + KLineConfig.kLineMinWidth
                      } else {
                          closePointY = openPoint.y + KLineConfig.kLineMinWidth
                      }
                  } else if idx + 1 < kLineModels.count {
                      // idx==0即第一个时
                      let subKLineModel = kLineModels[idx + 1]
                      
                      if kLineModels[i].close  < subKLineModel.open  {
                          openPoint.y = closePointY + KLineConfig.kLineMinWidth
                      } else {
                          closePointY = openPoint.y + KLineConfig.kLineMinWidth
                      }
                  }
              }
          }
          let closePoint = CGPoint(x: xPosition, y: closePointY)
          let highPoint = CGPoint(x: xPosition,
                                  y: (maxY - CGFloat((kLineModels[i].high  - minValue) / unitValue)))
          let lowPoint = CGPoint(x: xPosition,
                                 y: (maxY - CGFloat((kLineModels[i].low  - minValue) / unitValue)))
          
          let kLinePositionModel = KLinePositionModel()
          kLinePositionModel.openPoint = openPoint
          kLinePositionModel.closePoint = closePoint
          kLinePositionModel.highPoint = highPoint
          kLinePositionModel.lowPoint = lowPoint
          kLinePositionModel.color = kLineModels[i].open  > kLineModels[i].targetClose  ?
          KLineConfig.themeManager.candleRiseColor() : KLineConfig.themeManager.candleDeclineColor()
          
          for type in KLineConfig.mainViewType {
              if type == .ma {
                kLineModels[i].maDictionary.forEach { (key: String, value: String) in
                      if !value.isEmpty {
                          let maPoint = CGPoint(x: xPosition,
                                                y: (maxY - CGFloat((value.doubleValue()  - minValue) / unitValue)))
                          kLinePositionModel.maPoints[key] = maPoint
                      }
                  }
              } else if type == .ema {
                kLineModels[i].emaDictionary.forEach { (key: String, value: String) in
                      if !value.isEmpty {
                          let emaPoint = CGPoint(x: xPosition,
                                                 y: (maxY - CGFloat((value.doubleValue()  - minValue) / unitValue)))
                          kLinePositionModel.emaPoints[key] = emaPoint
                      }
                  }
              } else if type == .wma {
                kLineModels[i].wmaDictionary.forEach { (key: String, value: String) in
                      if !value.isEmpty {
                          let emaPoint = CGPoint(x: xPosition,
                                                 y: (maxY - CGFloat((value.doubleValue()  - minValue) / unitValue)))
                          kLinePositionModel.wmaPoints[key] = emaPoint
                      }
                  }
              } else if type == .boll {
                  if let mb = kLineModels[i].mb, !mb.isEmpty {
                      let point = CGPoint(x: xPosition,
                                          y: (maxY - CGFloat((mb.doubleValue()  - minValue) / unitValue)))
                      kLinePositionModel.bollPoints["BOLL_MB"] = point
                  }
                  
                  if let up = kLineModels[i].up, !up.isEmpty {
                      let point = CGPoint(x: xPosition,
                                          y: (maxY - CGFloat((up.doubleValue()  - minValue) / unitValue)))
                      kLinePositionModel.bollPoints["BOLL_UP"] = point
                  }
                  
                  if let dn = kLineModels[i].dn, !dn.isEmpty {
                      let point = CGPoint(x: xPosition,
                                          y: (maxY - CGFloat((dn.doubleValue()  - minValue) / unitValue)))
                      kLinePositionModel.bollPoints["BOLL_DN"] = point
                  }
              } else if type == .sar, let sar = kLineModels[i].sar {
                  let point = CGPoint(x: xPosition,
                                      y: (maxY - CGFloat((sar.doubleValue() - minValue) / unitValue)))
                  kLinePositionModel.sarPoints = point
                  kLinePositionModel.sarUp = (sar.doubleValue() >= kLineModels[i].close )
              }
          }
      kLineModels[i].positionModel = kLinePositionModel
          if let orderModels = kLineModels[i].orderModels{
              orderModels.forEach { order in
                  let y = (maxY - CGFloat((order.price  - minValue) / unitValue))
                  order.point = CGPoint(x: xPosition, y: y)
              }
              
          }
          idx += 1
      }
      
      if minValue == Double.greatestFiniteMagnitude { return (kLineModels, 0.0, 0.0, 0) }
      return (kLineModels, maxValue, minValue, scale)
  }
}
