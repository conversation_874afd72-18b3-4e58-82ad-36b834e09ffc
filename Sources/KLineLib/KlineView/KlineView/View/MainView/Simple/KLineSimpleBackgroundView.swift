//
//  FILE.swift
//  Ourbit
//
//  Created by pippen on 2024.
//

import UIKit

open class KLineSimpleBackgroundView: UIView {
    
    public lazy var simpleView: KLineSimpleView = {
        let v = KLineSimpleView()
        return v
    }()
    
    public lazy var logo: UIImageView = {
        let img = UIImageView()
        img.image = "kline_logo_new".themeImg(KlineLibThemeManager.currentTheme)
        return img
    }()
    
    public lazy var verticalLine: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor.clear
        return v
    }()
    
    public override init(frame: CGRect) {
        super.init(frame: frame)
        
        self.setupUI()
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    /// 设置视图
    public func setupUI() {
        self.addSubview(self.simpleView)
        self.simpleView.snp.makeConstraints { make in
            make.leading.trailing.top.equalTo(0)
            make.bottom.equalTo(-35).priority(999)
        }
        
        self.addSubview(self.logo)
        self.logo.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        self.addSubview(self.verticalLine)
        self.verticalLine.snp.makeConstraints { make in
            make.top.equalTo(KLineConfig.kLineMainViewMinY)
            make.leading.equalTo(-1.0)
            let offset = KLineConfig.kLineMainViewMaxY + KLineConfig.kLineTimeViewHeight
            make.bottom.equalTo(self.simpleView.snp.bottom).offset(-offset)
            make.width.equalTo(0.5)
        }
    }
    
    public func showIndicatorViews(_ isShow: Bool, _ point: CGPoint?) {
        guard let p = point else {
            if !isShow {
                self.verticalLine.isHidden = !isShow
            }
            return
        }
        self.verticalLine.isHidden = !isShow
        
        self.verticalLine.snp.updateConstraints { make in
            make.leading.equalTo(p.x)
        }
    }
    
    // swiftlint:disable:next all
    public func needDrawModels(_ models: [KLineModel]?) {
        self.simpleView.needDrawModels(models)
    }
    
    /// 主题改变
    public func changeTheme(_ theme: Theme = KlineLibThemeManager.currentTheme) {
        self.verticalLine.backgroundColor = KLineConfig.themeManager.klineLibDefaultTextColor()
        self.simpleView.changeTheme()
    }
}
