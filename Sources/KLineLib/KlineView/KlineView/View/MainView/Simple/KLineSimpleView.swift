//
//  FILE.swift
//  Ourbit
//
//  Created by pippen on 2024.
//

import UIKit

open class KLineSimpleView: UIView {
    /// 需要绘制的K线数据
    // swiftlint:disable:next all
    public var needDrawKLineModels: [KLineModel]?
    
    public override init(frame: CGRect) {
        super.init(frame: frame)
        
        self.backgroundColor = UIColor.clear
        self.setNeedsDisplay()
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // Only override draw() if you perform custom drawing.
    // An empty implementation adversely affects performance during animation.
    open override func draw(_ rect: CGRect) {
        // Drawing code
        
        guard let context = UIGraphicsGetCurrentContext() else { return }
        if let model = KLineConfig.customGridModel{
            self.drawGrid(context: context, rect: rect, model: model)
        }else{
            self.drawGrid(context:context, rect:rect)
        }
        
        guard let needDrawKLineModels = self.needDrawKLineModels else { return }
        let path = self.drawWith(context, rect, needDrawKLineModels, false)
        
        guard let p = path else { return }
        context.addPath(p.cgPath)
        context.setLineWidth(2)
        context.setStrokeColor(KLineConfig.themeManager.mainGreenColor().cgColor)
        context.strokePath()
    }
    
    // swiftlint:disable:next all
    public func needDrawModels(_ models: [KLineModel]?) {
        self.needDrawKLineModels = models
        self.setNeedsDisplay()
    }
    
    /// 主题改变
    public func changeTheme() {
        self.setNeedsDisplay()
    }
}

extension KLineSimpleView {
    /// 绘制折线图
    public func drawWith(_ context: CGContext,
                  _ rect: CGRect,
                  _ positionArray: [KLineModel],
                  _ closed: Bool) -> UIBezierPath? {
        if positionArray.count < 1 { return nil }
        if positionArray.count < 2 {
            guard let model: KLineModel = positionArray.first else { return nil }
            guard let positionModel = model.positionModel else { return nil }
            let path = UIBezierPath()
            var point = positionModel.closePoint
            path.move(to: point)
            point.x += 1
            point.y += 1
            path.addLine(to: point)
            path.close()
            return path
        }
        let nCurves = closed ? positionArray.count : positionArray.count - 1
        let path = UIBezierPath()
        guard let model: KLineModel = positionArray.first else { return nil }
        // 最大值
        var maxValue = KLineConfig.type == .kline ? model.high : model.close
        // 最小值
        var minValue = KLineConfig.type == .kline ? model.low : model.close
        // 最大值位置
        var maxPoint = CGPoint.zero
        // 最小值位置
        var minPoint = CGPoint.zero
        
        for i in 0..<nCurves {
            let model: KLineModel = positionArray[i]
            guard let positionModel = model.positionModel else { continue }
            let value = positionModel.closePoint
            
            var curPt = CGPointZero,
                prevPt = CGPointZero,
                nextPt = CGPointZero,
                endPt = CGPointZero
            curPt = value
            
            if i == 0 {
                path.move(to: curPt)
                maxPoint = curPt
                minPoint = curPt
            }
            
            var nexti = (i + 1) % positionArray.count
            var previ = (i - 1 < 0 ? positionArray.count - 1 : i - 1)
            
            let preModel: KLineModel = positionArray[previ]
            guard let prePositionModel = preModel.positionModel else { continue }
            prevPt = prePositionModel.closePoint
            
            let nexModel: KLineModel = positionArray[nexti]
            guard let nexPositionModel = nexModel.positionModel else { continue }
            nextPt = nexPositionModel.closePoint
            
            endPt = nextPt
            
            var ob, my: CGFloat
            
            if closed || i > 0 {
                ob = (nextPt.x - curPt.x) * 0.5 + (curPt.x - prevPt.x) * 0.5
                my = (nextPt.y - curPt.y) * 0.5 + (curPt.y - prevPt.y) * 0.5
            } else {
                ob = (nextPt.x - curPt.x) * 0.5
                my = (nextPt.y - curPt.y) * 0.5
            }
            
            var ctrlPt1 = CGPointZero
            ctrlPt1.x = curPt.x + ob / 3.0
            ctrlPt1.y = curPt.y + my / 3.0
            
            curPt = nexPositionModel.closePoint
            
            nexti = (nexti + 1) % positionArray.count
            previ = i
            
            let preModel1: KLineModel = positionArray[previ]
            guard let prePositionModel1 = preModel1.positionModel else { continue }
            prevPt = prePositionModel1.closePoint
            
            let nexModel1: KLineModel = positionArray[nexti]
            guard let nexPositionModel1 = nexModel1.positionModel else { continue }
            nextPt = nexPositionModel1.closePoint
            
            if closed || (i < nCurves - 1) {
                ob = (nextPt.x - curPt.x) * 0.5 + (curPt.x - prevPt.x) * 0.5
                my = (nextPt.y - curPt.y) * 0.5 + (curPt.y - prevPt.y) * 0.5
            } else {
                ob = (curPt.x - prevPt.x) * 0.5
                my = (curPt.y - prevPt.y) * 0.5
            }
            
            var ctrlPt2: CGPoint = .zero
            ctrlPt2.x = curPt.x - ob / 3.0
            ctrlPt2.y = curPt.y - my / 3.0
            
            path.addCurve(to: endPt, controlPoint1: ctrlPt1, controlPoint2: ctrlPt2)
            
            // 价格应该是从上到下--由大到小
            if maxPoint.y < endPt.y {
                maxPoint = endPt
                
                if KLineConfig.type == .kline {
                    maxValue = nexModel.high
                } else {
                    maxValue = nexModel.close
                }
            }
            
            if minPoint.y > endPt.y {
                minPoint = endPt
                
                if KLineConfig.type == .kline {
                    minValue = nexModel.low
                } else {
                    minValue = nexModel.close
                }
            }
        }
        
        /// 精度
        var scale = model.priceScale
        if scale < 0 { scale = 2 }
        
        self.drawTime(rect.size.height - KLineConfig.kLineTimeViewHeight / 2.0 - 5.0, rect.width, positionArray)
        
        if closed {
            path.close()
        }
        return path
    }
}

