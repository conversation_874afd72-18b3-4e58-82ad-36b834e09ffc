//
//  FILE.swift
//  Ourbit
//
//  Created by pippen on 2024.
//

import UIKit

open class KLineProfessionalView: UIView {
    /// 需要绘制的K线数据
    public var needDrawKLineModels: [KLineModel]?
    /// 点击回调
    public var tapGesClickedBlock: (()->(Void))?
    
    public var heightHadUpdate: ((CGFloat) -> Void) = { _ in }
    
    public lazy var mainView: KLineMainView = {
        let v = KLineMainView()
        v.tapGesClickedBlock = { [weak self] in
            self?.tapGesClickedBlock?()
        }
        addSubview(v)
        return v
    }()
    
    public lazy var logo: UIImageView = {
        let iv = UIImageView(image: "kline_logo_new".themeImg(KlineLibThemeManager.currentTheme))
        return iv
    }()
    
    public lazy var mainLabel: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .medium))
          .textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
          .numberOfLines = 0
        lb.textAlignment = .left
        return lb
    }()
    
    
    // 副指标视图
    public lazy var subBgViews: [KLineSubBgView] = {
        var list: [KLineSubBgView] = []
        for i in 0...UserDefaults.CachedKLineSubTypeKeyCount {
            let v = KLineSubBgView()
            list.append(v)
            v.tag = i
        }
        return list
    }()
    
    
    
    public override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required public init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }
    
    /// 设置视图
    open func setupUI() {
        backgroundColor = UIColor.clear
        
        insertSubview(logo, belowSubview: mainView)
        
        mainView.addSubview(mainLabel)
        
        
        mainView.snp.makeConstraints { make in
            make.top.leading.bottom.trailing.equalToSuperview()
        }
        
        mainLabel.snp.makeConstraints { make in
            make.leading.equalTo(2)
            make.trailing.equalTo(-2).priority(999)
            make.top.equalTo(2)
        }
        
        logo.snp.makeConstraints { make in
            make.centerX.equalTo(mainView)
            make.centerY.equalTo(mainView).offset(-12)
        }
        
        
        // 副指标
        subBgViews.forEach({
            $0.isHidden = true
            addSubview($0)
        })
    }
    open func updateUI() {
        
        var j: Int = 0
        subBgViews.forEach({$0.isHidden = true})
        if let index = KLineConfig.subViewType.firstIndex(where: {$0 == .vol}){
            KLineConfig.subViewType.safeSwap(0, index)
        }
        
        KLineConfig.subViewType.forEachIndex { item, _ in
            if subBgViews.count > j {
                subBgViews[j].isHidden = false
                subBgViews[j].subView.subViewType = item
                subBgViews[j].middleLine.isHidden = true
                if item == .vol{
                    subBgViews[j].subRightView.isNeedFormat = true
                    subBgViews[j].middleLine.isHidden = false
                }else{
                    subBgViews[j].subRightView.isNeedFormat = false
                }
                
            }
            
                j += 1
        }
        let i = KLineConfig.subViewType.count
        // 修改：移除时间轴高度，时间轴将直接放在主图下方
        let bottom: CGFloat = -(KLineConfig.kLineSubViewHeight + KLineConfig.subVeiwHeaderHeight) * CGFloat(i) - KLineConfig.kLineTimeViewHeight
        
        var lastSubView: KLineSubBgView?
        for v in subBgViews {
            if v == subBgViews.first {
                v.snp.remakeConstraints { make in
                    switch KLineConfig.kLineTimeViewWithBottomView{
                    case .mainViewBottom:
                        make.top.equalTo(mainView.snp.bottom).offset(KLineConfig.kLineTimeViewHeight + KLineConfig.subVeiwHeaderHeight)
                    case .subViewBottom:
                        make.top.equalTo(mainView.snp.bottom).offset(KLineConfig.subVeiwHeaderHeight)
                    }
                    make.left.right.equalToSuperview()
                    make.height.equalTo(KLineConfig.kLineSubViewHeight)
                }
            } else {
                guard let preV = lastSubView else { continue }
                v.snp.remakeConstraints { make in
                    make.top.equalTo(preV.snp.bottom).offset(KLineConfig.subVeiwHeaderHeight)
                    make.left.right.equalToSuperview()
                    make.height.equalTo(KLineConfig.kLineSubViewHeight)
                }
            }
            lastSubView = v
        }
        mainView.snp.remakeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.bottom.equalTo(bottom).priority(999)
        }
        
        changeTheme()
        layoutIfNeeded()
        mainView.setNeedsDisplay()
    }
    
    // swiftlint:disable:next all
    public func needDrawModels(_ models: [KLineModel]?) {
        needDrawKLineModels = models
        mainView.needDrawModels(models)
        for v in subBgViews {
            if !v.isHidden {
                v.subView.needDrawModels(models)
            }
        }
        setNeedsDisplay()
        updateUI()
        
    }
    
    public func updateTopData(_ model: KLineModel) {
        
        guard KLineConfig.displayindexValueOnView else{
            mainLabel.attributedText = NSMutableAttributedString()
            for v in subBgViews {
              if v.isHidden == false {
                v.updateTopData(model)
              }
            }
            return
        }
        var mas = NSMutableAttributedString()
        let font = (UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .medium))
        let scale: Int = model.priceScale
        
        for type in KLineConfig.mainViewType {
            if type == .ma {
                for index in 0..<KLineConfig.MAArray.count{
                    let key =  KLineConfig.MAArray[index]
                    let color = KLineConfig.MAHexColor[index]
                    var valueString = "--"
                    let value = model.maDictionary["\(key)"]
                    if let value = value, !value.isEmpty {
                        valueString = ShowNumber(value, .fullDown(scale))
                    }
                    let str = "\(type.title)\(key):\(valueString)   "
                    mas.append(NSAttributedString(string: str,
                                                  attributes: [
                                                    NSAttributedString.Key.font: font,
                                                    NSAttributedString.Key.foregroundColor: color
                                                  ]))
                }
                if mas.length > 0 {
                    mas.append(NSAttributedString(string: "\n"))
                }
            }else if type == .ema{
                for index in 0..<KLineConfig.EMAArray.count{
                    let key =  KLineConfig.EMAArray[index]
                    let color = KLineConfig.EMAHexColor[index]
                    var valueString = "--"
                    let value = model.emaDictionary["\(key)"]
                    if let value = value, !value.isEmpty {
                        valueString = ShowNumber(value, .fullDown(scale))
                    }
                    let str = "\(type.title)\(key):\(valueString)   "
                    mas.append(NSAttributedString(string: str,
                                                  attributes: [
                                                    NSAttributedString.Key.font: font,
                                                    NSAttributedString.Key.foregroundColor: color
                                                  ]))
                }
                if mas.length > 0 {
                    mas.append(NSAttributedString(string: "\n"))
                }
                
            }else if type == .wma{
                for index in 0..<KLineConfig.WMAArray.count{
                    let key =  KLineConfig.WMAArray[index]
                    let color = KLineConfig.WMAHexColor[index]
                    var valueString = "--"
                    let value = model.wmaDictionary["\(key)"]
                    if let value = value, !value.isEmpty {
                        valueString = ShowNumber(value, .fullDown(scale))
                    }
                    let str = "\(type.title)\(key):\(valueString)   "
                    mas.append(NSAttributedString(string: str,
                                                  attributes: [
                                                    NSAttributedString.Key.font: font,
                                                    NSAttributedString.Key.foregroundColor: color
                                                  ]))
                }
                if mas.length > 0 {
                    mas.append(NSAttributedString(string: "\n"))
                }
                
            } else if type == .boll, KLineConfig.bollColorArray.count >= 3 {
                if let mb = model.mb, !mb.isEmpty {
                    mas.append(NSAttributedString(string: "BOLL:\(ShowNumber(mb, .fullDown(scale)))   ",
                                                  attributes: [
                                                    NSAttributedString.Key.font: font,
                                                    NSAttributedString.Key.foregroundColor:
                                                                KLineConfig.bollColorArray[0]
                                                  ]))
                }
                
                if let up = model.up, !up.isEmpty {
                    mas.append(NSAttributedString(string: "UB:\(ShowNumber(up, .fullDown(scale)))   ",
                                                  attributes: [
                                                    NSAttributedString.Key.font: font,
                                                    NSAttributedString.Key.foregroundColor:
                                                                KLineConfig.bollColorArray[1]
                                                  ]))
                }
                
                if let dn = model.dn, !dn.isEmpty {
                    mas.append(NSAttributedString(string: "LB:\(ShowNumber(dn, .fullDown(scale)))   ",
                                                  attributes: [
                                                    NSAttributedString.Key.font: font,
                                                    NSAttributedString.Key.foregroundColor:
                                                                KLineConfig.bollColorArray[2]
                                                  ]))
                }
                if model.mb?.isEmpty == false || model.up?.isEmpty == false || model.dn?.isEmpty == false {
                    mas.append(NSAttributedString(string: "\n"))
                }
            } else if type == .sar, let value = model.sar {
                mas.append(NSAttributedString(string: "SAR:\(ShowNumber("\(value)", .fullDown(scale)))   ",
                                              attributes: [
                                                NSAttributedString.Key.font: font,
                                                NSAttributedString.Key.foregroundColor:
                                                            KLineConfig.sarColorArray[0]
                                              ]))
                mas.append(NSAttributedString(string: "\n"))
            }
        }
        
        if KLineConfig.type != .timeLine && KLineConfig.type != .brokenLine{
            mainLabel.attributedText = mas
        }else{
            mainLabel.attributedText = NSMutableAttributedString()
        }
        mas = NSMutableAttributedString()
        


        for v in subBgViews {
            guard v.isHidden == false else { continue }
            v.updateTopData(model)
        }
    }
    
    public func clearData() {
        mainLabel.attributedText = NSAttributedString()
        mainView.clearData()
        
        for v in subBgViews {
            v.clearData()
        }
    }
    
    public func updateLatestPriceUI(_ maxValue: Double, _ minValue: Double, _ scale: Int, _ price: String,previousDayClosePrice: Double?) {
      mainView.updateLatestPriceUI(maxValue, minValue, scale, price,previousDayClosePrice: previousDayClosePrice)
    }
    
    open override func draw(_ rect: CGRect) {
        guard let needDrawKLineModels = needDrawKLineModels else { return }
        // 修改：时间轴绘制位置调整到主图下方
        var timelineY:CGFloat = 0.0
        switch KLineConfig.kLineTimeViewWithBottomView{
        case .mainViewBottom:
            timelineY = mainView.frame.maxY + KLineConfig.kLineTimeViewHeight / 2.0 - 6.5
        case .subViewBottom:
            timelineY = self.frame.maxY - KLineConfig.kLineTimeViewHeight + 5
        }
        if let model = KLineConfig.customGridModel {
            drawTime(pointY: timelineY, width: rect.width, model: model)
        }else{
            drawTime(timelineY, rect.width, needDrawKLineModels)
        }
    }
    
    public func updatePopData(kLineModel: KLineModel, price: String) {
        updateTopData(kLineModel)
    }
    
    /// 主题改变
    public func changeTheme(_ theme: Theme = KlineLibThemeManager.currentTheme) {
        setNeedsDisplay()
        logo.image = "kline_logo_new".themeImg(theme)
        mainView.changeTheme()
        
        for v in subBgViews {
            v.changeTheme()
        }
    }
    
    open override func layoutSubviews() {
        super.layoutSubviews()
        updateUI()
        heightHadUpdate(mainView.bounds.height)
    }
}
