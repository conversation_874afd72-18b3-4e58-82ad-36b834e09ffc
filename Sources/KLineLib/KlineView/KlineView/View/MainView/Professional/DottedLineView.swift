//
//  OBDottedLineView.swift
//  Ourbit
//
//  Created by pippen on 2024/1/12.
//

import UIKit

open class DottedLineView: UIView {
    /// 虚线方向
    open var dotDirection: StrockDirection = .horizontal { didSet { setNeedsDisplay() } }
    /// 是否显示虚线
    open var showDottedLine = true { didSet { setNeedsDisplay() } }
    /// 虚线颜色 默认为空，则与背景颜色一致
    open var dotColor: UIColor = .clear { didSet { setNeedsDisplay() } }
    /// 虚线前置相位
    open var phase = 0.0 { didSet { setNeedsDisplay() } }
    /// 虚线断点长度
    open var lengths: [CGFloat] = [2.0, 2.0] { didSet { setNeedsDisplay() } }
    
    override public init(frame: CGRect) {
        super.init(frame: frame)
        backgroundColor = .clear
    }
    
    public required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override open func layoutSubviews() {
        super.layoutSubviews()
        setNeedsDisplay()
    }
    
    override open func draw(_ rect: CGRect) {
        super.draw(rect)
        guard showDottedLine, let context = UIGraphicsGetCurrentContext() else { return }
        // 绘制虚线
        switch dotDirection {
        case .horizontal:
            // 以高度中心点为基准，虚线宽度等于视图高度
            let lineY: CGFloat = height / 2
            context.setLineWidth(height)
            context.move(to: CGPoint(x: 0, y: lineY))
            context.addLine(to: CGPoint(x: width, y: lineY))
        case .vertical:
            // 以宽度中心点为基准，虚线宽度等于视图宽度
            let lineX: CGFloat = width / 2
            context.setLineWidth(width)
            context.move(to: CGPoint(x: lineX, y: 0))
            context.addLine(to: CGPoint(x: lineX, y: height))
        }
        context.setStrokeColor(dotColor.cgColor)
        context.setLineDash(phase: phase, lengths: lengths)
        context.drawPath(using: .stroke)
    }
}


