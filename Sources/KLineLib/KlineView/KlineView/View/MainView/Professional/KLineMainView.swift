//
//  FILE.swift
//  Ourbit
//
//  Created by pippen on 2024.
//

import UIKit
public protocol KLineMainViewDelegate:NSObjectProtocol{
  func animationComplet()
}
open class KLineMainView: UIView, KlineAnimationProtocol {
    /// 需要绘制的K线数据
    public var needDrawKLineModels: [KLineModelStruct] = []
    /// 点击回调
    public var tapGesClickedBlock: (()->(Void))?
    
    public var maxValue: Double?, minValue: Double?, scale: Int?, price: String?
    
    /// 最新价虚线
    public lazy var lineView: DottedLineView = {
        let v = DottedLineView()
        v.dotDirection = .horizontal
        return v
    }()
    
    public lazy var drawView: KLineDrawView = {
        let v = KLineDrawView()
        return v
    }()
    
    /// 最新价
    public lazy var priceLabel: UILabel = {
        let l = UILabel()
        l.backgroundColor = KLineConfig.priceBgColor()
        l.font = UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular)
        l.textColor = KLineConfig.themeManager.mainGreenColor()
        l.textAlignment(.center).layer.masksToBounds = true
        l.layer.borderWidth = 1.0
        l.isUserInteractionEnabled = true
        l.addTapGestureRecognizer { [weak self] _ in
            self?.tapGesClickedBlock?()
        }
        return l
    }()
    
    /// K线倒计时
    public lazy var countDownLabel: UILabel = {
        let l = UILabel()
        l.font = UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular)
        l.textColor = KLineConfig.themeManager.mainGreenColor()
        l.textAlignment(.center).layer.masksToBounds = true
        l.backgroundColor = UIColor.clear
        return l
    }()
    
    /// 最新价倒计时模式
    public lazy var priceLabel2: UILabel = {
        let l = UILabel()
        l.font = UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular)
        l.textColor = KLineConfig.themeManager.mainGreenColor()
        l.textAlignment(.center).layer.masksToBounds = true
        l.backgroundColor = UIColor.clear
        return l
    }()
    
    /// K线倒计时
    public lazy var countDownContainerV: UIView = {
        let v = UIView()
        v.backgroundColor = KLineConfig.themeManager.bgPrimary()
        v.cornerRadius = 4.0
        v.borderWidth = 1.0
        v.borderColor = KLineConfig.themeManager.mainGreenColor()
        
        let stackView = UIStackView(arrangedSubviews: [priceLabel2, countDownLabel])
        stackView.axis = .vertical
        stackView.alignment = .trailing
        stackView.distribution = .fillEqually
        stackView.spacing = -1
        v.addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.top.leading.equalTo(4)
            make.trailing.bottom.equalTo(-4)
        }
        return v
    }()
    
    /// 价格左箭头
    public lazy var priceLeftLabel: UILabel = {
        let lb = UILabel()
        lb.textFont(.systemFont(ofSize: 18))
            .textColor(KLineConfig.themeManager.mainGreenColor())
            .bgColor(.clear)
            .textAlignment(.center)
        lb.isHidden = true
        return lb
    }()
    
    public lazy var pointView: GradientCycleView = {
        let v = GradientCycleView()
        v.isHidden = true
        return v
    }()
    
    
    /// 价格是否右边显示
    public var rightPriceModel = true
    public var displayLink: CADisplayLink? = nil
    public var lastTimeClose:Double? = nil
    public var lastPositionModel:KLinePositionModel? = nil
  /// 上一个交易日最后一条数据
  public var previousDayClosePrice:Double? = nil
  //MARK: Michael: 解决动画完成后 重新滑动到最后一条数据时postion 错误issue
  public var delegate:KLineMainViewDelegate?
    public override init(frame: CGRect) {
        super.init(frame: frame)
        
        self.backgroundColor = UIColor.clear
        self.addSubview(lineView)
        lineView.snp.makeConstraints { make in
            make.top.leading.trailing.equalTo(0)
            make.height.equalTo(1)
        }
        
        self.addSubview(priceLabel)
        priceLabel.snp.makeConstraints { make in
            make.centerY.equalTo(lineView)
            make.trailing.equalTo(0)
            make.height.equalTo(16)
            make.width.equalTo(0)
        }
        
        self.addSubview(countDownContainerV)
        countDownContainerV.snp.makeConstraints { make in
            make.centerY.equalTo(lineView)
            make.trailing.equalTo(0)
        }
        
        self.addSubview(priceLeftLabel)
        priceLeftLabel.snp.makeConstraints { make in
            make.centerY.equalTo(self.priceLabel).offset(-0.3)
            make.trailing.equalTo(self.priceLabel.snp.leading).offset(7)
        }
        
        self.addSubview(pointView)
        pointView.snp.makeConstraints { make in
            make.top.leading.equalTo(0)
            make.width.height.equalTo(20)
        }
        
//        if KLineOrderManager.shared.showDelegateOrder == false,
//           KLineOrderManager.shared.isContract == true {

//        }
        
        self.clearData()
        
        self.setUpKLineDrawShape()
        
        self.setNeedsDisplay()
        
    }
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    deinit {
        self.destoryKLineDrawShape()
    }
    
    /// 防止CALayer动画影响
    open override func action(for layer: CALayer, forKey event: String) -> CAAction? { return nil }
    
    public func updateLatestPriceUI(_ maxValue: Double, _ minValue: Double, _ scale: Int, _ price: String,previousDayClosePrice:Double?) {
        //MARK: Michael: 更新Y轴price 值
        self.drawPriceWith(rect: self.bounds, model: KLineConfig.customGridModel, maxValue: maxValue, minValue: minValue, scale: scale,previousDayClosePrice: previousDayClosePrice)
        
        drawView.drawViewModel.maxValue = maxValue
        drawView.drawViewModel.minValue = minValue
        drawView.drawViewModel.pricesScale = scale
        
        self.maxValue = maxValue
        self.minValue = minValue
        self.scale = scale
        self.previousDayClosePrice = previousDayClosePrice
        self.price = price
        changeTheme()
        guard let model = needDrawKLineModels.last, let positionModel = model.positionModel else {
            lineView.isHidden = true
            priceLabel.isHidden = true
            priceLeftLabel.isHidden = true
            pointView.isHidden = true
            return
        }
        lineView.isHidden = false
        priceLabel.isHidden = false
        let priceStr = ShowNumber(price, .fullDown(scale))
        let with = priceStr.size(withAttributes: [.font: UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular)]).width + 6
        
        let minY = KLineConfig.kLineMainViewMinY
        let maxY = height - KLineConfig.kLineMainViewMaxY
        var unitValue = 1.0
        
        if maxY != minY && maxValue != minValue {
            unitValue = (maxValue - minValue) / Double((maxY - minY))
        }
        var topPoint = (maxY - CGFloat((price.doubleValue() - minValue) / unitValue))
        
        if topPoint < minY {
            topPoint = minY
        } else if topPoint > maxY {
            topPoint = maxY
        }
        
        if positionModel.closePoint.x + with + 3 > self.width {
            lineView.snp.updateConstraints { make in
                make.leading.equalTo(0)
                make.top.equalTo(topPoint)
            }
            
            priceLabel.snp.updateConstraints { make in
                make.trailing.equalTo(-80)
                make.width.equalTo(with + 16)
            }
            priceLabel.text = String(format: "%@ →", priceStr)
            priceLabel.layer.cornerRadius = 4.0
            
            priceLeftLabel.isHidden = true
            pointView.isHidden = true
            pointView.stopAnimation()
            
            rightPriceModel = false
        } else {
            pointView.snp.updateConstraints { make in
                make.leading.equalTo(positionModel.closePoint.x - 10)
                make.top.equalTo(topPoint - 10)
            }
            
            lineView.snp.updateConstraints { make in
                make.leading.equalTo(positionModel.closePoint.x)
                make.top.equalTo(topPoint)
            }
            
            priceLabel.snp.updateConstraints { make in
                make.trailing.equalTo(0)
                make.width.equalTo(with)
            }
            priceLabel.text = priceStr
            priceLabel.layer.cornerRadius = 4.0
            priceLeftLabel.isHidden = true
            
            if KLineConfig.type == .timeLine {
                pointView.isHidden = false
                //pointView.startAnimation()
            } else {
                pointView.isHidden = true
                pointView.stopAnimation()
            }
            
            rightPriceModel = true
        }
        priceLabel.layer.borderColor = backLogicColor().cgColor
        priceLabel.backgroundColor = KLineConfig.themeManager.bgPrimary().withAlphaComponent(0.85)
        
        lineView.isHidden = false
        priceLabel.isHidden = false
        self.setNeedsLayout()
    }
    
    public func clearData() {
        lineView.isHidden = true
        priceLabel.isHidden = true
        countDownContainerV.isHidden = true
        priceLeftLabel.isHidden = true
        pointView.isHidden = true
        pointView.stopAnimation()
    }
    
    // Only override draw() if you perform custom drawing.
    // An empty implementation adversely affects performance during animation.
    open override func draw(_ rect: CGRect) {
        // Drawing code
        guard let context = UIGraphicsGetCurrentContext() else { return }
        if let model = KLineConfig.customGridModel{
            self.drawGrid(context: context, rect: rect, model: model,isMainView: true)
        }else{
            self.drawGrid(context:context, rect:rect,isMainView: true)
        }
        
        
        if let maxValue = self.maxValue, let minValue = self.minValue, let scale = self.scale, let price = self.price {
          self.updateLatestPriceUI(maxValue, minValue, scale, price,previousDayClosePrice: self.previousDayClosePrice)
        }
        switch KLineConfig.type{
        case .timeLine,.brokenLine:
            let color = KLineConfig.type == .timeLine ? KLineConfig.themeManager.mountainColor() :  KLineConfig.themeManager.brokenLineColor()
            var array = [CGPoint]()
            
            for model in needDrawKLineModels {
                guard let positionModel = model.positionModel else { continue }
                let value: CGPoint = positionModel.closePoint
                array.append(value)
            }
            let isTimeLine = KLineConfig.type == .timeLine
            let curves = isTimeLine
            self.drawLineWith(context:context, rect: rect,positionArray: array,color: color,width: CGFloat(1),gradient: isTimeLine,curves: curves)
        case .kline,.americanLine,.hollowCandle:
            
            self.drawHatching(context:context, rect:rect, positionArray:needDrawKLineModels,klineType: KLineConfig.type)
            
            
        case .fiveDayLine:
            let color = KLineConfig.themeManager.mountainColor()
          var array = [[CGPoint]]()
          let timestamp = needDrawKLineModels.map({$0.timestamp}).map({$0.timestampToDateString(format: "MM,dd")}).uniqued()
          timestamp.forEach { date in
            array.append([])
          }
          for model in needDrawKLineModels {
              guard let positionModel = model.positionModel else { continue }
              let value: CGPoint = positionModel.closePoint
              let date = model.timestamp.timestampToDateString(format: "MM,dd")
              if let index = timestamp.firstIndex(where: {$0 == date}){
                array[index].append(value)
              }
              
          }
          
          self.drawFiveDayLineWith(context:context, rect: rect,positionArrays: array,color: color,width: CGFloat(1))
          /*
            var array = [CGPoint]()
            
            for model in needDrawKLineModels {
                guard let positionModel = model.positionModel else { continue }
                let value: CGPoint = positionModel.closePoint
                array.append(value)
            }
            self.drawFiveDayLineWith(context:context, rect: rect,positionArray: array,color: color,width: CGFloat(1),timeLine: true)
            */
        }
        addKLineIndicator(needDrawKLineModels: needDrawKLineModels, rect: rect)
        p_drawBuySellPoint()
    }
  //MARK: Michael: 添加主图指标
  private func addKLineIndicator(needDrawKLineModels:[KLineModelStruct],rect: CGRect){
    guard let context = UIGraphicsGetCurrentContext() else { return }
    context.resetClip()
    for type in KLineConfig.mainViewType {
        if type == .ma {
            for index in 0..<KLineConfig.MAArray.count{
                let key =  KLineConfig.MAArray[index]
                let color = KLineConfig.MAHexColor[index]
                let list = needDrawKLineModels.compactMap({$0.positionModel}).compactMap({$0.maPoints["\(key)"]})
                self.drawLineWith(context:context, rect:rect,positionArray: list,color: color, width:(1), gradient: false)
            }
        }else if type == .ema {
            for index in 0..<KLineConfig.EMAArray.count{
                let key =  KLineConfig.EMAArray[index]
                let color = KLineConfig.EMAHexColor[index]
                let list = needDrawKLineModels.compactMap({$0.positionModel}).compactMap({$0.emaPoints["\(key)"]})
                self.drawLineWith(context:context, rect:rect,positionArray: list,color: color, width:(1), gradient: false)
            }
        }else if type == .wma {
            for index in 0..<KLineConfig.WMAArray.count{
                let key =  KLineConfig.WMAArray[index]
                let color = KLineConfig.WMAHexColor[index]
                let list = needDrawKLineModels.compactMap({$0.positionModel}).compactMap({$0.wmaPoints["\(key)"]})
                self.drawLineWith(context:context, rect:rect,positionArray: list,color: color, width:(1), gradient: false)
            }
        } else if type == .boll {
            let arrayMB = needDrawKLineModels.compactMap({$0.positionModel}).compactMap({$0.bollPoints["BOLL_MB"]})
            let arrayUP = needDrawKLineModels.compactMap({$0.positionModel}).compactMap({$0.bollPoints["BOLL_UP"]})
            let arrayDN = needDrawKLineModels.compactMap({$0.positionModel}).compactMap({$0.bollPoints["BOLL_DN"]})
            if KLineConfig.bollColorArray.count >= 3 {
                self.drawLineWith(context:context, rect:rect,positionArray: arrayMB,color: KLineConfig.bollColorArray[0], width:CGFloat(1), gradient: false)
                self.drawLineWith(context:context, rect:rect,positionArray: arrayUP,color: KLineConfig.bollColorArray[1], width:CGFloat(1), gradient: false)
                self.drawLineWith(context:context, rect:rect,positionArray: arrayDN,color: KLineConfig.bollColorArray[2], width:CGFloat(1), gradient: false)
            }
        } else if type == .sar {
            for model in needDrawKLineModels {
                guard let positionModel = model.positionModel, let point = positionModel.sarPoints else {
                    continue
                }
                if positionModel.sarUp {
                    KLineConfig.themeManager.mainRedColor().setStroke()
                } else {
                    KLineConfig.themeManager.mainGreenColor().setStroke()
                }
                context.setLineWidth(1)
                context.addArc(center: point,
                               radius: 2,
                               startAngle: 0,
                               endAngle: Double.pi * 2,
                               clockwise: true)
                context.drawPath(using: .stroke)
            }
        }else if type == .vwap{
          if KLineConfig.type == .fiveDayLine{
            var array = [[CGPoint]]()
            let timestamp = needDrawKLineModels.map({$0.timestamp}).map({$0.timestampToDateString(format: "MM,dd")}).uniqued()
            timestamp.forEach { date in
              array.append([])
            }
            for model in needDrawKLineModels {
                guard let positionModel = model.positionModel else { continue }
                let value: CGPoint = positionModel.averagePoint
                let date = model.timestamp.timestampToDateString(format: "MM,dd")
                if let index = timestamp.firstIndex(where: {$0 == date}){
                  array[index].append(value)
                }
                
            }
            
            self.drawFiveDaysVwapLineWith(context: context, rect: rect, positionArrays: array, color: KLineConfig.themeManager.klineAverageLineColor(), width: 1.0)
          }else{
            let average = needDrawKLineModels.compactMap({$0.positionModel}).map({$0.averagePoint}).compactMap({$0})
            self.drawLineWith(context:context, rect:rect,positionArray: average,color: KLineConfig.themeManager.klineAverageLineColor(), width:(1), gradient: false)
          }
          
        }
    }
  }
    private func p_drawBuySellPoint() {
      let haveOrderModel = needDrawKLineModels.filter({$0.orderModels != nil})
      haveOrderModel.forEach { model in
        var color:UIColor = KLineConfig.themeManager.buyingAndSellingPointColor()
        var value:String = "T"
        guard let orderModels = model.orderModels,orderModels.count >= 1,let order = orderModels.last else{return}
        let type = orderModels.map({$0.content}).uniqued()
        if type.count == 1{
          color = orderModels.first!.bgColor
          value = orderModels.first!.content
        }
        
        guard let context = UIGraphicsGetCurrentContext() ,let positionModel = model.positionModel else { return }
        context.resetClip()
        
        
        var point:CGPoint = .zero
        var pointDownwards = true
        //MARK: Michael: 确定朝向logic 开始 ===============>>>>>>>>>>>>>>>>>
        //MARK: Michael: 设置point基准
        switch KLineConfig.type{
        case .timeLine,.fiveDayLine,.brokenLine:
          //MARK: Michael: 山型图折线图等只使用了 closePoint 绘制 所以基准点为 closePoint
          point = positionModel.closePoint
        default:
          //MARK: Michael: 其他图形 都以 highPoint 为基准
          point = positionModel.highPoint
        }
        //MARK: Michael: 以上方Point 为基准判断是否能放下买卖点
        let tempDashedLineStartPoint = CGPoint(x: point.x, y: point.y - 2)
        let tempDashedLineEndPoint = CGPoint(x: tempDashedLineStartPoint.x, y: tempDashedLineStartPoint.y - 8)
        
        // 买卖点矢量图
        let image = KLineConfig.themeManager.klineBuySellPointImage()
        let temPimage = UIImage(cgImage: image.cgImage!, scale: image.scale, orientation: .up)
        let tempPoint = CGPoint(x: point.x - temPimage.size.width / 2.0, y: tempDashedLineEndPoint.y - temPimage.size.height)
        
        //MARK: Michael: 确定朝向
        pointDownwards = tempPoint.y > 0
        
        //MARK: Michael: 确定朝向logic结束 <<<<<<<<<<<<<===============
        
        //MARK: Michael: 确定point基准
        switch KLineConfig.type{
        case .timeLine,.fiveDayLine,.brokenLine:
          point = positionModel.closePoint
        default:
          point = pointDownwards ? positionModel.highPoint : positionModel.lowPoint
        }
        let contextWidth = value.textWidth(font: order.font, maxHeight: order.radius)
        let contextHeight = value.textHeight(font: order.font, maxWidth: order.radius)
        
        //MARK: Michael: 处理 当买卖点在下方时 买卖点被画在视图外的情况
        if pointDownwards == false,(KLineConfig.type == .americanLine || KLineConfig.type == .hollowCandle || KLineConfig.type == .kline){
          let  dashedLineStartPoint = CGPoint(x: point.x, y: point.y + 2)
          let  dashedLineEndPoint = CGPoint(x: dashedLineStartPoint.x, y: dashedLineStartPoint.y + 8)
          let needDrawImage = UIImage(cgImage: image.cgImage!, scale: image.scale, orientation: .down)
          //let textPoint = CGPoint(x: point.x - contextWidth / 2.0, y: dashedLineEndPoint.y + contextHeight / 3.0)
          let imagePoint = CGPoint(x: point.x - needDrawImage.size.width / 2.0, y: dashedLineEndPoint.y)
          
          if imagePoint.y >= self.bounds.size.height{
            point = positionModel.highPoint
          }
        }
        /*
        let path = UIBezierPath()
//           
//           // 底部尖点
//           path.move(to: CGPoint(x: width/2, y: height))
//        
//        
//           // 左侧曲线（三次贝塞尔）
//           path.addCurve(to: CGPoint(x: width/2, y: 0),
//                       controlPoint1: CGPoint(x: -width/4, y: height*3/4),
//                       controlPoint2: CGPoint(x: width/4, y: height/4))
//           
//           // 右侧曲线（对称）
//           path.addCurve(to: CGPoint(x: width/2, y: height),
//                       controlPoint1: CGPoint(x: width*3/4, y: height/4),
//                       controlPoint2: CGPoint(x: width*5/4, y: height*3/4))
           
        
        
        path.move(to: CGPoint(x: point.x + width / 2.0, y: point.y + height))
        
        // 左侧曲线（三次贝塞尔）
        path.addCurve(to: CGPoint(x: point.x + width/2, y: point.y),
                      controlPoint1: CGPoint(x:point.x + -width/4, y: point.y + height*3/4),
                      controlPoint2: CGPoint(x:point.x +  width/4, y: point.y + height/4))
        
        // 右侧曲线（对称）
        path.addCurve(to: CGPoint(x:point.x +  width/2, y:point.y + height),
                      controlPoint1: CGPoint(x:point.x +  width*3/4, y: point.y + height/4),
                      controlPoint2: CGPoint(x:point.x +  width*5/4, y: point.y + height*3/4))
        path.close()
        

        color.setFill()
        path.fill()
  
        */
        //MARK: Michael: 绘制真实坐标点
        UIColor.black.withAlphaComponent(0.6).setFill()
        context.addArc(center: point,
                       radius: 2,
                       startAngle: 0,
                       endAngle: Double.pi * 2,
                       clockwise: true)
        context.drawPath(using: .fill)
        
        
        //绘制虚线
        let path = UIBezierPath()
        var dashedLineStartPoint:CGPoint = .zero
        var dashedLineEndPoint:CGPoint = .zero
        
        //MARK: Michael: 确定买卖点方向
        var needDrawImage = UIImage(cgImage: image.cgImage!, scale: image.scale, orientation: pointDownwards ? .up : .down)
        //MARK: Michael: 确定买卖点图片颜色
        needDrawImage = needDrawImage.withTintColor(color)
        
        var textPoint:CGPoint = .zero
        var imagePoint:CGPoint = .zero
        
        //MARK: Michael: 右侧无法完整显示
        if point.x + needDrawImage.size.width / 2.0 > self.bounds.size.width{
          if pointDownwards{
            //买卖点尖头朝下
            dashedLineStartPoint = CGPoint(x: point.x, y: point.y - 2)
            dashedLineEndPoint = CGPoint(x: dashedLineStartPoint.x - 10.0, y: dashedLineStartPoint.y - 8)
          }else{
            //买卖点尖头朝上
            dashedLineStartPoint = CGPoint(x: point.x, y: point.y + 2)
            dashedLineEndPoint = CGPoint(x: dashedLineStartPoint.x - 10.0, y: dashedLineStartPoint.y + 8)
          }
          //MARK: Michael: 左侧无法完整显示
        }else if point.x - needDrawImage.size.width / 2.0 < 0{
          if pointDownwards{
            //买卖点尖头朝下
            dashedLineStartPoint = CGPoint(x: point.x, y: point.y - 2)
            dashedLineEndPoint = CGPoint(x: dashedLineStartPoint.x + 10.0, y: dashedLineStartPoint.y - 8)
          }else{
            //买卖点尖头朝上
            dashedLineStartPoint = CGPoint(x: point.x, y: point.y + 2)
            dashedLineEndPoint = CGPoint(x: dashedLineStartPoint.x + 10.0, y: dashedLineStartPoint.y + 8)
          }
          //MARK: Michael: 可以完整显示
        }else{
          if pointDownwards{
            //买卖点尖头朝下
            dashedLineStartPoint = CGPoint(x: point.x, y: point.y - 2)
            dashedLineEndPoint = CGPoint(x: dashedLineStartPoint.x, y: dashedLineStartPoint.y - 8)
          }else{
            //买卖点尖头朝上
            dashedLineStartPoint = CGPoint(x: point.x, y: point.y + 2)
            dashedLineEndPoint = CGPoint(x: dashedLineStartPoint.x, y: dashedLineStartPoint.y + 8)
          }
        }
        
        if pointDownwards{
          //买卖点尖头朝下
          textPoint = CGPoint(x: dashedLineEndPoint.x - contextWidth / 2.0, y: dashedLineEndPoint.y - contextHeight - needDrawImage.size.height / 4)
          imagePoint = CGPoint(x: dashedLineEndPoint.x - needDrawImage.size.width / 2.0, y: dashedLineEndPoint.y - needDrawImage.size.height)
        }else{
          //买卖点尖头朝上
          textPoint = CGPoint(x: dashedLineEndPoint.x - contextWidth / 2.0, y: dashedLineEndPoint.y + contextHeight / 3.0)
          imagePoint = CGPoint(x: dashedLineEndPoint.x - needDrawImage.size.width / 2.0, y: dashedLineEndPoint.y)
        }
        
        path.move(to: dashedLineStartPoint)
        path.addLine(to: dashedLineEndPoint)
        
        let dashes: [CGFloat] = [3, 2]
        path.setLineDash(dashes, count: dashes.count, phase: 0)
        path.lineWidth = 2.0
        UIColor.black.withAlphaComponent(0.6).setStroke()
        path.stroke()
        
        //MARK: Michael: 绘制买卖点图片到point
        needDrawImage.draw(at: imagePoint)
        
        let attributes: [NSAttributedString.Key: Any] = [
          NSAttributedString.Key.foregroundColor: UIColor.white,//order.color,
          NSAttributedString.Key.font: order.font
        ]
        let attributeStr = NSAttributedString(string: value, attributes: attributes)
        //MARK: Michael: 绘制买卖点类型到point
        attributeStr.draw(at: textPoint)
        
        
        /* 原来绘制买卖点方法
        guard var point = model.positionModel?.highPoint else { return }
        
        let contextWidth = value.textWidth(font: order.font, maxHeight: order.radius)
        let contextHeight = value.textHeight(font: order.font, maxWidth: order.radius)
        //MARK: Michael: 让上影线漏出来
        point = CGPoint(x: point.x, y: point.y - order.radius - 2.0)
        let textPoint = CGPoint(x: point.x - contextWidth / 2.0, y: point.y - contextHeight / 2.0)
        color.setFill()
        context.addArc(center: point,
                       radius: order.radius,
                       startAngle: 0,
                       endAngle: Double.pi * 2,
                       clockwise: true)
        context.drawPath(using: .fill)
        
        let attributes: [NSAttributedString.Key: Any] = [
          NSAttributedString.Key.foregroundColor: order.color,
          NSAttributedString.Key.font: order.font
        ]
        let attributeStr = NSAttributedString(string: value, attributes: attributes)
        attributeStr.draw(at: textPoint)
        */
        
      }
      
      /*
        guard let needDrawKLineModels = self.needDrawKLineModels else { return  }
        let haveOrderModel = needDrawKLineModels.filter({$0.orderModels != nil})
        let orderModels = haveOrderModel.compactMap({$0.orderModels}).flatMap({$0})
        guard orderModels.count > 0 else{return}
        guard let context = UIGraphicsGetCurrentContext() else { return }
        context.resetClip()
        // 画B/S数据
            for order in orderModels {
                guard let point = order.point else { continue }
                let color: UIColor = order.bgColor
                
                let contextWidth = order.content.textWidth(font: order.font, maxHeight: order.radius)
                let contextHeight = order.content.textHeight(font: order.font, maxWidth: order.radius)
                let textPoint = CGPoint(x: point.x - contextWidth / 2.0, y: point.y - contextHeight / 2.0)
                color.setFill()
                if order.shape == .round {
                    context.addArc(center: point,
                                   radius: order.radius,
                                   startAngle: 0,
                                   endAngle: Double.pi * 2,
                                   clockwise: true)
                    context.drawPath(using: .fill)
                } else {
                    let offset: CGFloat = -order.radius
                    let rect = CGRect(x: point.x - order.radius, y: point.y + offset, width: order.radius * 2, height: order.radius * 2)
                    let path = UIBezierPath(roundedRect: rect, cornerRadius: 4)
                    
                    let y = rect.origin.y + rect.height
                    path.move(to: CGPoint(x: point.x - 2, y: y))
                    path.addLine(to: CGPoint(x: point.x, y: y + 3))
                    path.addLine(to: CGPoint(x: point.x + 2, y: y))
                    path.close()
                    let clipPath: CGPath = path.cgPath
                    context.addPath(clipPath)
                    context.fillPath()
                }
                
                let attributes: [NSAttributedString.Key: Any] = [
                    NSAttributedString.Key.foregroundColor: order.color,
                    NSAttributedString.Key.font: order.font
                ]
                let attributeStr = NSAttributedString(string: order.content, attributes: attributes)
                attributeStr.draw(at: textPoint)
            }
        */
    }
    
    // swiftlint:disable:next all
    public func needDrawModels(_ models: [KLineModel]?) {
      guard let models = models else{return}
      self.drawView.updateKLineModels(models)
      self.needDrawKLineModels = models.compactMap({$0.mapToTempKLineModel()})
      if self.needDrawKLineModels.contains(where: {$0.needAnimation == true}){
        stopAnimation()
        startAnimation()
        if KLineConfig.type == .timeLine{
          pointView.startAnimation()
        }else{
          pointView.stopAnimation()
        }
      }else{
        self.setNeedsDisplay()
      }
        
    }
  public func startAnimation() {
    //MARK: Michael: 防止最后一条闪屏 将最后一次的位置模型赋值给空的那条
    if let index = self.needDrawKLineModels.lastIndex(where: {$0.positionModel == nil}){
      self.needDrawKLineModels[index].positionModel = lastPositionModel
    }
    guard displayLink == nil else {return}
    displayLink = CADisplayLink(target: self, selector: #selector(tempUpdateAnimation))
    displayLink?.add(to: .main, forMode: .common)
    
  }
  
  @objc func tempUpdateAnimation(){
    updateAnimation {[weak self] maxValue, minValue, price in
      guard let self = self else { return }
      self.updateLatestPriceUI(maxValue, minValue, self.scale ?? 3, price, previousDayClosePrice: self.previousDayClosePrice)
    } animationComplet: {[weak self]  in
      guard let self = self else { return }
      self.pointView.stopAnimation()
      self.delegate?.animationComplet()
    }

  }

    
    /// 主题改变
    public func changeTheme() {
        self.setNeedsDisplay()
        self.lineView.dotColor = backLogicColor()
        self.priceLabel2.textColor = backLogicColor()
        self.priceLabel.textColor = backLogicColor()
        self.countDownLabel.textColor = backLogicColor()
        self.countDownContainerV.backgroundColor = KLineConfig.themeManager.bgPrimary()
        self.countDownContainerV.borderColor = backLogicColor()//KLineConfig.themeManager.mainGreenColor()
    }
  private func backLogicColor()->UIColor{
    if KLineConfig.latestPriceColorWithPreviousDay , let previousDayClosePrice = self.previousDayClosePrice,let price = self.price{
      var color:UIColor = KLineConfig.themeManager.mainGreenColor()
      let latestPrice = price.doubleValue()
      if latestPrice > previousDayClosePrice{
        color = KLineConfig.themeManager.mainGreenColor()
      }else if latestPrice == previousDayClosePrice{
        color = KLineConfig.themeManager.textGreyColor()
      }else{
        color = KLineConfig.themeManager.mainRedColor()
      }
      return color
    }
    return KLineConfig.themeManager.mainGreenColor()
  }
}

extension KLineMainView {
    /// 画上下影线
    public func drawHatching(context: CGContext,rect: CGRect,positionArray: [KLineModelStruct],klineType:KLineViewType) {
        if positionArray.count < 1 { return }
        // swiftlint:disable:next all
        let firstModel: KLineModelStruct = positionArray.first!
        guard let positionModel = firstModel.positionModel else { return }
        let isPro = (KLineConfig.type != .timeLine || KLineConfig.type != .brokenLine)  && KLineConfig.kLineType == .professional
        
        // 最大值
        var maxValue = isPro ? firstModel.high : firstModel.close
        // 最小值
        var minValue = isPro ? firstModel.low : firstModel.close
        // 最大值位置
        var maxPoint = isPro ? positionModel.highPoint : positionModel.closePoint
        // 最小值位置
        var minPoint = isPro ? positionModel.lowPoint : positionModel.closePoint
        // 画中间较宽的开收盘线段-实体线
        let lineWidth = KLineConfig.kLineWidth
        let templineWidth = calculateLineWidth(oldlineWidth: lineWidth, contentWidth: rect.width, count: positionArray.count)
        for model in positionArray {
            if let positionModel = model.positionModel {
                
                
                // 设置画笔颜色
                context.setStrokeColor(positionModel.color.cgColor)
                
                let openPoint = positionModel.openPoint
                let closePoint = positionModel.closePoint
                let highPoint = positionModel.highPoint
                let lowPoint = positionModel.lowPoint
                
                switch klineType {
                case .kline:

                    context.setLineWidth(templineWidth)
                    let solidPoints: [CGPoint] = [openPoint, closePoint]
                    // 画线
                    context.strokeLineSegments(between: solidPoints)
                    // 画上下影线
                    context.setLineWidth(1)
                    let shadowPoints: [CGPoint] = [highPoint, lowPoint]
                    // 画线
                    context.strokeLineSegments(between: shadowPoints)
                case .americanLine:
                    context.setLineWidth(1)
                    let upPath = CGMutablePath()
                    let openPointEnd = CGPoint(x: openPoint.x - lineWidth/2, y: openPoint.y)
                    upPath.move(to:openPoint)
                    upPath.addLine(to:openPointEnd)
                    context.addPath(upPath)
                    context.strokePath()
                    
                    
                    let downPath = CGMutablePath()
                    let closePointEnd = CGPoint(x: closePoint.x + lineWidth/2, y: closePoint.y)
                    downPath.move(to:closePoint)
                    downPath.addLine(to:closePointEnd)
                    context.addPath(downPath)
                    context.strokePath()
                    // 画上下影线
                    context.setLineWidth(1)
                    let shadowPoints: [CGPoint] = [highPoint, lowPoint]
                    // 画线
                    context.strokeLineSegments(between: shadowPoints)
                case .hollowCandle:
                    
                    let openLeft = CGPoint(x: openPoint.x - templineWidth / 2, y: openPoint.y)
                    let openRight = CGPoint(x: openPoint.x + templineWidth / 2, y: openPoint.y)
                    
                    let closeRight = CGPoint(x: closePoint.x + templineWidth / 2, y: closePoint.y)
                    let closeLeft = CGPoint(x: closePoint.x - templineWidth / 2, y: closePoint.y)
                    let path = CGMutablePath()
                    
                  if model.open < model.close{//上升显示空心
                    context.setLineWidth(1)
                    path.move(to: openLeft)
                    path.addLine(to: closeLeft)
                    path.addLine(to: closeRight)
                    path.addLine(to: openRight)
                    path.closeSubpath()
                  }else{//下降显示实心
                    context.setLineWidth(templineWidth)
                    let solidPoints: [CGPoint] = [openPoint, closePoint]
                    // 画线
                    context.strokeLineSegments(between: solidPoints)
                  }
                  context.setLineWidth(1)
                    //MARK: Michael: 上下引线
                    if model.open > model.close{
                        path.move(to: openPoint)
                        path.addLine(to: highPoint)
                        path.closeSubpath()
                        path.move(to: closePoint)
                        path.addLine(to: lowPoint)
                        path.closeSubpath()
                    }else{
                        path.move(to: openPoint)
                        path.addLine(to: lowPoint)
                        path.closeSubpath()
                        path.move(to: closePoint)
                        path.addLine(to: highPoint)
                        path.closeSubpath()
                    }
                    
                    
                    
                    context.addPath(path)
                    context.strokePath()
                default:break
                }
                
                
                // 价格应该是从上到下--由大到小
                if isPro {
                    if maxValue < model.high {
                        maxValue = model.high
                        maxPoint = highPoint
                    }
                    
                    if minValue > model.low {
                        minValue = model.low
                        minPoint = lowPoint
                    }
                } else {
                    if maxValue < model.close {
                        maxValue = model.close
                        maxPoint = closePoint
                    }
                    
                    if minValue > model.close {
                        minValue = model.close
                        minPoint = closePoint
                    }
                }
            }else{
              print("无positionModel")
            }
        }
        
        /// 精度
        var scale = firstModel.priceScale
        if scale < 0 { scale = 2 }
        self.drawIndicator(rect: rect, point: maxPoint, value: String(maxValue), scale: scale, isMin: false)
        self.drawIndicator(rect: rect, point: minPoint, value: String(minValue), scale: scale, isMin: true)
    }
    
    /// 绘制最大值最小值
    public func drawIndicator(rect: CGRect,point: CGPoint,value: String,scale: Int,isMin: Bool) {
        let isShowLeft: Bool = point.x > rect.size.width / 2.0
        let valueStr = ShowNumber(value, .fullDown(scale))
        // swiftlint:disable:next all
        let showString = "\(valueStr)" as NSString
        let font = UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular)
        let textSize = showString.size(withAttributes: [NSAttributedString.Key.font: font])
        var textPoint = CGPoint(x: point.x - 1.0, y: point.y + (isMin ? -(textSize.height + 3.0) : 3.0))
        let textColor = KLineConfig.themeManager.klineLibHihtLowPriceTextColor()
      //绘制虚线
      let path = UIBezierPath()
      var dashedLineEndPoint:CGPoint = .zero
      let xOffset = isShowLeft ? -20.0 : 20.0
      let yOffset = point.x < textSize.width ? isMin ? -10.0 : 10.0 : 0
      
      dashedLineEndPoint = CGPoint(x: point.x + xOffset, y: point.y + yOffset)
      
      
      path.move(to: point)
      path.addLine(to: dashedLineEndPoint)
      path.lineWidth = 0.5
      textColor.setStroke()
      path.stroke()
      
      if isShowLeft{
        textPoint = CGPoint(x: dashedLineEndPoint.x - textSize.width, y: dashedLineEndPoint.y - textSize.height / 2.0)
      }else{
        textPoint = CGPoint(x: dashedLineEndPoint.x, y: dashedLineEndPoint.y - textSize.height / 2.0)
      }
      /*
        if KLineConfig.type != .timeLine {
            showString = NSString(string: "一\(valueStr)")
            if isShowLeft { showString = "\(valueStr)一" as NSString }
        }
        
        
        if isShowLeft { textPoint = CGPoint(x: point.x - textSize.width + 1.0,
                                            y: point.y + (isMin ? -(textSize.height + 3.0) : 3.0)) }
        
        if KLineConfig.type != .timeLine {
            textPoint = CGPoint(x: point.x - 0.5, y: point.y - textSize.height / 2.0)
            if isShowLeft { textPoint = CGPoint(x: point.x - textSize.width - 0.5, y: point.y - textSize.height / 2.0) }
        }
        */
        
        let bgColor = UIColor.clear
        let attr = [
            NSAttributedString.Key.font: font,
            NSAttributedString.Key.foregroundColor: textColor,
            NSAttributedString.Key.backgroundColor: bgColor
        ]
        showString.draw(at: textPoint, withAttributes: attr)
    }
}
extension KLineMainView {
    public func p_updateDrawView() {
        // 当前存在画数据
        if !KLineDrawViewModel.shared.drawModelList.isEmpty {
            self.addSubview(self.drawView)
            self.sendSubviewToBack(self.drawView)
            self.drawView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            self.drawView.isHidden = false
        } else {
            if self.drawView.superview != nil {
                self.drawView.removeFromSuperview()
            }
        }
    }
    
    public func setUpKLineDrawShape() {
        self.p_updateDrawView()
        
        CusstomNotification.observe(self, KLineDrawModeDidLoadNotification) { [weak self] _ in
            guard let self = self else { return }
            self.p_updateDrawView()
        }
        
        CusstomNotification.observe(self, KLineDrawModeDidChangedNotification) { [weak self] notify in
            guard let drawStatus = notify.object as? KLineDrawStatus, let self = self else { return }
            
            if drawStatus != .none {
                // 添加画线view
                if self.drawView.superview == nil {
                    self.addSubview(self.drawView)
                    self.sendSubviewToBack(self.drawView)
                    self.drawView.snp.remakeConstraints { make in
                        make.edges.equalToSuperview()
                    }
                }
                self.drawView.isHidden = false
                self.drawView.enableEvent = drawStatus == .drawShape
            } else {
                self.drawView.isHidden = KLineDrawViewModel.shared.drawModelList.isEmpty
                self.drawView.enableEvent = false
            }
            
            self.priceLabel.isUserInteractionEnabled = (drawStatus != .drawShape)
        }
    }
    
    public func destoryKLineDrawShape() {
        CusstomNotification.remove(self, KLineDrawModeDidLoadNotification)
        CusstomNotification.remove(self, KLineDrawModeDidChangedNotification)
    }
}
