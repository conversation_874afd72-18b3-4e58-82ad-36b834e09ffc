//
//  File.swift
//  KLineLib
//
//  Created by <PERSON> on 2025/4/23.
//

import Foundation
import UIKit
public class KlineOrderModel{
    public enum PointShape{
        case round
        case square
    }
    
    /// 显示点的Font
    public var font:UIFont = UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular)
    /* 之前项目中的3种颜色
    KLineConfig.themeManager.candleDeclineColor()
    KLineConfig.themeManager.candleRiseColor()
    KLineConfig.themeManager.orangeColor()
    */
    /// 显示点的背景色
    public var bgColor:UIColor = .red
    
    /// 显示点的文字颜色
    public var color:UIColor = .white
    
    /// 显示点的内容
    public var content: String = "B" //B S L
    
    /// 当前point价格 点显示的Y坐标取决于 这个price
    public var price:Double = 0.0
    
    /// 不用赋值 需在适当实际自动计算
    public var point: CGPoint? = nil
    
    /// 显示点的类型 （round 或 square）//废弃 仅适用圆形
    public var shape:PointShape = .round
    
    /// 点显示的半径
    public var radius:CGFloat = 7.0
   
  public init(font: UIFont, bgColor: UIColor, color: UIColor, content: String, price: Double, shape: PointShape = .round, radius: CGFloat) {
        self.font = font
        self.bgColor = bgColor
        self.color = color
        self.content = content
        self.price = price
        self.shape = shape
        self.radius = radius
    }
    public init(){
        
    }
}
