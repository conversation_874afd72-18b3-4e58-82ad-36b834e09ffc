//
//  CustomGridModel.swift
//  KLineLib
//
//  Created by <PERSON> on 2025/4/14.
//

import Foundation
import UIKit
public struct CustomGridModel{
    
    /// 网格颜色
    public var dividerColor:UIColor = KLineConfig.themeManager.dividerColor()
    /// 背景网格水平格数（平分）
    public var horizontalLineCount: Int = 5
    
    /// 需要不显示的index集合
    public var needHiddenIndexs:[Int] = []
    ///需要显示的时间数组 注意使用24小时制 垂直线条数 按此数组数量绘制
    public var keyPoints:[Date] = []
    
    /// 需要忽略的 时间
    public var ignorePoints:[(start:Date,end:Date)] = []
    
    /// 时间轴需要显示的格式 为空时显示传入的数组原始值 不为空时 将上方时间数组转为当前dateFormart 显示
    public var dateFormat:String = "HH:mm:ss"
    
    public init(dividerColor: UIColor, horizontalLineCount: Int, needHiddenIndexs: [Int], keyPoints: [Date], ignorePoints: [(start: Date, end: Date)], dateFormat: String) {
        self.dividerColor = dividerColor
        self.horizontalLineCount = horizontalLineCount
        self.needHiddenIndexs = needHiddenIndexs
        self.keyPoints = keyPoints
        self.ignorePoints = ignorePoints
        self.dateFormat = dateFormat
    }
    public init(){
        
    }
    
    /// 根据视图宽度直接计算出 每个x的为位置
    /// - Parameter width: 视图宽度
    /// - Returns: -
    public func calculateXposition(width:CGFloat)->[CGFloat]{
        
        guard keyPoints.count >= 2 else {return []}
        let ignore = ignorePoints.compactMap({$0.end.timeIntervalSince1970 - $0.start.timeIntervalSince1970}).sorted(by: {$0 < $1})
        let times = keyPoints.compactMap({$0.timeIntervalSince1970}).sorted(by: {$0 < $1})

        // 绘制 X轴参考线
        guard let firstTime = times.first,let lastTime = times.last else{return []}
        var timeDifference = CGFloat(lastTime - firstTime)
        ignore.forEach { value in
            timeDifference -= value
        }
        let step = width / timeDifference
        var xPositions:[CGFloat] = []
        for index in 0..<keyPoints.count {
            var current = times[index] - firstTime
            if let ignoreindex =  ignorePoints.lastIndex(where: {$0.end.timeIntervalSince1970 <= times[index]}){
                for t in 0..<ignore.count{
                    if t <= ignoreindex{
                        current -= ignore[t]
                    }
                }
            }
            
            let x = step * CGFloat(current)
            xPositions.append(x)
        }
        return xPositions
    }
    public func calculateSubModels(width:CGFloat)->[CustomGridSubModel]{
        var result:[CustomGridSubModel] = []
        let xPositions = self.calculateXposition(width: width)
        var index = 0
        keyPoints.forEach { date in
            var model = CustomGridSubModel(timeStr: (date.timeIntervalSince1970 * 1000).timestampToDateString(format: dateFormat))
            ignorePoints.forEach { (start,end) in
                if date == start || date == end{
                    model.type = .critical
                }else if date < start || date > end{
                    model.type = .normal
                }else{
                    model.type = .discard
                }
            }
            model.x = xPositions[index]
            result.append(model)
            index += 1
        }
        return result
    }
    public enum DateType{
        case normal
        case critical
        case discard
    }
    public struct CustomGridSubModel{
        var timeStr:String
        var type:DateType = .normal
        var x:CGFloat = 0.0
    }
}
