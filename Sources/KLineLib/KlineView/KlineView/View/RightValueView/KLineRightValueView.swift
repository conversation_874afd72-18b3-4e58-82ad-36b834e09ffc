//
//  FILE.swift
//  Ourbit
//
//  Created by pippen on 2024.
//

import UIKit
import SnapKit
open  class KLineRightValueView: UIView {
    //MARK: Michael: 看到之前代码只有vol 传的true  其他都为false
  public var isNeedFormat = false{
    didSet{
      if isNeedFormat {
        middleLlabel.isHidden = !isNeedFormat
      }
    }
  }
    
    public lazy var topLabel: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .textColor(KLineConfig.themeManager.klineLibSubTextColor())
            .text("")
            .bgColor(.clear)
            .textAlignment(.right)
        return lb
    }()
    
    public lazy var middleLlabel: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .textColor(KLineConfig.themeManager.klineLibSubTextColor())
            .text("")
            .bgColor(.clear)
            .textAlignment(.right)
        return lb
    }()
    
    public lazy var bottomLabel: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .textColor(KLineConfig.themeManager.klineLibSubTextColor())
            .text("")
            .bgColor(.clear)
            .textAlignment(.right)
        return lb
    }()
    
    public override init(frame: CGRect) {
        super.init(frame: frame)
        
        self.addSubview(topLabel)
        topLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.leading.trailing.equalToSuperview()
        }
        
        self.addSubview(middleLlabel)
        middleLlabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.trailing.equalToSuperview()
        }
        
        self.addSubview(bottomLabel)
        bottomLabel.snp.makeConstraints { make in
            make.bottom.equalToSuperview()
            make.leading.trailing.equalToSuperview()
        }
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    public func updateData(_ maxValue: Double, _ minValue: Double, _ scale: Int) {
        let step = (maxValue - minValue) / 2.0
        
        if isNeedFormat {
            topLabel.text(AccuracyManager.formatbAbreviationQuantityNum(accuracy: scale, quantity: maxValue))
            middleLlabel.text(AccuracyManager.formatbAbreviationQuantityNum(accuracy: scale, quantity: maxValue - step))
            bottomLabel.text(AccuracyManager.formatbAbreviationQuantityNum(accuracy: scale, quantity: minValue))
        } else {
            topLabel.text(ShowNumber(String(maxValue), .fullDown(scale)))
            middleLlabel.text(ShowNumber(String(maxValue - step), .fullDown(scale)))
            bottomLabel.text(ShowNumber(String(minValue), .fullDown(scale)))
        }
        
        
        switch KLineConfig.pricePosition{
        case .left(_):
            topLabel.textAlignment(.left)
            middleLlabel.textAlignment(.left)
            bottomLabel.textAlignment(.left)
        case .right(_):
            topLabel.textAlignment(.right)
            middleLlabel.textAlignment(.right)
            bottomLabel.textAlignment(.right)
        }
    }
    
    public func clearData() {
        topLabel.text("")
        middleLlabel.text("")
        bottomLabel.text("")
    }
    
    /// 主题改变
    public func changeTheme() {
        self.topLabel.textColor(KLineConfig.themeManager.klineLibSubTextColor())
        self.middleLlabel.textColor(KLineConfig.themeManager.klineLibSubTextColor())
        self.bottomLabel.textColor(KLineConfig.themeManager.klineLibSubTextColor())
    }
}
