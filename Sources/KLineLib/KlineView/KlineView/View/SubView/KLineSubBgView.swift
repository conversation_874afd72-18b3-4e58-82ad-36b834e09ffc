//
//  FILE.swift
//  Ourbit
//
//  Created by pippen on 2024.
//

import UIKit

open class KLineSubBgView: UIView {
    public lazy var subView: KLineSubView = {
        let v = KLineSubView()
        return v
    }()
    
    public lazy var subRightView: KLineRightValueView = {
        let v = KLineRightValueView()
        v.middleLlabel.isHidden = true
        v.bottomLabel.isHidden = true
        return v
    }()
    
    public lazy var subLabel: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .medium))
          .textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
           .numberOfLines = 0
        lb.textAlignment(.left)
        return lb
    }()
    public lazy var middleLine:UIView = {
      let view = UIView()
      view.backgroundColor = KLineConfig.themeManager.dividerColor()
      view.isHidden = true
      
      return view
  }()
    
    public init() {
        super.init(frame: .zero)
        initUI()
    }
    
    required public init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }
    
    public func initUI() {
        
        
        addSubview(middleLine)
        addSubview(subLabel)
        addSubview(subView)
        insertSubview(subRightView, aboveSubview: subView)
        
        subLabel.snp.makeConstraints { make in
            make.leading.equalTo(2)
            make.trailing.equalTo(-2).priority(999)
            make.top.equalTo(2)
        }
        
        subView.snp.makeConstraints { make in
            make.top.equalTo(0)
            make.leading.equalTo(0)
            make.trailing.equalTo(0)
            make.bottom.equalTo(0)
        }
        
      subRightView.snp.remakeConstraints{ make in
            make.top.equalTo(0)
            switch KLineConfig.pricePosition{
            case .left(let offset):
                make.left.equalToSuperview().offset(offset)
            case .right(let offset):
                make.right.equalToSuperview().offset(offset)
            }
            make.bottom.equalTo(0)
        }
      
        middleLine.snp.makeConstraints { make in
          make.leading.centerY.right.equalToSuperview()
          make.height.equalTo(1.0)
        }
    }
    public func updateTopData(_ model: KLineModel) {
        
        if let references = KLineConfig.references,let _ = references.first(where: {$0.type == subView.subViewType}){
            self.subRightView.isHidden = true
        }else{
            self.subRightView.isHidden = false
        }
        
        guard KLineConfig.displayindexValueOnView else {
            subLabel.attributedText = NSMutableAttributedString()
            return
        }
        let mas = NSMutableAttributedString()
        let scale: Int = model.priceScale
        
        // 更新k线指标数据
        if subView.subViewType == .vol{
            do {
                let volScale = model.quantityScale ?? 0.0
                let volStr = "Vol:\(AccuracyManager.quantityNum(accuracy: volScale, quantity: model.volume))   "
                
                mas.append(masAppend(str: volStr, color: KLineConfig.themeManager.klineLibSubTextColor()))
                var index = 0
                
                KLineConfig.volMAArray.forEach { number in
                    if let value = model.volMADictionary[String(number)], KLineConfig.volMAColorArray.count > index {
                        let volValue = AccuracyManager.quantityNum(accuracy: Int(volScale), quantity: value)
                        mas.append(masAppend(str: "MA\(number):\(volValue)   ", color: KLineConfig.volMAColorArray[index]))
                    }
                    index += 1
                }
            }
        }else if subView.subViewType == .macd &&
           KLineConfig.macdArray.count >= 3 &&
           KLineConfig.macdColorArray.count >= 2 {
            let m1 = KLineConfig.macdArray[0]
            let m2 = KLineConfig.macdArray[1]
            let m3 = KLineConfig.macdArray[2]
            mas.append(masAppend(str: "MACD(\(m1),\(m2),\(m3))   ",
                                 color: KLineConfig.themeManager.klineLibSubTextColor()))
            if model.macd != nil {
                let settingColor = model.macd < 0 ? KLineConfig.themeManager.candleRiseColor() : KLineConfig.themeManager.candleDeclineColor()
                mas.append(masAppend(str: "MACD:\(ShowNumber(model.macd, .fullDown(scale)))   ",
                                     color: settingColor))
            }
            
            if model.dif != nil {
                mas.append(masAppend(str: "DIF:\(ShowNumber(model.dif, .fullDown(scale)))   ",
                                     color: KLineConfig.macdColorArray[0]))
            }
            
            if model.dea != nil {
                mas.append(masAppend(str: "DEA:\(ShowNumber(model.dea, .fullDown(scale)))   ",
                                     color: KLineConfig.macdColorArray[1]))
            }
        } else if subView.subViewType == .kdj &&
                  KLineConfig.kdjArray.count >= 3 &&
                  KLineConfig.kdjColorArray.count >= 3 {
            let m1 = KLineConfig.kdjArray[0]
            let m2 = KLineConfig.kdjArray[1]
            let m3 = KLineConfig.kdjArray[2]
            mas.append(masAppend(str: "KDJ(\(m1),\(m2),\(m3))   ",
                                 color: KLineConfig.themeManager.klineLibSubTextColor()))
            
            if model.k != nil  {
                mas.append(masAppend(str: "K:\(ShowNumber(model.k, .fullDown(scale)))   ",
                                     color: KLineConfig.kdjColorArray[0]))
            }
            
            if model.d != nil {
                mas.append(masAppend(str: "D:\(ShowNumber(model.d, .fullDown(scale)))   ",
                                     color: KLineConfig.kdjColorArray[1]))
            }
            
            if model.j != nil {
                mas.append(masAppend(str: "J:\(ShowNumber(model.j, .fullDown(scale)))   ",
                                     color: KLineConfig.kdjColorArray[2]))
            }
        } else if subView.subViewType == .rsi {
            var index = 0
            KLineConfig.rsiArray.forEach { number in
                if let value = model.rsiDictionary[String(number)], KLineConfig.rsiColorArray.count > index {
                    mas.append(masAppend(str: "RSI\(number):\(ShowNumber(value, .fullDown(scale)))   ",
                                         color: KLineConfig.rsiColorArray[index]))
                }
                index += 1
            }
        } else if subView.subViewType == .wr {
            var index = 0
            KLineConfig.wrArray.forEach { number in
                if let value = model.wrDictionary[String(number)], KLineConfig.wrColorArray.count > index {
                    mas.append(masAppend(str: "WR\(number):\(ShowNumber(value, .fullDown(scale)))   ",
                                         color: KLineConfig.wrColorArray[index]))
                }
                index += 1
            }
        } else if subView.subViewType == .roc {
            // 参数
            if KLineConfig.rocArray.isEmpty == false {
                let param = KLineConfig.rocArray.map { String($0) }.joined(separator: ",")
                mas.append(masAppend(str: "ROC(\(param))  ",
                                     color: KLineConfig.themeManager.klineLibSubTextColor()))
            }
            // 指标值
            if KLineConfig.rocColorArray.count > 1 {
                var v1 = "--"
                var v2 = "--"
                if let value = model.rocDictionary["0"] { v1 = ShowNumber("\(value)", .fullDown(scale)) }
                if let value = model.rocDictionary["1"] { v2 = ShowNumber("\(value)", .fullDown(scale)) }
                
                mas.append(masAppend(str: "ROC:\(v1)   ",
                                     color: KLineConfig.rocColorArray[0]))
                mas.append(masAppend(str: "MAROC:\(v2)",
                                     color: KLineConfig.rocColorArray[1]))
            }
        } else if subView.subViewType == .stochRSI {
            // 参数
            if KLineConfig.stochRSIArray.isEmpty == false {
                let param = KLineConfig.stochRSIArray.map { String($0) }.joined(separator: ",")
                mas.append(masAppend(str: "STOCHRSI(\(param))   ",
                                     color: KLineConfig.themeManager.klineLibSubTextColor()))
            }
            // 指标值
            if KLineConfig.stochRSIColorArray.count > 1 {
                var v1 = "--"
                var v2 = "--"
                if let value = model.stochRSIDictionary["0"] { v1 = ShowNumber("\(value)", .fullDown(scale)) }
                if let value = model.stochRSIDictionary["1"] { v2 = ShowNumber("\(value)", .fullDown(scale)) }
                
                mas.append(masAppend(str: "STOCHRSI:\(v1)   ",
                                     color: KLineConfig.rocColorArray[0]))
                mas.append(masAppend(str: "MASTOCHRSI:\(v2)",
                                     color: KLineConfig.rocColorArray[1]))
            }
        } else if subView.subViewType == .cci {
            // 参数
            if KLineConfig.cciArray.isEmpty == false {
                let m1 = KLineConfig.cciArray[0]
                mas.append(masAppend(str: "CCI(\(m1))   ",
                                     color: KLineConfig.themeManager.klineLibSubTextColor()))
            }
            // 指标值
            if KLineConfig.rocColorArray.isEmpty == false {
                var value = "--"
                if let cci = model.cci { value = ShowNumber("\(cci)", .fullDown(scale)) }
                mas.append(masAppend(str: "CCI:\(value)",
                                     color: KLineConfig.rocColorArray[0]))
            }
        } else if subView.subViewType == .obv {
            // 参数
            if KLineConfig.obvArray.isEmpty == false {
                let param = KLineConfig.obvArray.map { String($0) }.joined(separator: ",")
                mas.append(masAppend(str: "OBV(\(param))   ",
                                     color: KLineConfig.themeManager.klineLibSubTextColor()))
            }
            // 指标值
            if KLineConfig.obvColorArray.count > 1 {
                var v1 = "--"
                var v2 = "--"
                if let obv = model.obvDictionary["0"] { v1 = ShowNumber("\(obv)", .fullDown(2)) }
                if let obv = model.obvDictionary["1"] { v2 = ShowNumber("\(obv)", .fullDown(2)) }
                
                mas.append(masAppend(str: "OBV:\(v1)   ",
                                     color: KLineConfig.obvColorArray[0]))
                mas.append(masAppend(str: "MAOBV:\(v2)",
                                     color: KLineConfig.obvColorArray[1]))
            }
        } else if subView.subViewType == .trix {
            // 参数
            if KLineConfig.trixArray.isEmpty == false {
                let m1 = KLineConfig.trixArray[0]
                mas.append(masAppend(str: "TRIX(\(m1))   ",
                                     color: KLineConfig.themeManager.klineLibSubTextColor()))
            }
            // 指标值
            if KLineConfig.trixColorArray.isEmpty == false {
                var value = "--"
                if let v = model.trix { value = ShowNumber("\(v)", .fullDown(scale)) }
                mas.append(masAppend(str: "TRIX:\(value)",
                                     color: KLineConfig.trixColorArray[0]))
            }
        } else if subView.subViewType == .dmi {
            // 参数
            if KLineConfig.dmiArray.count > 1 {
                let m1 = KLineConfig.dmiArray[0]
                let m2 = KLineConfig.dmiArray[1]
                mas.append(masAppend(str: "DMI(\(m1),\(m2))   ",
                                     color: KLineConfig.themeManager.klineLibSubTextColor()))
            }
            // 指标值
            if KLineConfig.dmiColorArray.count > 3 {
                let array = ["PDI", "MDI", "ADX", "ADXR"]
                var i = 0
                for str in array {
                    var value = "--"
                    if let v = model.dmiDictionary["\(i)"] {
                        value = ShowNumber("\(v)", .fullDown(scale))
                    } else {
                        value = "--"
                    }
                    let space = i != array.count - 1 ? "   " : ""
                    mas.append(masAppend(str: "\(str):\(value)\(space)",
                                         color: KLineConfig.dmiColorArray[i]))
                    i += 1
                }
            }
        }
        subLabel.attributedText = mas
    }
    
    /// 拼接k线头部信息
    public func masAppend(str: String, color: UIColor) -> NSMutableAttributedString {
        let mas = NSMutableAttributedString()
        let font = UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .medium)
        mas.append(NSAttributedString(string: str,
                                      attributes: [
                                        NSAttributedString.Key.font: font,
                                        NSAttributedString.Key.foregroundColor: color
                                      ]))
        return mas
    }
    
    public func clearData() {
        subLabel.attributedText = NSAttributedString()
        subRightView.clearData()
    }
    
    public func changeTheme() {
        subView.changeTheme()
        subRightView.changeTheme()
    }
}
