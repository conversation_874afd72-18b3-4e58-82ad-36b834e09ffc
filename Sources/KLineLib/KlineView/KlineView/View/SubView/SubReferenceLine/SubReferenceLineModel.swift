//
//  SubReferenceLineModel.swift
//  KLineLib
//
//  Created by <PERSON> on 2025/4/18.
//

import Foundation
import UIKit

/// 副视图参考线 模型
public struct SubReferenceLineModel{
    
    /// 当前添加的视图类型
    public var type:KLineSubViewType = .rsi
    
    /// 最大值
    public var maxValue:CGFloat = 0.0
    
    /// 最小值
    public var minValue:CGFloat = 0.0
    
    /// 参考线数组
    public var referenceList:[CGFloat] = []
    
    /// 虚线颜色
    public var dotColor:UIColor = KLineConfig.themeManager.klineLibSubTextColor()
    /// 虚线断点长度
    public var lengths: [CGFloat] = [2.0, 2.0]
    /// 虚线上文字的 字体
    public var font:UIFont = .systemFont(ofSize: 10.0)
    
    /// 虚线上文字的 字体颜色
    public var labTextColor:UIColor = KLineConfig.themeManager.klineLibSubTextColor()
    
    /// 参考区间 填充颜色
    public var fillColor:UIColor = KLineConfig.themeManager.klineLibSubTextColor().withAlphaComponent(0.1)
    
    public init(type: KLineSubViewType,
                maxValue: CGFloat,
                minValue: CGFloat,
                referenceList: [CGFloat],
                dotColor: UIColor = KLineConfig.themeManager.klineLibSubTextColor(),
                lengths: [CGFloat] = [2.0, 2.0],
                font: UIFont = .systemFont(ofSize: 10.0),
                labTextColor: UIColor = KLineConfig.themeManager.klineLibSubTextColor(),
                fillColor:UIColor = KLineConfig.themeManager.klineLibSubTextColor().withAlphaComponent(0.1)) {
        self.type = type
        self.maxValue = maxValue
        self.minValue = minValue
        self.referenceList = referenceList
        self.dotColor = dotColor
        self.lengths = lengths
        self.font = font
        self.labTextColor = labTextColor
        self.fillColor = fillColor
    }
    public init(){
        
    }
   
    
}

