//
//  FILE.swift
//  Ourbit
//
//  Created by pippen on 2024.
//

import UIKit

open class KLineSubView: UIView {
    /// 副指标类型
    public var subViewType: KLineSubViewType = .close
    /// 需要绘制的K线数据
    // swiftlint:disable:next all
    public var needDrawKLineModels: [KLineModel]?
    
    public override init(frame: CGRect) {
        super.init(frame: frame)
        
        self.backgroundColor = UIColor.clear
        self.setNeedsDisplay()
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // Only override draw() if you perform custom drawing.
    // An empty implementation adversely affects performance during animation.
    open override func draw(_ rect: CGRect) {
        // Drawing code
        
        guard let context = UIGraphicsGetCurrentContext() else { return }
        if let model = KLineConfig.customGridModel{
            self.drawGrid(context: context, rect: rect, model: model)
        }else{
            self.drawGrid(context:context, rect:rect)
        }
        
        guard let needDrawKLineModels = self.needDrawKLineModels else { return }
        //MARK: Michael: 添加参考线
        self.subviews.forEach({$0.removeFromSuperview()})
        if let references = KLineConfig.references,let model = references.first(where: {$0.type == subViewType}){
            self.addReferenceLine(model: model)
        }
        if subViewType == .vol {
            let array = needDrawKLineModels.compactMap({$0.positionModel}).compactMap({$0.volPoints})
            let color = needDrawKLineModels.compactMap({$0.positionModel}).compactMap({$0.color})
            
            self.drawColumn(context: context, rect: rect, positionArray: array, color: color, lineWidth: KLineConfig.kLineWidth)
            
            var index = 0
            
            // swiftlint:disable:next all
          KLineConfig.volMAArray.forEach({ number in
            if KLineConfig.volMAColorArray.count > index {
              let key = String(number)
              let color = KLineConfig.volMAColorArray[index]
              let list = needDrawKLineModels.compactMap({$0.positionModel}).compactMap({$0.volMAPoints[key]})
              self.drawLineForReferenceWith(context:context, rect:rect,positionArray: list,color: color,width: CGFloat(1))
              index += 1
            }
          })
        }else if subViewType == .macd {
          let macdArray = needDrawKLineModels.compactMap({$0.positionModel}).compactMap({$0.macdPoints})
          let color = needDrawKLineModels.map({$0.macd < 0.0 ? KLineConfig.themeManager.candleRiseColor() : KLineConfig.themeManager.candleDeclineColor()})
          
            self.drawColumn(context: context, rect: rect, positionArray: macdArray, color: color, lineWidth: KLineConfig.kLineWidth)
            
            let difArray = needDrawKLineModels.compactMap({$0.positionModel}).compactMap({$0.difPoint})
          
            var difColor: UIColor = .clear
            if KLineConfig.macdColorArray.count >= 1 { difColor = KLineConfig.macdColorArray[0] }
            self.drawLineForReferenceWith(context: context, rect: rect, positionArray: difArray, color: difColor, width: CGFloat(1))
            
            let deaArray = needDrawKLineModels.compactMap({$0.positionModel}).compactMap({$0.deaPoint})
            
            var deaColor: UIColor = .clear
            if KLineConfig.macdColorArray.count >= 2 { deaColor = KLineConfig.macdColorArray[1] }
            self.drawLineForReferenceWith(context: context, rect: rect, positionArray: deaArray, color: deaColor, width: CGFloat(1))
        } else if subViewType == .kdj {
          let arrayK = needDrawKLineModels.compactMap({$0.positionModel}).compactMap({$0.kdjPoints["KDJ_K"]})
          let arrayD = needDrawKLineModels.compactMap({$0.positionModel}).compactMap({$0.kdjPoints["KDJ_D"]})
          let arrayJ = needDrawKLineModels.compactMap({$0.positionModel}).compactMap({$0.kdjPoints["KDJ_J"]})
          
          var kColor: UIColor = .clear
          if KLineConfig.kdjColorArray.count >= 1 { kColor = KLineConfig.kdjColorArray[0] }
          var dColor: UIColor = .clear
          if KLineConfig.kdjColorArray.count >= 2 { dColor = KLineConfig.kdjColorArray[1] }
          var jColor: UIColor = .clear
          if KLineConfig.kdjColorArray.count >= 3 { jColor = KLineConfig.kdjColorArray[2] }
          self.drawLineForReferenceWith(context: context, rect: rect, positionArray: arrayK, color: kColor.withAlphaComponent(0.5), width: CGFloat(1))
          self.drawLineForReferenceWith(context: context, rect: rect, positionArray: arrayD, color: dColor.withAlphaComponent(0.5), width: CGFloat(1))
          self.drawLineForReferenceWith(context: context, rect: rect, positionArray: arrayJ, color: jColor.withAlphaComponent(0.5), width: CGFloat(1))
        } else if subViewType == .rsi {
            var index = 0
            
            KLineConfig.rsiArray.forEach({ number in
                if KLineConfig.rsiColorArray.count > index {
                    let key = String(number)
                    let color = KLineConfig.rsiColorArray[index].withAlphaComponent(0.5)
                  let array = needDrawKLineModels.compactMap({$0.positionModel}).compactMap({$0.rsiPoints[key]})
                  self.drawLineForReferenceWith(context: context, rect: rect, positionArray: array, color: color, width: CGFloat(1))
                    index += 1
                }
            })
        } else if subViewType == .wr {
            var index = 0
            
            KLineConfig.wrArray.forEach({ number in
                if KLineConfig.wrColorArray.count > index {
                    let key = String(number)
                    let color = KLineConfig.wrColorArray[index].withAlphaComponent(0.5)
                    let array = needDrawKLineModels.compactMap({$0.positionModel}).compactMap({$0.wmaPoints[key]})
                    self.drawLineForReferenceWith(context: context, rect: rect, positionArray: array, color: color, width: CGFloat(1))
                    index += 1
                }
            })
        } else if subViewType == .roc {
            var index = 0
            
            KLineConfig.rocArray.forEach({ _ in
                if KLineConfig.rocColorArray.count > index {
                    let color = KLineConfig.rocColorArray[index].withAlphaComponent(0.5)
                    let array = needDrawKLineModels.compactMap({$0.positionModel}).compactMap({$0.rocPoints["\(index)"]})
                    self.drawLineForReferenceWith(context: context, rect: rect, positionArray: array, color: color, width: CGFloat(1), curves: false)
                    index += 1
                }
            })
        } else if subViewType == .stochRSI {
            var index = 0
            
            KLineConfig.stochRSIArray.forEach({ _ in
                if KLineConfig.stochRSIColorArray.count > index {
                    let color = KLineConfig.stochRSIColorArray[index].withAlphaComponent(0.5)
                    let array = needDrawKLineModels.compactMap({$0.positionModel}).compactMap({$0.stochRSIPoints["\(index)"]})
                    self.drawLineForReferenceWith(context: context, rect: rect, positionArray: array, color: color, width: CGFloat(1), curves: false)
                    index += 1
                }
            })
        } else if subViewType == .obv {
            var index = 0
            
            KLineConfig.obvColorArray.forEach({ _ in
                if KLineConfig.obvColorArray.count > index {
                    let color = KLineConfig.obvColorArray[index].withAlphaComponent(0.5)
                    let array = needDrawKLineModels.compactMap({$0.positionModel}).compactMap({$0.obvPoints["\(index)"]})
                    self.drawLineForReferenceWith(context: context, rect: rect, positionArray: array, color: color, width: CGFloat(1), curves: false)
                    index += 1
                }
            })
        } else if subViewType == .cci {
            if !KLineConfig.cciColorArray.isEmpty {
                let color = KLineConfig.cciColorArray[0].withAlphaComponent(0.5)
                let array = needDrawKLineModels.compactMap({$0.positionModel}).compactMap({$0.cciPoints})
                self.drawLineForReferenceWith(context: context, rect: rect, positionArray: array, color: color, width: CGFloat(1), curves: false)
            }
        } else if subViewType == .trix {
            if !KLineConfig.trixColorArray.isEmpty {
                let color = KLineConfig.trixColorArray[0].withAlphaComponent(0.5)
                let array = needDrawKLineModels.compactMap({$0.positionModel}).compactMap({$0.trixPoints})
                self.drawLineForReferenceWith(context: context, rect: rect, positionArray: array, color: color, width: CGFloat(1), curves: false)
            }
        } else if subViewType == .dmi {
            var index = 0
            
            KLineConfig.dmiColorArray.forEach({ _ in
                if KLineConfig.dmiColorArray.count > index {
                    let color = KLineConfig.dmiColorArray[index].withAlphaComponent(0.5)
                    let array = needDrawKLineModels.compactMap({$0.positionModel}).compactMap({$0.dmiPoints["\(index)"]})
                    self.drawLineForReferenceWith(context: context, rect: rect, positionArray: array, color: color, width: CGFloat(1), curves: false)
                    index += 1
                }
            })
        }
    }
    
    /// 添加参考线
    /// - Parameter model: 参考线模型
    private func addReferenceLine(model:SubReferenceLineModel){
        model.referenceList.forEach { percentage in
            let line = DottedLineView()
            line.dotDirection = .horizontal
            line.dotColor = model.dotColor
            let valueLab = UILabel().text("\(percentage)").textColor(model.labTextColor).textFont(model.font)
            let top = self.height * (1.0 - CGFloat(percentage) / 100.0)
            self.addSubview(line)
            line.snp.makeConstraints { make in
                make.left.right.equalToSuperview()
                make.height.equalTo(1.0)
                make.centerY.equalTo(top)
            }
            self.addSubview(valueLab)
          let labHeight = "\(percentage)".textHeight(font: model.font, maxWidth: 100)
          var labOffset:CGFloat = 0
          if top + labHeight / 2.0 > self.height{//底部超出
            labOffset = -(labHeight / 2.0)
          }else if top - labHeight / 2.0 < 0{//顶部超出
            labOffset = labHeight / 2.0
          }
            valueLab.snp.makeConstraints { make in
              make.centerY.equalTo(line).offset(labOffset)
                switch KLineConfig.pricePosition{
                case .left(let offset):
                    make.left.equalToSuperview().offset(offset)
                case .right(let offset):
                    make.right.equalToSuperview().offset(-offset)
                }
            }
        }
        
        let sortList = model.referenceList.sorted(by: {$0 > $1})
        guard sortList.count >= 2 else{return}
        let view = UIView()
        view.backgroundColor = model.fillColor
        self.addSubview(view)
        view.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalToSuperview().offset(self.height * (1.0 - CGFloat(sortList.first!) / 100.0))
            make.bottom.equalToSuperview().offset( -self.height * CGFloat(sortList.last! / 100.0))
        }
        
    }
    // swiftlint:disable:next all
    public func needDrawModels(_ models: [KLineModel]?) {
        self.needDrawKLineModels = models
        self.setNeedsDisplay()
    }
    
    /// 主题改变
    public func changeTheme() {
        self.setNeedsDisplay()
    }
}
