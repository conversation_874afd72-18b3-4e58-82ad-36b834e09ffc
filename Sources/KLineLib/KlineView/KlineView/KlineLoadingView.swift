//
//  FILE.swift
//  Ourbit
//
//  Created by pippen on 2024.
//

import UIKit

open class KlineLoadingView: UIView {
    public var theme: Theme = KlineLibThemeManager.currentTheme
    
    public lazy var imageView: UIImageView = {
        let v = UIImageView()
        v.backgroundColor = .clear
        v.image = "kline_loading".themeImg(KlineLibThemeManager.currentTheme)
        return v
    }()
    
    public init() {
        super.init(frame: .zero)
        initUI()
    }
    
    required public init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }
    
    public func initUI() {
        alpha = 0.9
        addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.centerY.centerX.equalToSuperview()
        }
    }

    open override func layoutSubviews() {
        super.layoutSubviews()
        self.setColorLayerHorizontal(leftColor: KLineConfig.themeManager.fillBgColor(),
                                     rightColor: KLineConfig.themeManager.bgPrimary(),
                                     frame: self.frame)
    }
    
    /// 开始动画
    public func startAnimating() {
        // 1. 创建动画
        let rotationAnim = CABasicAnimation(keyPath: "transform.rotation.z")
        // 2. 设置动画属性
        rotationAnim.fromValue = 0 // 开始角度
        rotationAnim.toValue = Double.pi * 2 // 结束角度
        rotationAnim.repeatCount = MAXFLOAT // 重复次数
        rotationAnim.duration = 1 // 一圈所需要的时间
        rotationAnim.isRemovedOnCompletion = false // 默认是true，切换到其他控制器再回来，动画效果会消失，需要设置成false，动画就不会停了
        imageView.layer.add(rotationAnim, forKey: "rotationAnimKey") // 给需要旋转的view增加动画
    }
    
    /// 停止动画
    public func stopAnimating() {
        imageView.layer.removeAnimation(forKey: "rotationAnimKey")
    }
    
    /// 主题改变
    public func changeTheme(_ theme: Theme = KlineLibThemeManager.currentTheme) {
        self.theme = theme
        imageView.image = "kline_loading".themeImg(theme)
        // swiftlint:disable:next all
        layer.sublayers?.forEach({ layer in if layer is CAGradientLayer { layer.removeFromSuperlayer() } })
        layoutSubviews()
    }
}
