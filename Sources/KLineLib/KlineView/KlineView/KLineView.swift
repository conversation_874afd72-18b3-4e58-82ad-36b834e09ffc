//
//  FILE.swift
//  Ourbit
//
//  Created by pippen on 2024.
//

import UIKit

open class KLineView: UIView {
    /// K线数据
    public var kLineModels = [KLineModel]()
    /// 需要绘制的K线数据
    // swiftlint:disable:next all
    public var needDrawKLineModels: [KLineModel]?
    /// 是否拖到中
    public var isDragging = false
    /// 点击的相对ScrollView的点位置
    public var location: CGPoint?
    /// 之前的缩放大小
    public var oldScale: CGFloat = 1.0
    /// 最大值
    public var maxValue: Double = 0.0
    /// 最小值
    public var minValue: Double = 0.0
    /// 点击回调
    public var tapedBlock: ((KLineModel) -> Void)?
    /// 隐藏点击价格回调
    public var hiddenInfoViewBlock: ((Bool) -> Void)?
    /// 上次滚动位置
    var lastDrawLeftOffset: CGFloat?
    /// 是否启用更新画线数据
    public var needUpdateDrawOffset = true
    /// 是否需要震动反馈
    public var needFeedBack = false
    /// 是否显示浮动窗口
    public var showInfoView = false
    /// 预留的柱子数量
    public let leftEmptyCount = 5
    /// 存储上一次滚动的偏移量
    public var previousContentOffset: CGFloat = 0
    
    /// 当前使用的交易时段数组
    public var currentTradingSessions: [(from: Date, to: Date)]?
    
    /// 串行控制队列
    private var p_serialQueue = DispatchQueue.serialQueue(label: "com.kline.entrust.data")
    
    public var context: KlineSymbolKlineContext?
    public weak var delegate:KlineViewDelegate?
    public weak var contextDelegate: KDCDelegate?
  
  /// 当前数据的上一个交易日最后收市价
    public var previousDayClosePrice:Double?
    /// 记录上一次坐标位置(主要用于震动反馈)
    private var lastTimePoint:CGPoint = .zero
    private var debouncer:Debouncer? = nil
    public lazy var scrollView: UIScrollView = {
        let sv = UIScrollView()
        sv.backgroundColor = .clear
        sv.showsVerticalScrollIndicator = false
        sv.showsHorizontalScrollIndicator = false
        sv.decelerationRate = UIScrollView.DecelerationRate(rawValue: 0.7)
        sv.minimumZoomScale = 1.0
        sv.maximumZoomScale = 1.0
        sv.delegate = self
        sv.bounces = false
        sv.contentInsetAdjustmentBehavior = .never
        return sv
    }()
    
    /// 简易版背景视图
    open lazy var simpleBGView: KLineSimpleBackgroundView = {
        let v = KLineSimpleBackgroundView()
        return v
    }()
    
    /// 专业版背景视图
    open lazy var professionalView: KLineProfessionalView = {
        let v = KLineProfessionalView()
        v.tapGesClickedBlock = { [weak self] in
            guard let self = self else { return }
            self.showIndicatorViews(show:false)
            self.p_resetScrollOffset(animated: true)
            self.drawKLine()
        }
        return v
    }()
    
    
    /// 加载中视图
    public lazy var loadingView: KlineLoadingView = {
        let v = KlineLoadingView()
        v.isHidden = true
        return v
    }()
    
    public var kLineWidth: CGFloat {
        KLineConfig.kLineGap + KLineConfig.kLineWidth
    }
    
    public lazy var longPressGesture = UILongPressGestureRecognizer(target: self,
                                                             action: #selector(longPressMethod(longGes:)))
    public lazy var pinchGesture = UIPinchGestureRecognizer(target: self,
                                                     action: #selector(pinchMethod(pinchGes:)))
    public lazy var tapGesture = UITapGestureRecognizer(target: self,
                                                 action: #selector(tapMethod(tapGes:)))
    
    public var enableGestureEvent = false {
        didSet {
            scrollView.isScrollEnabled = enableGestureEvent
            pinchGesture.isEnabled = enableGestureEvent
        }
    }
    
    public var showDrawTool = false {
        didSet {
            longPressGesture.isEnabled = !showDrawTool
            let status: KLineDrawStatus = showDrawTool == true ? .tapShape : .none
            KLineDrawViewModel.shared.drawStatus = status
            self.showIndicatorViews(show:false)
        }
    }
    public override init(frame: CGRect) {
        super.init(frame: frame)
        self.professionalView.mainView.delegate = self
        setupUI()
        changeTheme()
        CusstomNotification.observe(self, Noti.Data.klineThemeSkinDidChanged) { [weak self] _ in
            self?.changeTheme()
        }
    }
    //MARK: Michael: 需要使用这个来初始化 视图 并且 需要传入 实现 OBKDCDelegate 的类
    public convenience init(delegate: KDCDelegate){
        self.init(frame: .zero)
        //到时候会改掉这个
//        self.context = KlineContext(delegate: delegate)
        self.contextDelegate = delegate
        self.professionalView.mainView.delegate = self
        p_resetKlineContext()
        
    }
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    deinit {
        hiddenInfoViewBlock = nil
        tapedBlock = nil
        self.destoryKLineDrawShape()
        CusstomNotification.remove(self, Noti.Data.klineThemeSkinDidChanged)
    }
    
    /// 设置视图
    open func setupUI() {
        // 产品定义k线暂不用支持RTL
        semanticContentAttribute = .forceLeftToRight
        scrollView.semanticContentAttribute = .forceLeftToRight
        
        
        addSubview(scrollView)
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 长按手势
        scrollView.addGestureRecognizer(longPressGesture)
        
        // 点击
        scrollView.addGestureRecognizer(tapGesture)
        
        // 缩放手势
        scrollView.addGestureRecognizer(pinchGesture)
        
        scrollView.addSubview(simpleBGView)
        simpleBGView.snp.makeConstraints { make in
            make.top.leading.bottom.equalToSuperview()
            make.width.equalToSuperview()
            make.height.equalToSuperview()
        }
        
        scrollView.addSubview(professionalView)
        professionalView.snp.makeConstraints { make in
            make.top.leading.bottom.equalToSuperview()
            make.width.equalToSuperview()
            make.height.equalToSuperview()
        }
        
        if KLineConfig.kLineType == .simple {
            simpleBGView.isHidden = false
            professionalView.isHidden = true
        } else {
            simpleBGView.isHidden = true
            professionalView.isHidden = false
        }
        
        addSubview(loadingView)
        loadingView.snp.makeConstraints { make in
            make.top.leading.bottom.equalToSuperview()
            make.width.equalTo(52)
        }
        updateUI()
        
        self.setUpKLineDrawShape()
    }
    
    /// 更新视图
    open func updateUI() {
        if KLineConfig.kLineType == .professional {
            simpleBGView.isHidden = true
            professionalView.isHidden = false
            professionalView.updateUI()
        } else {
            simpleBGView.isHidden = false
            professionalView.isHidden = true
            simpleBGView.changeTheme()
        }
        
        p_resetScrollOffset(animated: false)
        drawKLine()
        reloadKLineIndicator()
    }
    public func reloadKLineIndicator(){
        self.context?.reloadKLineIndicator()
    }
    @objc func longPressMethod(longGes: UILongPressGestureRecognizer) {
        if .changed == longGes.state || .began == longGes.state {
            let point = longGes.location(in: scrollView)
            
            if abs(location?.x ?? 0.0 - point.x) < (KLineConfig.kLineWidth + KLineConfig.kLineGap) / 2 {
                return
            }
            // 暂停滑动
            scrollView.isScrollEnabled = false
            needFeedBack = true
            updateInfoViewUI(location: point)
        }
        
        if longGes.state == .ended {
            // 恢复scrollView的滑动
            scrollView.isScrollEnabled = true
            needFeedBack = false
            self.showIndicatorViews(show:false)
          //MARK: Michael: 要求松开十字线时返回最右侧数据
          guard let needDrawKLineModels = needDrawKLineModels,let last = needDrawKLineModels.last else { return }
          self.delegate?.currentSelectModelWith(klineView: self, klineModel: last)
          professionalView.updateTopData(last)
            /*
            if KLineConfig.kLineType == .simple {
                TimerTool.addTimeObserver(target: self, duration: 2) { [weak self] _ in
                    guard let self = self else { return }
                    TimerTool.removeTimeObserver(target: self)
                    self.showIndicatorViews(show:false)
                }
            }
             */
        }
    }
    
    @objc func tapMethod(tapGes: UITapGestureRecognizer) {
        let drawViewModel = KLineDrawViewModel.shared
        if KLineConfig.kLineType == .professional, drawViewModel.hiddenDraw == false {
            // 专业K线才能绘图
            if drawViewModel.drawStatus == .drawShape {
                if drawViewModel.isDrawShape == true {
                    // 如果当前绘制中，提示不安全点
                    drawViewModel.showErrorPointTips()
                } else {
                    drawViewModel.clearSelectDrawModel()
                }
                return
            } else {
                let klinePoiton = tapGes.location(in: professionalView)
                if drawViewModel.drawStatus == .tapShape,
                   let model = drawViewModel.checkContainsPoint(klinePoiton) {
                    // 当前选中了图形
                    drawViewModel.selectDrawModel(model)
                    return
                }
            }
        }
        
        // 如果当前显示画线功能，不显示popView
        if showDrawTool == false ,KLineConfig.clickToDisplayInfoView {
            needFeedBack = true
            let location = tapGes.location(in: scrollView)
            
            if showInfoView == true {
                showIndicatorViews(show:false)
            } else {
                updateInfoViewUI(location: location)
            }
        }
        
        if KLineConfig.kLineType == .simple {
            TimerTool.addTimeObserver(target: self, duration: 2) { [weak self] _ in
                guard let self = self else { return }
                TimerTool.removeTimeObserver(target: self)
                self.showIndicatorViews(show:false)
            }
        }
    }
    
    @objc func pinchMethod(pinchGes: UIPinchGestureRecognizer) {
        if pinchGes.state == .began || pinchGes.state == .changed {
            needUpdateDrawOffset = false
        }
        let difValue = pinchGes.scale - oldScale
        if abs(difValue) < KLineConfig.kLineScaleBound {
            needUpdateDrawOffset = true
            p_updateDrawCount()
            return
        }
        let oldKLineWidth = KLineConfig.kLineWidth
        let value = (difValue > 0.0 ? (KLineConfig.kLineScaleFactor) : (-KLineConfig.kLineScaleFactor))
      let newKLineWidth = oldKLineWidth + CGFloat(14.0) * value
        
        if newKLineWidth > KLineConfig.kLineMaxWidth {
            KLineConfig.kLineWidth = KLineConfig.kLineMaxWidth
            needUpdateDrawOffset = true
            p_updateDrawCount()
            return
        }
        
        if newKLineWidth < KLineConfig.kLineMinWidth {
            KLineConfig.kLineWidth = KLineConfig.kLineMinWidth
            needUpdateDrawOffset = true
            p_updateDrawCount()
            return
        }
        KLineConfig.kLineWidth = newKLineWidth
        oldScale = pinchGes.scale
        let kLineGap = KLineConfig.kLineGap
        let lastFillOffset = KLineViewModel.KLineCurrentFillStartWidth
        p_updateKLineModels(kLineModels)
          
        let fillOffset = KLineViewModel.KLineCurrentFillStartWidth
        let kLineViewWidth = p_getKLineContentWidth()
        let maxOffsetX = kLineViewWidth
        
//        self.context?.reloadKLineTimePostion(contentSize: self.bounds.size) { [weak self] _ in
//            guard let self = self else { return }
            
            let lastX = self.scrollView.contentOffset.x
            if kLineViewWidth <= self.scrollView.frame.size.width {
                self.scrollView.contentOffset = CGPoint(x: 0.0, y: self.scrollView.contentOffset.y)
                self.showIndicatorViews(show:false)
                self.updateViewWidth()
                self.drawKLine()
            } else {
                if pinchGes.numberOfTouches == 2 {
                    let pointX = self.scrollView.contentOffset.x - lastFillOffset
                    let oldLeftArrowCount = pointX / (oldKLineWidth + kLineGap)
                    var offsetX = oldLeftArrowCount * (newKLineWidth + kLineGap) + fillOffset
                    
                    offsetX = max(offsetX, 0)
                    offsetX = min(offsetX, maxOffsetX)
                  self.scrollView.contentOffset = CGPoint(x: offsetX +  CGFloat(needDrawKLineModels!.count) / 2.0 * CGFloat(14.0) * value, y: self.scrollView.contentOffset.y)
                }
            }
            
            // 如果没有触发滚动事件，手动更新柱子
            if self.scrollView.contentOffset.x == lastX {
                self.showIndicatorViews(show:false)
                self.updateViewWidth()
                self.drawKLine()
            }
            
            if pinchGes.state == .ended {
                self.needUpdateDrawOffset = true
                self.p_updateDrawCount()
            }
//        }
         
    }
    
    public func getExactXPositionWithOriginXPosition(_ originXPosition: CGFloat) -> (CGPoint?, KLineModel?) {
        guard let needDrawKLineModels = needDrawKLineModels,
              !needDrawKLineModels.isEmpty else { return (nil, nil) }
        
        let xPositoinInMainView = originXPosition - scrollView.contentOffset.x
        
        for kLineModel in needDrawKLineModels {
            guard let positionModel = kLineModel.positionModel else { continue }
            let minX = positionModel.highPoint.x - kLineWidth
            let maxX = positionModel.highPoint.x + kLineWidth
            
            if xPositoinInMainView > minX && xPositoinInMainView < maxX {
                return (kLineModel.positionModel?.closePoint, kLineModel)
            }
        }
        return (nil, nil)
    }
    
    
    public func setUpKLineDrawShape() {
        CusstomNotification.observe(self, KLineDrawModeDidChangedNotification) { [weak self] notify in
            guard let self = self else { return }
            guard let drawStatus = notify.object as? KLineDrawStatus else { return }
            self.enableGestureEvent = drawStatus != .drawShape
        }
    }
    
    public func destoryKLineDrawShape() {
        CusstomNotification.remove(self, KLineDrawModeDidChangedNotification)
    }
}
extension KLineView {
    /// 更新视图宽度位置
    public func updateViewWidth() {
        let kLineViewWidth = p_getKLineContentWidth()
        
        if KLineConfig.kLineType == .simple {
            simpleBGView.snp.updateConstraints { make in
                make.leading.equalTo(scrollView.contentOffset.x)
            }
        } else {
            professionalView.snp.updateConstraints { make in
                make.leading.equalTo(scrollView.contentOffset.x)
            }
        }
        layoutIfNeeded()
        // 更新scrollview的contentsize
        scrollView.contentSize = CGSize(width: kLineViewWidth, height: scrollView.contentSize.height)
    }
    /// 更新点击展示的视图的UI
    public func updateInfoViewUI(location: CGPoint) {
        
        let (point, kLineModel) = getExactXPositionWithOriginXPosition(location.x)
        if kLineModel != nil {
            self.location = location
            
        }
        guard let needDrawKLineModels = needDrawKLineModels,
              let last = needDrawKLineModels.last,
              let position = last.positionModel else{
            showIndicatorViews(show: false, point: .zero)
            return}
        let model:KLineModel = kLineModel ?? last
        
        let tempX = point?.x ?? position.closePoint.x
        var tempY = location.y
        if location.y < CGFloat(0){
            tempY = 0
        }else if location.y >= self.height{
            tempY = self.height
        }
        let newPoint = CGPoint(x: tempX, y: tempY)
        //MARK: Michael: 记录需要隐藏的数组范围
        var needHiddenYList:[(start:CGFloat,end:CGFloat)] = []
        //MARK: Michael: 按顺序储存当前所有显示的副视图的 显示范围
        var subViewPosition:[(start:CGFloat,end:CGFloat)] = []
        //MARK: Michael: 当前所有显示的副视图
        let subTypesCount = KLineConfig.subViewType.count
        //MARK: Michael: 当前副视图高度
        let subViewHeight = KLineConfig.kLineSubViewHeight
        //MARK: Michael: 每个副视图的头部视图高度
        let subViewHeaderHeight = KLineConfig.subVeiwHeaderHeight
        //MARK: Michael: 时间轴高度
        let timeHeight = KLineConfig.kLineTimeViewHeight
        //MARK: Michael: 主视图高度
        let mainViewHeight = professionalView.mainView.bottom
        
        //MARK: Michael: 当前时间轴显示位置
        switch KLineConfig.kLineTimeViewWithBottomView{
        case .mainViewBottom:
            needHiddenYList.append((mainViewHeight,mainViewHeight + timeHeight))
        case .subViewBottom:
            needHiddenYList.append((self.height - timeHeight,self.height))
        }
        
        
        //MARK: Michael: 基准高度为主视图高度
        var baseY = mainViewHeight
        switch KLineConfig.kLineTimeViewWithBottomView{
        case .mainViewBottom:
            baseY += timeHeight
        case .subViewBottom:
            break
        }
        for i in 0..<subTypesCount{
            let start = baseY + CGFloat(i) * (subViewHeaderHeight + subViewHeight)
            let end = start + subViewHeaderHeight
            //MARK: Michael: 将所有副视图Header加到隐藏数组中
            needHiddenYList.append((start,end))
            //MARK: Michael: 将所有副视图显示范围加到数组中
            let subViewStart = end
            let subViewEnd = end + subViewHeight
            subViewPosition.append((subViewStart,subViewEnd))
        }
        //MARK: Michael: 发现Vol的位置在 KLineConfig.subViewType 中不一定是 第一位 但是 subViewPosition 一直是放在第一位的 需要在这里交换下
        if let index = KLineConfig.subViewType.firstIndex(where: {$0 == .vol}){
            subViewPosition.safeSwap(0, index)
        }
        
        var show = true
        //MARK: Michael: 判断是否显示十字及info view logic
        if let _ = needHiddenYList.first(where: { (start,end) in
            return newPoint.y >= start && newPoint.y <= end
        }){
            show = false
        }
        showIndicatorViews(show: true,hiddenHorizontalLine: !show, point: newPoint)
        //MARK: Michael: 仅需要显示十字 虚线和info view 时 计算Y轴显示的内容
        guard show else{return}
        //MARK: Michael: 开启震动
        if needFeedBack && lastTimePoint.x != newPoint.x {
            systemFeedbackGenerator()
        }
        lastTimePoint = newPoint
        //MARK: Michael: 显示水平虚线文字内容logic
        var xPrice = "0.0"
        var positionType:KLineSubViewType? = nil
        
        if let index = subViewPosition.firstIndex(where: { (start,end) in
            return newPoint.y >= start && newPoint.y <= end
        }){
            positionType = KLineConfig.subViewType[index]
        }
        
        switch newPoint.y{
        case 0...mainViewHeight:
            let minY = KLineConfig.kLineMainViewMinY
            let maxY = professionalView.mainView.size.height - KLineConfig.kLineMainViewMaxY
            var unitValue = 1.0
            
            
            if maxY != minY && maxValue != minValue {
                unitValue = (maxValue - minValue) / Double((maxY - minY))
            }
            xPrice = "\((maxY - newPoint.y - 1.0) * unitValue + minValue)"
        default:
            guard let positionType = positionType else{return}
            
            xPrice = calculateCurrentLocationSubTypeValue(point: newPoint,
                                                          subViewPosition:subViewPosition,
                                                          subType: positionType,
                                                          klineModel: model)
        }
        
            tapedBlock?(model)
            professionalView.updatePopData(kLineModel: model, price: xPrice)
            //向外抛出当前点击信息（主要用于更新info View ）
            self.delegate?.updatePopDataWith(klineView: self, kLineModel: model, price: xPrice)
    }
    //MARK: Michael: 主要确定当前Y 是在哪个视图
    private func calculateCurrentLocationSubTypeValue(point:CGPoint,
                                                      subViewPosition:[(start:CGFloat,end:CGFloat)],
                                                      subType:KLineSubViewType,
                                                      klineModel:KLineModel)->String{
        var value = "0.0"
        guard let needDrawKLineModels = needDrawKLineModels ,
              let index = KLineConfig.subViewType.firstIndex(where: {$0 == subType}) else{return value}
        let curretnTypePosition = subViewPosition[index]
        var tempMaxValue = 0.0
        var tempMinValue = 0.0
        let size = CGSize(width: .zero,height: KLineConfig.kLineSubViewHeight)
        
        if let references = KLineConfig.references ,let model = references.first(where: {$0.type == subType}){
            tempMaxValue = model.maxValue
            tempMinValue = model.minValue
        }else{
            switch subType {
            case .vol:
                let (_, maxValue, minValue, _) = KLineViewModel.convertToVolumePositionModels(containerSize:size,
                                                                                                       kLineModels: needDrawKLineModels)
                tempMaxValue = maxValue
                tempMinValue = minValue
            default:
                let  (_, maxValue, minValue, _) = KLineViewModel.convertToSubPositionModels(containerSize:size,
                                                                                                     kLineModels: needDrawKLineModels,
                                                                                                     type: subType)
                tempMaxValue = maxValue
                tempMinValue = minValue
            }
        }
        
        
        let minY = curretnTypePosition.start
        let maxY = curretnTypePosition.end
        var unitValue = 1.0
        
        if maxY != minY && tempMaxValue != tempMinValue {
            unitValue = (tempMaxValue - tempMinValue) / Double((maxY - minY))
        }
        value = "\((maxY - point.y - 1.0) * unitValue + tempMinValue)"
        
        
        
        return value
    }
    
    public func showIndicatorViews(show: Bool ,hiddenHorizontalLine:Bool = false,point:CGPoint = .zero) {
        if !show {
            location = nil
        }
        
        if KLineConfig.kLineType == .simple {
            simpleBGView.showIndicatorViews(show, point)
        }
        self.delegate?.showIndicatorViewsWith(klineView: self, isShow: show,horizontalStatus:hiddenHorizontalLine, point: point)
        //MARK: Michael: 自动隐藏info View logic
        if show,KLineConfig.autoHiddeninfoViewTime > 0{
            if self.debouncer == nil {
                self.debouncer = Debouncer.init(delay: KLineConfig.autoHiddeninfoViewTime)
            }
            self.debouncer!.debounce {[weak self]  in
                //MARK: Michael: 添加 scrollView.isScrollEnabled 判断 让其在长按拖动时不隐藏
                guard let self = self,scrollView.isScrollEnabled else { return }
                showInfoView = false
                self.delegate?.showIndicatorViewsWith(klineView: self, isShow: false,horizontalStatus:hiddenHorizontalLine, point: point)
                
            }
            
        }
        hiddenInfoViewBlock?(!show)
        
        showInfoView = show
    }
    //MARK: Michael: 震动反馈
    private func systemFeedbackGenerator(){
        let impactLight = UIImpactFeedbackGenerator(style: UIImpactFeedbackGenerator.FeedbackStyle.light)
        impactLight.impactOccurred()
    }
    public func getKlineViewSpace() -> Double {
        var space: Double = 0
        if let model = kLineModels.last {
            let inviteAttr: [NSAttributedString.Key: Any] = [
                NSAttributedString.Key.font: UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular)
            ]
            let priceAttri = NSAttributedString(string: "\(model.close) →", attributes: inviteAttr)
            space += priceAttri.size().width
        }
        return space
    }
    
    public func p_updateKLineModels(_ models: [KLineModel]) {
        guard !models.isEmpty else {
            kLineModels = []
            KLineViewModel.KLineCurrentFillStartWidth = 0
            return
        }
        kLineModels = models
        context?.kLineList = models
        calculationPreviousDayModel()
        context?.reloadKLineTimePostion(contentSize:self.bounds.size){ [weak self] _ in
            guard let self = self else { return }
            self.showIndicatorViews(show:false)
            self.updateViewWidth()
            self.drawKLine()
        }
        // 计算需要填充的宽度
        let w = min(scrollView.width - kLineWidth * CGFloat(leftEmptyCount), 0)
        KLineViewModel.KLineCurrentFillStartWidth = w + kLineWidth / 2.0 //  + kLineWidth / 2.0 的目的是为了完整显示第一条数据
    }
    private func p_resetKlineContext() {
        context?.destoryContext()
        context = KlineSymbolKlineContext(queue: p_serialQueue)
        context?.delegate = contextDelegate
    }
    
    // swiftlint:disable:next all
    public func setKLineModels(_ newModels: [KLineModel]?, _ needReDraw: Bool = false) {
        guard let models = newModels else { return }
        self.p_resetKlineContext()
        
        var needReDraw = needReDraw
        // 如果当前没有柱子，又有新柱子，一定重绘
        if kLineModels.isEmpty, !models.isEmpty {
            needReDraw = true
        }
        p_updateKLineModels(models)
        
        // 重新绘制，把参数置为0
        if needReDraw == true { lastDrawLeftOffset = nil }
        
//        updateViewWidth()
        
        let space: Double = getKlineViewSpace()
        // 需要绘制的最小的数量，减去空隙后能展示的数量
        let minCount = Int((scrollView.frame.size.width - space) / kLineWidth) + 2
        var drawCount = minCount
        if let needDrawKLineModels = needDrawKLineModels, needReDraw == false {
            drawCount = needDrawKLineModels.count
        }
        
        if needReDraw && kLineModels.isEmpty {
            professionalView.clearData()
            showIndicatorViews(show:false)
        }
        
        // 绘制的数组和最小数组不相等，就重新绘制
        if needReDraw && drawCount <= minCount {
            // 设置contentOffset
            let kLineViewWidth = p_getKLineContentWidth()
            let fill = KLineViewModel.KLineCurrentFillStartWidth
            let offset = kLineViewWidth - scrollView.frame.size.width - fill + space
            
            if offset > 0 {
                scrollView.contentOffset = CGPoint(x: offset, y: 0.0)
            } else {
                scrollView.contentOffset = CGPoint.zero
            }
            // 更新保持绘制柱子数量
            p_updateDrawCount()
            
            showIndicatorViews(show:false)
            loadingView.isHidden = true
            loadingView.stopAnimating()
        } else {
            // 反向更新offset
            if let offset = lastDrawLeftOffset {
                p_resetScrollOffset(left: offset, animated: false)
            }
            
            
            guard let location = location else { return }
            updateInfoViewUI(location: location)
        }
        
        asyncOnMain {[weak self]  in
            guard let self = self else { return }
            self.updateUI()
        }
    }
    
    
    public func updateLastTimeKLineModels(models: [KLineModel]) {
        guard Thread.isMainThread else {
            DispatchQueue.main.async { self.updateLastTimeKLineModels(models: models) }
            return
        }
        var updatedModels = self.kLineModels
        var latestUpdatedModel: KLineModel? = nil

        // 遍历传入的模型，根据timestamp判断是更新还是新增
        for model in models {
            // 设置动画状态
            let updatedModel = model
            updatedModel.needAnimation = false
            updatedModel.animationProgress = 0
            
            // 查找是否存在相同timestamp的模型
            if let existingIndex = updatedModels.firstIndex(where: { $0.timestamp == model.timestamp }) {
                // 更新现有模型
                updatedModels[existingIndex] = updatedModel
            } else {
                // 新增模型
                updatedModels.append(updatedModel)
            }
            
            // 记录最后一个处理的模型，用于更新UI
            latestUpdatedModel = updatedModel
        }
        
        // 更新模型数据
        kLineModels = updatedModels
        context?.kLineList = updatedModels
        
        // 计算当前可视区域的数组
        let contentW = scrollView.contentSize.width - scrollView.width
        let size = professionalView.mainView.size
        guard let needDrawModels = KLineViewModel.extractNeedDrawModels(size,
                                                                      scrollView.contentOffset.x,
                                                                      contentW,
                                                                      lastDrawLeftOffset,
                                                                      kLineModels) else {
            return
        }
        
        let needUpdate = needDrawModels.filter({$0.needAnimation == true})
        // 当实时更新的这条数据不在可视范围内再更新最新价的相关数据
        if needUpdate.count == 0, let latestModel = latestUpdatedModel {
            let (_, maxValue, minValue, scale) = KLineViewModel.convertToKLinePositionModels(containerSize: size,
                                                                                           kLineModels: needDrawModels)
            professionalView.updateLatestPriceUI(maxValue, minValue, scale, "\(latestModel.close)", previousDayClosePrice: self.previousDayClosePrice)
        }
        
        // 异步计算指标并更新UI
        p_serialQueue.async {
            KLineIndicatorTools.calculationIndicator(models: updatedModels)
            asyncOnMain{[weak self] in
                guard let self = self else { return }
                self.drawKLine()
            }
        }
    }
    
    /**
     1.更新所有指标 ✅
     2.更新主视图柱子高度 ✅
     3.更新所有副视图柱子及线条✅
     
     */
  public func updateLastTimeKLineModels(lastTimeModel: KLineModel,isDone:Bool = false) {
    
    //self.p_resetKlineContext()
    var models = self.kLineModels
    models.forEach({$0.needAnimation = false})
    models.removeLast()
    //MARK: Michael: 重点将needAnimation 设为 true 并且将 animationProgress 置为0
    lastTimeModel.needAnimation = !isDone
    lastTimeModel.animationProgress = 0
    models.append(lastTimeModel)
//    p_updateKLineModels(models)
    kLineModels = models
    context?.kLineList = models
    //计算当前可视区域的数组
    let contentW = scrollView.contentSize.width - scrollView.width
    let size = professionalView.mainView.size
    guard let needDrawModels = KLineViewModel.extractNeedDrawModels(size,
                                                                      scrollView.contentOffset.x,
                                                                      contentW,
                                                                      lastDrawLeftOffset,
                                                                      kLineModels) else {
        return
    }
    let needUpdate = needDrawModels.filter({$0.needAnimation == true})
    //MARK: Michael: 当实时更新的这条数据不在可视范围内再更新最新价的相关数据
    if needUpdate.count == 0{
      let (_, maxValue, minValue, scale) = KLineViewModel.convertToKLinePositionModels(containerSize: size,
                                                                                            kLineModels: needDrawModels)
      professionalView.updateLatestPriceUI(maxValue, minValue, scale, "\(lastTimeModel.close)",previousDayClosePrice: self.previousDayClosePrice)
    }
    
    
    p_serialQueue.async {
      KLineIndicatorTools.calculationIndicator(models: models)
      asyncOnMain{[weak self]  in
        guard let self = self else { return }
//        self.updateUI()
        self.drawKLine()
      }
    }
    
    
//    self.reloadKLineIndicator()
    
//    asyncOnMain {[weak self]  in
//        guard let self = self else { return }
//        self.updateUI()
//    }
    
  }
    
    /// 添加最新的k线数据,自动滚动到最新一条数据
    /// - Parameters:
    ///   - appendModels: 需要追加的K线数据 数组
    public func addKlineData(appendModels: [KLineModel]) {
        var tempList = self.kLineModels
        tempList.append(contentsOf: appendModels)
        tempList.sort(by: {$0.timestamp < $1.timestamp})
        p_updateKLineModels(tempList)
        updateUI()
    }
    
    /// 向前添加k线数据
    /// - Parameters:
    ///   - beforeModels: 之前数据的数组
    ///   - needScrollerToStart: 是否需要滚动到开始位置 默认 不需要
    public func addBeforeKlineData(beforeModels: [KLineModel],needScrollerToStart:Bool = false) {
        var tempList = self.kLineModels
        tempList.append(contentsOf: beforeModels)
        tempList.sort(by: {$0.timestamp < $1.timestamp})
        p_updateKLineModels(tempList)
        updateViewWidth()
        
        if needScrollerToStart{
            scrollView.contentOffset = .zero
        }else{
            var offsetX = scrollView.contentOffset.x
            offsetX += kLineWidth * CGFloat(beforeModels.count)
            scrollView.contentOffset = CGPoint(x: offsetX, y: 0)
        }
        
        drawKLine()
        reloadKLineIndicator()
    }
  
  /// 获取上一个交易日的最后收市价
  private func calculationPreviousDayModel(){
    //MARK: Michael: 如果开启了最新价颜色随前一个交易日收市价变化则传入前一个交易日数据
    if KLineConfig.latestPriceColorWithPreviousDay{
      self.previousDayClosePrice = self.delegate?.closingPriceOfThePreviousDay()
    }
  }
    public func drawKLine() {
        if scrollView.contentSize.width - (lastDrawLeftOffset ?? 0) < 0 {
            lastDrawLeftOffset = nil
        }
        
        let contentW = scrollView.contentSize.width - scrollView.width
        if KLineConfig.kLineType == .simple {
            var size = simpleBGView.simpleView.size
            size.height -= KLineConfig.kLineTimeViewHeight
            guard let needDrawModels = KLineViewModel.extractNeedDrawModels(size,
                                                                              scrollView.contentOffset.x,
                                                                              contentW,
                                                                              lastDrawLeftOffset,
                                                                              kLineModels) else {
                needDrawKLineModels = []
                simpleBGView.needDrawModels([])
                return
            }
            let (models, maxValue, minValue, scale) = KLineViewModel.convertToKLinePositionModels(containerSize: size,
                                                                                                  kLineModels: needDrawModels)
            kLineView(maxValue, minValue, scale)
            needDrawKLineModels = models
            simpleBGView.needDrawModels(needDrawKLineModels)
        } else {
            let size = professionalView.mainView.size
            guard let needDrawModels = KLineViewModel.extractNeedDrawModels(size,
                                                                              scrollView.contentOffset.x,
                                                                              contentW,
                                                                              lastDrawLeftOffset,
                                                                              kLineModels) else {
                needDrawKLineModels = []
                professionalView.needDrawModels([])
                return
            }
            var (models, maxValue, minValue, scale) = KLineViewModel.convertToKLinePositionModels(containerSize: size,
                                                                                                  kLineModels: needDrawModels)
            kLineView(maxValue, minValue, scale)
        
            var j = 0
            KLineConfig.subViewType.forEachIndex { type, _ in
                let _ :KLineSubViewType = type
              
                if professionalView.subBgViews.count > j {
                    let subBgView = professionalView.subBgViews[j]
                    
                    let size = subBgView.size
                    if type == .vol{
                        (models, maxValue, minValue, scale) = KLineViewModel.convertToVolumePositionModels(containerSize: size,
                                                                                                           kLineModels: models)
                        subBgView.subRightView.updateData(maxValue, minValue, scale)
                    }else{
                        (models, maxValue, minValue, scale) = KLineViewModel.convertToSubPositionModels(containerSize:size,
                                                                                                        kLineModels: models,
                                                                                                        type: type)
                        subBgView.subRightView.updateData(maxValue, minValue, scale)
                    }
                    
                }
                    j += 1
            }
            
            needDrawKLineModels = models
            professionalView.needDrawModels(needDrawKLineModels)
            guard let model = needDrawKLineModels?.last else { return }
            self.delegate?.currentSelectModelWith(klineView: self, klineModel: model)
            professionalView.updateTopData(model)
            guard let model = needDrawKLineModels?.first else { return }
            self.delegate?.leftmostModelWith(klineView: self, klineModel: model)
        }
    }
    
    public func kLineView(_ max: Double, _ min: Double, _ scale: Int) {
      //guard max != maxValue || min != minValue else{return}
        maxValue = max
        minValue = min
        guard let model = kLineModels.last else { return }
      professionalView.updateLatestPriceUI(maxValue, minValue, scale, "\(model.close)",previousDayClosePrice: self.previousDayClosePrice)
    }
    
    /// 主题改变
    public func changeTheme() {
        let theme = KlineLibThemeManager.currentTheme
        backgroundColor = KLineConfig.themeManager.bgCard()
        loadingView.changeTheme(theme)
        if KLineConfig.kLineType == .simple {
            simpleBGView.changeTheme(theme)
        } else {
            professionalView.changeTheme(theme)
        }
    }
}

extension KLineView: UIScrollViewDelegate,TrottleProtocol {
    open func scrollViewDidScroll(_ scrollView: UIScrollView) {
        guard !kLineModels.isEmpty else { return }
        if isDragging {
            showIndicatorViews(show:false)
            lastDrawLeftOffset = nil
        }
        
        updateViewWidth()
        drawKLine()
        
        if scrollView.contentOffset.x < previousContentOffset {
            var leftWidth = kLineWidth * (CGFloat)(leftEmptyCount)
            leftWidth = scrollView.width - leftWidth
            if scrollView.contentOffset.x <= leftWidth {
                
                self.throttle(TimeInterval(1)) {[weak self]  in
                    guard let self = self else { return }
                    self.delegate?.scrollToStartPosition()
                }
            }
        }
//        let x = scrollView.contentOffset.x.remainder(dividingBy: scrollView.bounds.size.width / 5.0)
//        let tempX = x >= 0 ? x : scrollView.bounds.size.width / 5.0 + x
        
        // 更新上一次滚动的偏移量
        previousContentOffset = scrollView.contentOffset.x
    }
    
    open func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
        isDragging = true
    }
    
    open func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        if !decelerate {
            if isDragging { p_updateDrawCount() }
            isDragging = false
        }
    }
    
    open func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        if isDragging { p_updateDrawCount() }
        isDragging = false
    }
    
    open func scrollViewDidEndScrollingAnimation(_ scrollView: UIScrollView) {
        p_updateDrawCount()
    }
}

extension KLineView {
    /// 更新上次需要保留的绘制数量
    private func p_updateDrawCount() {
        guard needUpdateDrawOffset,
              let drawModels = needDrawKLineModels,
              !drawModels.isEmpty else { return }
        if drawModels.last == kLineModels.last {
            lastDrawLeftOffset = scrollView.contentSize.width - scrollView.contentOffset.x - scrollView.width
        } else {
            lastDrawLeftOffset = nil
        }
    }
    
    /// 调整视图滚动区域
    private func p_resetScrollOffset(left: CGFloat? = nil, animated: Bool) {
        var kLineViewWidth: CGFloat = 0
        
        // 计算滚动的区域
        if let v = left {
            kLineViewWidth = scrollView.contentSize.width - v
        } else {
            // 设置contentOffset
            let fill = KLineViewModel.KLineCurrentFillStartWidth
            kLineViewWidth = CGFloat(self.kLineModels.count) * kLineWidth + fill
            // 滚动到最右边
            let space: Double = getKlineViewSpace()
            kLineViewWidth += space
        }
        let offset = max(0, kLineViewWidth - scrollView.frame.size.width)
        scrollView.setContentOffset(CGPoint(x: offset, y: 0), animated: animated)
        if !animated { p_updateDrawCount() }
    }
    
    /// 获取k线滚动的Content
    private func p_getKLineContentWidth() -> CGFloat {
        let width = kLineWidth
        let space = scrollView.width - CGFloat(leftEmptyCount) * width
        let fill = KLineViewModel.KLineCurrentFillStartWidth
        
        let kLineViewWidth = CGFloat(kLineModels.count) * width + space + fill
        return max(kLineViewWidth, scrollView.width)
    }
}
extension KLineView:KLineMainViewDelegate{
  public func animationComplet() {
    if let last = self.kLineModels.last{
      updateLastTimeKLineModels(lastTimeModel: last, isDone: true)
    }
  }
}
