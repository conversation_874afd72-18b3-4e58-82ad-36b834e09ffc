//
//  KlineDelegate.swift
//  UseKlineToolsDemo
//
//  Created by <PERSON> on 2025/4/8.
//

import Foundation
public protocol KlineViewDelegate:NSObjectProtocol{
    /// 用于通知外部当前K线滚动到起点了 用于需要加载之前的数据的通知
    func scrollToStartPosition()
    
    /// 当前选中的KlineModel 里面包含所以的指标值
    /// - Parameters:
    ///   - klineView: -
    ///   - klineModel: 当前选中的KlineModel
    func currentSelectModelWith(klineView:KLineView,klineModel:KLineModel)
    
    
    /// 最左側的 KLineModel
    /// - Parameters:
    ///   - klineView: -
    ///   - klineModel: -
    func leftmostModelWith(klineView:KLineView,klineModel:KLineModel)
    
    
    /// 更新info View 中的信息
    /// - Parameters:
    ///   - klineView: -
    ///   - kLineModel: 当前选中的klineModel
    ///   - price: 当前十字线对应的y轴值
    func updatePopDataWith(klineView:KLineView,kLineModel: KLineModel, price: String)
    
    
    /// 更新当前infoView是否显示 及 当前点击的point
    /// - Parameters:
    ///   - klineView: -
    ///   - isShow: 是否应该显示
    ///   - horizontalStatus: 是否隐藏水平线
    ///   - point: 当前点击的point
    func showIndicatorViewsWith(klineView:KLineView, isShow: Bool,horizontalStatus:Bool, point: CGPoint?)
  
    /// 前一日收市价
    /// - Returns: -
    func closingPriceOfThePreviousDay()->Double?
}
