//
//  OBKLineViewModel.swift
//  KLineDemo
//
//  Created by <PERSON> on 2025/3/19.
//

import Foundation
import UIKit
public typealias KLineInvokeValues = (kLineModels: [KLineModel], maxValue: Double, minValue: Double, scale: Int)
open class KLineViewModel{
    /// 用于记录当前开始填充的宽度
    public static var KLineCurrentFillStartWidth: CGFloat = 0
    /// 用户记录当前绘制的时候，开始偏移，通过ScrollView偏移计算
    public static var KLineStartFillOffset: CGFloat = 0
   
    /// 提取需要绘制的数组
    public static func extractNeedDrawModels(_ size:CGSize,
                                      _ x:CGFloat,
                                      _ width:CGFloat,
                                      _ offset:CGFloat? = 0,
                                      _ kLineModels: [KLineModel])->[KLineModel]?{
        var x = x
        if let value = offset {
            x = width - value
        }
        if KLineConfig.displayAllData{
            return kLineModels
        }else{        
            return p_extractNeedDrawModels(size, x, kLineModels)
        }
    }
    
    /// 提取需要绘制的数组
    // swiftlint:disable:next all
    private static func p_extractNeedDrawModels(_ size: CGSize,
                                               _ scrollViewOffsetX: CGFloat,
                                               _ kLineModels: [KLineModel]) -> [KLineModel]? {
        guard !kLineModels.isEmpty else { return nil }
        
        let lineGap = KLineConfig.kLineGap
        let lineWidth = KLineConfig.kLineWidth
        var needDrawKLineCount = (Int)(ceil((size.width) / (lineGap + lineWidth)))
        var needDrawKLineStartIndex: Int = 0
        // 除去填充后的偏移
        let fakeOffsetX = scrollViewOffsetX - KLineCurrentFillStartWidth
        
        if fakeOffsetX > 0 {
            needDrawKLineStartIndex = Int(fakeOffsetX / (lineGap + lineWidth))
        } else {
            needDrawKLineStartIndex = 0
            needDrawKLineCount = max(Int((size.width + fakeOffsetX) / (lineGap + lineWidth)), 0)
        }
        
        // 计算开始的位置
        if fakeOffsetX > 0 {
            KLineStartFillOffset = 0
            let offset = CGFloat(needDrawKLineStartIndex) * (lineGap + lineWidth) + lineGap - fakeOffsetX
            KLineStartFillOffset += offset
        } else {
            KLineStartFillOffset = abs(fakeOffsetX)
        }
        
        var needDrawKLineModels = [KLineModel]()
        // 防止数组越界
        if needDrawKLineStartIndex >= 0, needDrawKLineStartIndex < kLineModels.count {
            if needDrawKLineStartIndex + needDrawKLineCount < kLineModels.count {
                // 截取部分需要绘制的K线数据
                let endIndex = needDrawKLineStartIndex + needDrawKLineCount
                needDrawKLineModels = Array(kLineModels[needDrawKLineStartIndex...endIndex])
            } else {
                // 截取部分需要绘制的K线数据
                let endIndex = kLineModels.count - 1
                needDrawKLineModels = Array(kLineModels[needDrawKLineStartIndex...endIndex])
            }
        }
        return needDrawKLineModels
    }
    /// 将model转化为Position模型
    open class func convertToKLinePositionModels(containerSize:CGSize,
                                                 kLineModels: [KLineModel]) -> KLineInvokeValues {
        guard kLineModels.first != nil else { return (kLineModels, 0.0, 0.0, 0) }
        let isPro = KLineConfig.kLineType == .professional
        var minValue = Double.greatestFiniteMagnitude
        var maxValue: Double = 0.0
      var  allValue:[Double] = []
      let high = kLineModels.map({$0.high})
      let low = kLineModels.map({$0.low})
      let open = kLineModels.map({$0.open})
      let close = kLineModels.map({$0.close})
      
      //MARK: Michael: 应该不需要再管order price 暂时这里先留着 需要的话直接打开即可
      //let order = kLineModels.map({$0.orderModels}).compactMap({$0}).flatMap({$0}).map({$0.price})
      //allValue.append(contentsOf: order)
      
      allValue.append(contentsOf: close)
      
      if isPro {
        allValue.append(contentsOf: high)
        allValue.append(contentsOf: low)
        allValue.append(contentsOf: open)
        

        var mainValueList:[Double] = []
        KLineConfig.mainViewType.forEach { type in
          switch type {
          case .ma:
            let list =  kLineModels.map({$0.maDictionary}).map({$0.values}).flatMap({$0}).compactMap({$0.asDouble()})
            mainValueList.append(contentsOf: list)
          case .ema:
            let list =  kLineModels.map({$0.emaDictionary}).map({$0.values}).flatMap({$0}).compactMap({$0.asDouble()})
            mainValueList.append(contentsOf: list)
          case .wma:
            let list =  kLineModels.map({$0.wmaDictionary}).map({$0.values}).flatMap({$0}).compactMap({$0.asDouble()})
            mainValueList.append(contentsOf: list)
          case .boll:
            let mb = kLineModels.compactMap({$0.mb}).compactMap({$0.asDouble()})
            let up = kLineModels.compactMap({$0.up}).compactMap({$0.asDouble()})
            let dn = kLineModels.compactMap({$0.dn}).compactMap({$0.asDouble()})
            
            mainValueList.append(contentsOf: mb)
            mainValueList.append(contentsOf: up)
            mainValueList.append(contentsOf: dn)
          case .close:
            break
          case .sar:
            let list =  kLineModels.map({$0.sar}).compactMap({$0}).compactMap({$0.asDouble()})
            mainValueList.append(contentsOf: list)
          case .vwap:
            let list =  kLineModels.map({$0.averagePrice}).compactMap({$0}).filter({$0 != 0})
            mainValueList.append(contentsOf: list)
          }
        }
        
        allValue.append(contentsOf: mainValueList)
      }
      let resultAllValues = allValue.compactMap({$0})
      if let max = resultAllValues.max(),let min = resultAllValues.min(){
        maxValue = max
        minValue = min
      }
        var minY = KLineConfig.kLineMainViewMinY
        var maxY = containerSize.height - KLineConfig.kLineMainViewMaxY
        var unitValue = 1.0
      
//      //MARK: Michael: 当前可视区域中的最高价 有买卖信息时 导致买卖点绘制会被遮挡
//        let order = kLineModels.filter({$0.orderModels != nil})
//        if let hight = order.map({$0.high}).max() ,maxValue - 6 <= hight{
//          //maxValue += maxValue * 0.01
//        }
//      //MARK: Michael: 暂时先这么处理吧 后面再想想更好的方案
//        if KLineConfig.type == .timeLine {
//          minY += 9.0
//          maxY -= 9.0
//        }
        
        if maxY != minY && maxValue != minValue && maxY != minY {
            unitValue = (maxValue - minValue) / Double((maxY - minY))
        }
        let lineGap = KLineConfig.kLineGap
        let lineWidth = KLineConfig.kLineWidth
        //左侧需空出的空间
        var tempIndex = 0
        //MARK: Michael: 当当前为五日分时时 计算需要空出的左侧空间
        if KLineConfig.type == .fiveDayLine ,let estimate = KLineConfig.expectedTotalDataCount{
            //一天的数据条数
            let oneDayCount = estimate / 5
            //当前数据大概占几天
            let currentDays = ceil(Double(kLineModels.count) / Double(oneDayCount))
            //左侧需空出的空间
            tempIndex = (5 - Int(currentDays)) * oneDayCount
        }
        
        var idx = 0
        var scale = 14
        
        let templineWidth = calculateLineWidth(oldlineWidth: lineWidth, contentWidth: containerSize.width, count: kLineModels.count)
        
        for kLineModel in kLineModels {
            var xPosition:CGFloat = .zero
            if KLineConfig.displayAllData{
                if let expectedCount = KLineConfig.expectedTotalDataCount {
                    if expectedCount < kLineModels.count{
                        xPosition = CGFloat(idx + tempIndex) *  containerSize.width / CGFloat(kLineModels.count)
                    }else{
                        xPosition = CGFloat(idx + tempIndex) *  containerSize.width / CGFloat(expectedCount)
                    }
                }else{
                    xPosition = CGFloat(idx + tempIndex) *  containerSize.width / CGFloat(kLineModels.count)
                }
                xPosition += templineWidth
            }else{
                xPosition = self.startXPosition() + CGFloat(idx + tempIndex) * (lineGap + lineWidth)
            }
            var openPoint = CGPoint(x: xPosition,
                                    y: (maxY - CGFloat((kLineModel.open  - minValue) / unitValue)))
            var closePointY = (maxY - CGFloat((kLineModel.close  - minValue) / unitValue))
            scale = kLineModel.priceScale
            if scale < 0 { scale = 2 }
            
            // 防止出现柱子特别细，看不清问题
            if abs(closePointY - openPoint.y) < KLineConfig.kLineMinWidth {
                if openPoint.y > closePointY {
                    openPoint.y = closePointY + KLineConfig.kLineMinWidth
                } else if openPoint.y < closePointY {
                    closePointY = openPoint.y + KLineConfig.kLineMinWidth
                } else {
                    if idx > 0 {
                        let preKLineModel = kLineModels[idx - 1]
                        
                        if kLineModel.open  > preKLineModel.close  {
                            openPoint.y = closePointY + KLineConfig.kLineMinWidth
                        } else {
                            closePointY = openPoint.y + KLineConfig.kLineMinWidth
                        }
                    } else if idx + 1 < kLineModels.count {
                        // idx==0即第一个时
                        let subKLineModel = kLineModels[idx + 1]
                        
                        if kLineModel.close  < subKLineModel.open  {
                            openPoint.y = closePointY + KLineConfig.kLineMinWidth
                        } else {
                            closePointY = openPoint.y + KLineConfig.kLineMinWidth
                        }
                    }
                }
            }
            let closePoint = CGPoint(x: xPosition, y: closePointY)
            let highPoint = CGPoint(x: xPosition,
                                    y: (maxY - CGFloat((kLineModel.high  - minValue) / unitValue)))
            let lowPoint = CGPoint(x: xPosition,
                                   y: (maxY - CGFloat((kLineModel.low  - minValue) / unitValue)))
            let averagePoint = CGPoint(x: xPosition,
                                       y: (maxY - CGFloat((kLineModel.averagePrice  - minValue) / unitValue)))
            let kLinePositionModel = KLinePositionModel()
            kLinePositionModel.openPoint = openPoint
            kLinePositionModel.closePoint = closePoint
            kLinePositionModel.highPoint = highPoint
            kLinePositionModel.lowPoint = lowPoint
            kLinePositionModel.averagePoint = averagePoint
          var color = KLineConfig.themeManager.candleRiseColor()
          
          if kLineModel.open  > kLineModel.close{
            color = KLineConfig.themeManager.candleRiseColor()
          }else if kLineModel.open  < kLineModel.close{
            color = KLineConfig.themeManager.candleDeclineColor()
          }else{
            color = calculationColorWithPreviousClosePriceWith(currentModel: kLineModel, allModels: kLineModels)
          }
            kLinePositionModel.color = color
            
            for type in KLineConfig.mainViewType {
                if type == .ma {
                    kLineModel.maDictionary.forEach { (key: String, value: String) in
                        if !value.isEmpty {
                            let maPoint = CGPoint(x: xPosition,
                                                  y: (maxY - CGFloat((value.doubleValue()  - minValue) / unitValue)))
                            kLinePositionModel.maPoints[key] = maPoint
                        }
                    }
                } else if type == .ema {
                    kLineModel.emaDictionary.forEach { (key: String, value: String) in
                        if !value.isEmpty {
                            let emaPoint = CGPoint(x: xPosition,
                                                   y: (maxY - CGFloat((value.doubleValue()  - minValue) / unitValue)))
                            kLinePositionModel.emaPoints[key] = emaPoint
                        }
                    }
                } else if type == .wma {
                    kLineModel.wmaDictionary.forEach { (key: String, value: String) in
                        if !value.isEmpty {
                            let emaPoint = CGPoint(x: xPosition,
                                                   y: (maxY - CGFloat((value.doubleValue()  - minValue) / unitValue)))
                            kLinePositionModel.wmaPoints[key] = emaPoint
                        }
                    }
                } else if type == .boll {
                    if let mb = kLineModel.mb, !mb.isEmpty {
                        let point = CGPoint(x: xPosition,
                                            y: (maxY - CGFloat((mb.doubleValue()  - minValue) / unitValue)))
                        kLinePositionModel.bollPoints["BOLL_MB"] = point
                    }
                    
                    if let up = kLineModel.up, !up.isEmpty {
                        let point = CGPoint(x: xPosition,
                                            y: (maxY - CGFloat((up.doubleValue()  - minValue) / unitValue)))
                        kLinePositionModel.bollPoints["BOLL_UP"] = point
                    }
                    
                    if let dn = kLineModel.dn, !dn.isEmpty {
                        let point = CGPoint(x: xPosition,
                                            y: (maxY - CGFloat((dn.doubleValue()  - minValue) / unitValue)))
                        kLinePositionModel.bollPoints["BOLL_DN"] = point
                    }
                } else if type == .sar, let sar = kLineModel.sar {
                    let point = CGPoint(x: xPosition,
                                        y: (maxY - CGFloat((sar.doubleValue() - minValue) / unitValue)))
                    kLinePositionModel.sarPoints = point
                    kLinePositionModel.sarUp = (sar.doubleValue() >= kLineModel.close )
                }
            }
            kLineModel.positionModel = kLinePositionModel
            if let orderModels = kLineModel.orderModels{
                orderModels.forEach { order in
                    let y = (maxY - CGFloat((order.price  - minValue) / unitValue))
                    order.point = CGPoint(x: xPosition, y: y)
                }
                
            }
            idx += 1
        }
        
        if minValue == Double.greatestFiniteMagnitude { return (kLineModels, 0.0, 0.0, 0) }
        return (kLineModels, maxValue, minValue, scale)
    }
    /// 转换量的位置
    public static func convertToVolumePositionModels(containerSize: CGSize,
                                                     kLineModels: [KLineModel]) -> KLineInvokeValues {
        guard kLineModels.first != nil else { return (kLineModels, 0.0, 0.0, 0) }
        var minValue = Double.greatestFiniteMagnitude
        var maxValue: Double = 0.0
      var allValues:[Double] = []
      let volume = kLineModels.map({$0.volume})
      let list =  kLineModels.map({$0.volMADictionary}).compactMap({$0.values}).flatMap({$0}).map({$0.doubleValue()})
      allValues.append(contentsOf: volume)
      allValues.append(contentsOf: list)
      if let max = allValues.max(),let min = allValues.min(){
        maxValue = max
        minValue = min
      }
        
        let minY: CGFloat = 0
        let maxY: CGFloat = containerSize.height
        var unitValue: CGFloat = 1.0
        if let references = KLineConfig.references ,let model = references.first(where: {$0.type == .vol}){
          maxValue = model.maxValue
          minValue = model.minValue
        }
        if maxY != minY && maxValue != minValue {
            unitValue = CGFloat(maxValue - minValue) / (maxY - minY)
        }
        let lineGap = KLineConfig.kLineGap
        let lineWidth = KLineConfig.kLineWidth
        var idx = 0
        var scale = 14
        
        for kLineModel in kLineModels {
            scale = Int(kLineModel.quantityScale ?? 0.0)
            if scale < 0{ scale = 2 }
            
            var xPosition = self.startXPosition() + CGFloat(idx) * (lineGap + lineWidth)
            if let postion = kLineModel.positionModel {
                xPosition = postion.openPoint.x
            }
            var yPosition = abs(maxY - CGFloat(kLineModel.volume - minValue) / unitValue)
            
            if abs(yPosition - maxY) < 0.5 {
                yPosition = maxY - 1.0
            }
            let startPoint = CGPoint(x: xPosition, y: yPosition)
            let endPoint = CGPoint(x: xPosition, y: maxY)
            
            if let kLinePositionModel = kLineModel.positionModel {
                kLineModel.volMADictionary.forEach { (key: String, value: String) in
                    if !value.isEmpty {
                        let maPoint = CGPoint(x: xPosition,
                                              y: (maxY - CGFloat(CGFloat(value.doubleValue() - minValue) / unitValue)))
                        kLinePositionModel.volMAPoints[key] = maPoint
                    }
                }
                kLinePositionModel.volPoints = [startPoint, endPoint]
            }
            idx += 1
        }
        if minValue == Double.greatestFiniteMagnitude { return (kLineModels, 0.0, 0.0, 0) }
        return (kLineModels, maxValue, minValue, scale)
    }
    /// 转换副图的位置
    public static func convertToSubPositionModels(containerSize: CGSize,
                                                  kLineModels: [KLineModel],
                                                  type: KLineSubViewType) -> KLineInvokeValues {
        guard kLineModels.first != nil else { return (kLineModels, 0.0, 0.0, 0) }
        var minValue = Double.greatestFiniteMagnitude
        var maxValue: Double = 0.0
        
      var allValues:[Double] = []
      switch type {
      case .vol:
        let volume = kLineModels.map({$0.volume})
        let list =  kLineModels.map({$0.volMADictionary}).compactMap({$0.values}).flatMap({$0}).map({$0.doubleValue()})
        allValues.append(contentsOf: volume)
        allValues.append(contentsOf: list)
      case .macd:
        let dif =  kLineModels.compactMap({$0.dif}).filter({$0 != 0})
        let dea =  kLineModels.compactMap({$0.dea}).filter({$0 != 0})
        let macd =  kLineModels.compactMap({$0.macd}).filter({$0 != 0})
        allValues.append(contentsOf: dif)
        allValues.append(contentsOf: dea)
        allValues.append(contentsOf: macd)
      case .kdj:
        let k =  kLineModels.compactMap({$0.k}).filter({$0 != 0})
        let d =  kLineModels.compactMap({$0.d}).filter({$0 != 0})
        let j =  kLineModels.compactMap({$0.j}).filter({$0 != 0})
        allValues.append(contentsOf: k)
        allValues.append(contentsOf: d)
        allValues.append(contentsOf: j)
      case .rsi:
        let list =  kLineModels.map({$0.rsiDictionary}).compactMap({$0.values}).flatMap({$0}).map({$0.doubleValue()})
        allValues.append(contentsOf: list)
      case .wr:
        let list =  kLineModels.map({$0.wrDictionary}).compactMap({$0.values}).flatMap({$0}).map({$0.doubleValue()})
        allValues.append(contentsOf: list)
      case .obv:
        let list =  kLineModels.map({$0.obvDictionary}).compactMap({$0.values}).flatMap({$0})
        allValues.append(contentsOf: list)
      case .roc:
        let list =  kLineModels.map({$0.rocDictionary}).compactMap({$0.values}).flatMap({$0})
        allValues.append(contentsOf: list)
      case .cci:
        let cci =  kLineModels.compactMap({$0.cci})
        allValues.append(contentsOf: cci)
      case .stochRSI:
        let list =  kLineModels.map({$0.stochRSIDictionary}).compactMap({$0.values}).flatMap({$0})
        allValues.append(contentsOf: list)
      case .trix:
        let trix =  kLineModels.compactMap({$0.trix})
        allValues.append(contentsOf: trix)
      case .dmi:
        let list =  kLineModels.map({$0.dmiDictionary}).compactMap({$0.values}).flatMap({$0})
        allValues.append(contentsOf: list)
      case .close:
        break
      }
      
      if let max = allValues.max(),let min = allValues.min(){
        maxValue = max
        minValue = min
      }
      
      
        /*
        for kLineModel in kLineModels {
            if type == .macd {
                if !(kLineModel.dif == 0.0) {
                    if kLineModel.dif > maxValue {
                        maxValue = kLineModel.dif
                    }
                    
                    if kLineModel.dif < minValue {
                        minValue = kLineModel.dif
                    }
                }
                
                if !(kLineModel.dea == 0.0) {
                    if kLineModel.dea > maxValue {
                        maxValue = kLineModel.dea
                    }
                    
                    if kLineModel.dea < minValue {
                        minValue = kLineModel.dea
                    }
                }
                
                if !(kLineModel.macd == 0.0) {
                    if kLineModel.macd > maxValue {
                        maxValue = kLineModel.macd
                    }
                    
                    if kLineModel.macd < minValue {
                        minValue = kLineModel.macd
                    }
                }
            } else if type == .kdj {
                if !(kLineModel.k == 0.0) {
                    if kLineModel.k > maxValue {
                        maxValue = kLineModel.k
                    }
                    
                    if kLineModel.k < minValue {
                        minValue = kLineModel.k
                    }
                }
                
                if !(kLineModel.d == 0.0) {
                    if kLineModel.d > maxValue {
                        maxValue = kLineModel.d
                    }
                    
                    if kLineModel.d < minValue {
                        minValue = kLineModel.d
                    }
                }
                
                if !(kLineModel.j == 0.0) {
                    if kLineModel.j > maxValue {
                        maxValue = kLineModel.j
                    }
                    
                    if kLineModel.j < minValue {
                        minValue = kLineModel.j
                    }
                }
            } else if type == .rsi {
                KLineConfig.rsiArray.forEach { number in
                    let value: String = kLineModel.rsiDictionary[String(number)] ?? ""
                    
                    if !value.isEmpty {
                        if value.doubleValue() > maxValue {
                            maxValue = value.doubleValue()
                        }
                        
                        if value.doubleValue() < minValue {
                            minValue = value.doubleValue()
                        }
                    }
                }
            } else if type == .wr {
                KLineConfig.wrArray.forEach { number in
                    let value: String = kLineModel.wrDictionary[String(number)] ?? ""
                    
                    if !value.isEmpty {
                        if value.doubleValue() > maxValue {
                            maxValue = value.doubleValue()
                        }
                        
                        if value.doubleValue() < minValue {
                            minValue = value.doubleValue()
                        }
                    }
                }
            } else if type == .roc {
                kLineModel.rocDictionary.forEach { (_, value: Double) in
                    maxValue = max(value, maxValue)
                    minValue = min(value, minValue)
                }
            } else if type == .cci, let cci = kLineModel.cci {
                maxValue = max(cci, maxValue)
                minValue = min(cci, minValue)
            } else if type == .obv {
                kLineModel.obvDictionary.forEach { (_, value: Double) in
                    maxValue = max(value, maxValue)
                    minValue = min(value, minValue)
                }
            } else if type == .stochRSI {
                kLineModel.stochRSIDictionary.forEach { (_, value: Double) in
                    maxValue = max(value, maxValue)
                    minValue = min(value, minValue)
                }
            } else if type == .trix, let v = kLineModel.trix {
                maxValue = max(v, maxValue)
                minValue = min(v, minValue)
            } else if type == .dmi {
                kLineModel.dmiDictionary.forEach { (_, value: Double) in
                    maxValue = max(value, maxValue)
                    minValue = min(value, minValue)
                }
            }
        }
        */
        let minY: CGFloat = 0
        let maxY: CGFloat = containerSize.height
        var unitValue: CGFloat = 1.0
        
        if let references = KLineConfig.references ,let model = references.first(where: {$0.type == type}){
          maxValue = model.maxValue
          minValue = model.minValue
        }
      
        if maxY != minY && maxValue != minValue {
            unitValue = CGFloat(maxValue - minValue) / (maxY - minY)
        }
        let lineGap = KLineConfig.kLineGap
        let lineWidth = KLineConfig.kLineWidth
        var idx = 0
        var scale = 14
        
        for kLineModel in kLineModels {
            scale = kLineModel.priceScale
            if scale < 0 { scale = 2 }
            
            if let kLinePositionModel = kLineModel.positionModel {
                var xPosition = self.startXPosition() + CGFloat(idx) * (lineGap + lineWidth)
                if let postion = kLineModel.positionModel {
                    xPosition = postion.openPoint.x
                }
                
                if type == .macd {
                    if !(kLineModel.macd == 0.0) {
                        let yPosition = (maxY - CGFloat(kLineModel.macd - minValue) / unitValue)
                        let startPoint = CGPoint(x: xPosition, y: yPosition)
                        let endPoint = CGPoint(x: xPosition, y: (maxY + CGFloat(minValue) / unitValue))
                        kLinePositionModel.macdPoints = [startPoint, endPoint]
                    }
                    
                    if !(kLineModel.dif == 0.0) {
                        let point = CGPoint(x: xPosition,
                                            y: (maxY - CGFloat((kLineModel.dif - minValue)) / unitValue))
                        kLinePositionModel.difPoint = point
                    }
                    
                    if !(kLineModel.dea == 0.0) {
                        let point = CGPoint(x: xPosition,
                                            y: (maxY - CGFloat((kLineModel.dea - minValue)) / unitValue))
                        kLinePositionModel.deaPoint = point
                    }
                } else if type == .kdj {
                    if !(kLineModel.k == 0.0) {
                        let point = CGPoint(x: xPosition,
                                            y: (maxY - CGFloat((kLineModel.k - minValue)) / unitValue))
                        kLinePositionModel.kdjPoints["KDJ_K"] = point
                    }
                    
                    if !(kLineModel.d == 0.0) {
                        let point = CGPoint(x: xPosition,
                                            y: (maxY - CGFloat((kLineModel.d - minValue)) / unitValue))
                        kLinePositionModel.kdjPoints["KDJ_D"] = point
                    }
                    
                    if !(kLineModel.j == 0.0) {
                        let point = CGPoint(x: xPosition,
                                            y: (maxY - CGFloat((kLineModel.j - minValue)) / unitValue))
                        kLinePositionModel.kdjPoints["KDJ_J"] = point
                    }
                } else if type == .rsi {
                    kLineModel.rsiDictionary.forEach { (key: String, value: String) in
                        if !value.isEmpty {
                            let point = CGPoint(x: xPosition,
                                                y: (maxY - CGFloat(CGFloat(value.doubleValue() - minValue) / unitValue)))
                            kLinePositionModel.rsiPoints[key] = point
                        }
                    }
                } else if type == .wr {
                    kLineModel.wrDictionary.forEach { (key: String, value: String) in
                        if !value.isEmpty {
                            let point = CGPoint(x: xPosition,
                                                y: (maxY - CGFloat(CGFloat(value.doubleValue() - minValue) / unitValue)))
                            kLinePositionModel.wrPoints[key] = point
                        }
                    }
                } else if type == .roc {
                    kLineModel.rocDictionary.forEach { (key: String, value: Double) in
                        let point = CGPoint(x: xPosition,
                                            y: (maxY - CGFloat(CGFloat(value - minValue) / unitValue)))
                        kLinePositionModel.rocPoints[key] = point
                    }
                } else if type == .stochRSI {
                    kLineModel.stochRSIDictionary.forEach { (key: String, value: Double) in
                        let point = CGPoint(x: xPosition,
                                            y: (maxY - CGFloat(CGFloat(value - minValue) / unitValue)))
                        kLinePositionModel.stochRSIPoints[key] = point
                    }
                } else if type == .cci, let cci = kLineModel.cci {
                    let point = CGPoint(x: xPosition,
                                        y: (maxY - CGFloat(CGFloat(cci - minValue) / unitValue)))
                    kLinePositionModel.cciPoints = point
                } else if type == .obv {
                    kLineModel.obvDictionary.forEach { (key: String, value: Double) in
                        let point = CGPoint(x: xPosition,
                                            y: (maxY - CGFloat(CGFloat(value - minValue) / unitValue)))
                        kLinePositionModel.obvPoints[key] = point
                    }
                } else if type == .trix, let v = kLineModel.trix {
                    let point = CGPoint(x: xPosition,
                                        y: (maxY - CGFloat(CGFloat(v - minValue) / unitValue)))
                    kLinePositionModel.trixPoints = point
                } else if type == .dmi {
                    kLineModel.dmiDictionary.forEach { (key: String, value: Double) in
                        let point = CGPoint(x: xPosition,
                                            y: (maxY - CGFloat(CGFloat(value - minValue) / unitValue)))
                        kLinePositionModel.dmiPoints[key] = point
                    }
                }
            }
            idx += 1
        }
        if minValue == Double.greatestFiniteMagnitude { return (kLineModels, 0.0, 0.0, 0) }
        return (kLineModels, maxValue, minValue, scale)
    }
  // 将数据值转换为视图Y坐标
  public static func yPosition(value: CGFloat,maxValue:CGFloat,minValue:CGFloat,viewHeight:CGFloat) -> CGFloat {
       guard maxValue > minValue else { return viewHeight / 2 } // 防止除以零
       
       let normalizedValue = (value - minValue) / (maxValue - minValue)
       return viewHeight * (1 - normalizedValue) // iOS坐标系Y轴向下，所以用1减
   }
   
  // 将视图Y坐标转换回数据值
  public static func value(yPosition: CGFloat,maxValue:CGFloat,minValue:CGFloat,viewHeight:CGFloat) -> CGFloat {
    guard viewHeight > 0 else { return minValue }
    
    let normalizedY = 1 - (yPosition / viewHeight)
    return minValue + normalizedY * (maxValue - minValue)
  }
    /// 开始的点
    public static func startXPosition() -> CGFloat {
        let x = KLineStartFillOffset
//        if KLineConfig.type == .kline {
//            return KLineConfig.kLineWidth / 2.0 + x
//        }
//        if KLineConfig.type == .timeLine {
//            return 0
//        }
        return x
    }
  private static func calculationColorWithPreviousClosePriceWith(currentModel:KLineModel,allModels:[KLineModel])->UIColor{
    var color = KLineConfig.themeManager.candleDeclineColor()
    guard let currentIndex = allModels.firstIndex(where: {$0 == currentModel}),currentIndex >= 1 else { return color }
    let targtModel = allModels[currentIndex - 1]
    guard let positionModel = targtModel.positionModel else { return color }
    if currentModel.close == targtModel.close{
      color = positionModel.color
    }else if currentModel.close > targtModel.close{
      color = KLineConfig.themeManager.candleDeclineColor()
    }else{
      color = KLineConfig.themeManager.candleRiseColor()
    }
    return color
  }
}
