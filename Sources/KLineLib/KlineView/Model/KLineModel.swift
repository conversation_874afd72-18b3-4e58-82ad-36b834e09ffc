//
//  OBKLineModel.swift
//  UseKlineToolsDemo
//
//  Created by <PERSON> on 2025/3/31.
//

import Foundation
/// K线数据模型
/// 这里使用Class  是因为会有方法在其他地方修改内部的值
open class KLineModel:Equatable {
    public static func == (lhs: KLineModel, rhs: KLineModel) -> Bool {
        lhs.timestamp == rhs.timestamp &&
        lhs.close == rhs.close
    }
    
    /// 时间戳
    public var timestamp: TimeInterval = 0
    /// 开盘价
    public var open: Double = 0
    /// 最高价
    public var high: Double = 0
    /// 最低价
    public var low: Double = 0
    /// 收盘价
    public var close: Double = 0
    /// 成交量
    public var volume: Double = 0
    /// 成交额
    public var amount: Double = 0
    /// 涨跌幅
    public var changeRate: Double = 0
    /// 涨跌额
    public var changeAmount: Double = 0
  
    /// 均价
    public var averagePrice:Double = 0.0
    /// 价格精度
    public var priceScale: Int = 3
    public var positionModel: KLinePositionModel?
    //
    public var sumValue:Double = 0
    public var sumVolValue:Double = 0
    public var ema1:Double = 0
    public var ema2:Double = 0
    // MARK: - MA 指标数据
    public var maDictionary:[String:String] = [:]
    public var emaDictionary:[String:String] = [:]
    public var wmaDictionary:[String:String] = [:]
    // MARK: - MACD 指标数据
    public var dif: Double = 0.0
    public var dea: Double = 0.0
    public var macd: Double = 0.0
    
    // MARK: - KDJ 指标数据
    public var k: Double = 0.0
    public var d: Double = 0.0
    public var j: Double = 0.0
    
    // MARK: - RSI 指标数据
    public var rsiDictionary:[String:String] = [:]
    
    // MARK: - BOLL 指标数据
    public var boll: Double?
    public var ub: Double?
    public var lb: Double?
    
    public var isShowTime:Bool = false
    
    
    public var wrDictionary:[String:String] = [:]
    public var rocDictionary:[String:Double] = [:]
    public var stochRSIDictionary:[String:Double] = [:]
    public var cci:Double? = nil
    public var obvDictionary:[String:Double] = [:]
    public var trix:Double? = nil
    public var dmiDictionary:[String:Double] = [:]
    public var volMADictionary:[String:String] = [:]
    
    /// 成交量精度？
    public var quantityScale:Double? = 3.0
    public var mb:String?
    public var up:String?
    public var dn:String?
    public var sar:String?  = nil
    
    public var orderModels:[KlineOrderModel]? = nil
  
  //MARK: Michael: 动画相关 Start
    //绘制当前point 是否需要动画(仅处理open 和close 的动画)
    public var needAnimation:Bool = false
    //绘制进度
    public var animationProgress:CGFloat = 0
  
  //MARK: Michael: 动画相关 End
    public init() {}
    
    
    public func isFirstKline()->Bool{
        return false
    }
    public func riseDownAmount()->Double{ //好像与合约相关
        return 0.0
    }
    public func dispalyRiseDownAmount()->String{//好像与合约相关
        return "方法未实现"
    }
    public func displayRiseDownRate()->String{//好像与合约相关
        return "方法未实现"
    }
    public func rangeAmount()->String{//好像与合约相关
        return "方法未实现"
    }
  public func mapToTempKLineModel()->KLineModelStruct{
    var model = KLineModelStruct()
    model.timestamp = self.timestamp
    model.open = self.open
    model.high = self.high
    model.low = self.low
    model.close = self.close
    model.targetClose = self.close
    model.volume = self.volume
    model.amount = self.amount
    model.changeRate = self.changeRate
    model.changeAmount = self.changeAmount
    model.averagePrice = self.averagePrice
    model.priceScale = self.priceScale
    //MARK: Michael: 需要将需要动画数据的位置设置为nil  否则会先闪到targetClose 在动画到targetClose
    if self.needAnimation{
      model.positionModel = nil
    }else{
      model.positionModel = self.positionModel
    }
    
    model.sumValue = self.sumValue
    model.sumVolValue = self.sumVolValue
    model.ema1 = self.ema1
    model.ema2 = self.ema2
    // MARK: - MA 指标数据
    model.maDictionary = self.maDictionary
    model.emaDictionary = self.emaDictionary
    model.wmaDictionary = self.wmaDictionary
    // MARK: - MACD 指标数据
    model.dif = self.dif
    model.dea = self.dea
    model.macd = self.macd
    
    // MARK: - KDJ 指标数据
    model.k = self.k
    model.d = self.d
    model.j = self.j
    
    // MARK: - RSI 指标数据
    model.rsiDictionary = self.rsiDictionary
    
    // MARK: - BOLL 指标数据
    model.boll = self.boll
    model.ub = self.ub
    model.lb = self.lb
    
    model.isShowTime = self.isShowTime
    
    
    model.wrDictionary = self.wrDictionary
    model.rocDictionary = self.rocDictionary
    model.stochRSIDictionary = self.stochRSIDictionary
    model.cci = self.cci
    model.obvDictionary = self.obvDictionary
    model.trix = self.trix
    model.dmiDictionary = self.dmiDictionary
    model.volMADictionary = self.volMADictionary
    
    /// 成交量精度？
    model.quantityScale = self.quantityScale
    model.mb = self.mb
    model.up = self.up
    model.dn = self.dn
    model.sar = self.sar
    
    model.orderModels = self.orderModels
  
  //MARK: Michael: 动画相关 Start
    //绘制当前point 是否需要动画(仅处理open 和close 的动画)
    model.needAnimation = self.needAnimation
    //绘制进度
    model.animationProgress = self.animationProgress
    return model
  }
}

//MARK: Michael: 建个这个模型的目的是为了 主视图中的最后一条数据的动画效果
public struct KLineModelStruct:Equatable {
    public static func == (lhs: KLineModelStruct, rhs: KLineModelStruct) -> Bool {
        lhs.timestamp == rhs.timestamp &&
        lhs.close == rhs.close
    }
    
    /// 时间戳
    public var timestamp: TimeInterval = 0
    /// 开盘价
    public var open: Double = 0
    /// 最高价
    public var high: Double = 0
    /// 最低价
    public var low: Double = 0
    /// 收盘价
    public var close: Double = 0
    /// 成交量
    public var volume: Double = 0
    /// 成交额
    public var amount: Double = 0
    /// 涨跌幅
    public var changeRate: Double = 0
    /// 涨跌额
    public var changeAmount: Double = 0
    /// 均价
    public var averagePrice:Double = 0.0
    /// 价格精度
    public var priceScale: Int = 3
    public var positionModel: KLinePositionModel?
    //
    public var sumValue:Double = 0
    public var sumVolValue:Double = 0
    public var ema1:Double = 0
    public var ema2:Double = 0
    // MARK: - MA 指标数据
    public var maDictionary:[String:String] = [:]
    public var emaDictionary:[String:String] = [:]
    public var wmaDictionary:[String:String] = [:]
    // MARK: - MACD 指标数据
    public var dif: Double = 0.0
    public var dea: Double = 0.0
    public var macd: Double = 0.0
    
    // MARK: - KDJ 指标数据
    public var k: Double = 0.0
    public var d: Double = 0.0
    public var j: Double = 0.0
    
    // MARK: - RSI 指标数据
    public var rsiDictionary:[String:String] = [:]
    
    // MARK: - BOLL 指标数据
    public var boll: Double?
    public var ub: Double?
    public var lb: Double?
    
    public var isShowTime:Bool = false
    
    
    public var wrDictionary:[String:String] = [:]
    public var rocDictionary:[String:Double] = [:]
    public var stochRSIDictionary:[String:Double] = [:]
    public var cci:Double? = nil
    public var obvDictionary:[String:Double] = [:]
    public var trix:Double? = nil
    public var dmiDictionary:[String:Double] = [:]
    public var volMADictionary:[String:String] = [:]
    
    /// 成交量精度？
    public var quantityScale:Double? = 3.0
    public var mb:String?
    public var up:String?
    public var dn:String?
    public var sar:String?  = nil
    
    public var orderModels:[KlineOrderModel]? = nil
  
  //MARK: Michael: 动画相关 Start
    //绘制当前point 是否需要动画(仅处理open 和close 的动画)
    public var needAnimation:Bool = false
    //绘制进度
    public var animationProgress:CGFloat = 0
  /// 此属性为最后一条数据的动画效果服务 平时因=close
    public var targetClose:Double = 0
  //MARK: Michael: 动画相关 End
    public init() {}
}
/*
public protocol KlineModelProtocol{
  /// 时间戳
  var timestamp: TimeInterval {get set}
  /// 开盘价
  var open: Double {get set}
  /// 最高价
  var high: Double {get set}
  /// 最低价
  var low: Double {get set}
  /// 收盘价
  var close: Double {get set}
  /// 成交量
  var volume: Double {get set}
  /// 成交额
  var amount: Double {get set}
  /// 涨跌幅
  var changeRate: Double {get set}
  /// 涨跌额
  var changeAmount: Double {get set}
  /// 价格精度
  var priceScale: Int {get set}
  var positionModel: KLinePositionModel? {get set}
  //
  var sumValue:Double {get set}
  var sumVolValue:Double {get set}
  var ema1:Double {get set}
  var ema2:Double {get set}
  // MARK: - MA 指标数据
  var maDictionary:[String:String] {get set}
  var emaDictionary:[String:String] {get set}
  var wmaDictionary:[String:String] {get set}
  // MARK: - MACD 指标数据
  var dif: Double {get set}
  var dea: Double {get set}
  var macd: Double {get set}
  
  // MARK: - KDJ 指标数据
  var k: Double {get set}
  var d: Double {get set}
  var j: Double {get set}
  
  // MARK: - RSI 指标数据
  var rsiDictionary:[String:String] {get set}
  
  // MARK: - BOLL 指标数据
  var boll: Double? {get set}
  var ub: Double? {get set}
  var lb: Double? {get set}
  
  var isShowTime:Bool {get set}
  
  
  var wrDictionary:[String:String] {get set}
  var rocDictionary:[String:Double] {get set}
  var stochRSIDictionary:[String:Double] {get set}
  var cci:Double? {get set}
  var obvDictionary:[String:Double] {get set}
  var trix:Double? {get set}
  var dmiDictionary:[String:Double] {get set}
  var volMADictionary:[String:String] {get set}
  
  /// 成交量精度？
  var quantityScale:Double? {get set}
  var mb:String? {get set}
  var up:String? {get set}
  var dn:String? {get set}
  var sar:String? {get set}
  
  var orderModels:[KlineOrderModel]? {get set}

//MARK: Michael: 动画相关 Start
  //绘制当前point 是否需要动画(仅处理open 和close 的动画)
  var needAnimation:Bool {get set}
  //绘制进度
  var animationProgress:CGFloat {get set}
/// 此属性为最后一条数据的动画效果服务 平时应=close
  var targetClose:Double {get set}

//MARK: Michael: 动画相关 End
  
  
  func isFirstKline()->Bool
  func riseDownAmount()->Double
  func dispalyRiseDownAmount()->String
  func displayRiseDownRate()->String
  func rangeAmount()->String
}
extension KlineModelProtocol{
  public func isFirstKline()->Bool{
      return false
  }
  public func riseDownAmount()->Double{ //好像与合约相关
      return 0.0
  }
  public func dispalyRiseDownAmount()->String{//好像与合约相关
      return "方法未实现"
  }
  public func displayRiseDownRate()->String{//好像与合约相关
      return "方法未实现"
  }
  public func rangeAmount()->String{//好像与合约相关
      return "方法未实现"
  }
}
*/
