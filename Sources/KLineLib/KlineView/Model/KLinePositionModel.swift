//
//  OBKLinePositionModel.swift
//  KLineDemo
//
//  Created by <PERSON> on 2025/3/19.
//

import Foundation
import UIKit
public class KLinePositionModel{
    public var openPoint:CGPoint = .zero
    public var closePoint:CGPoint = .zero
    public var highPoint:CGPoint = .zero
    public var lowPoint:CGPoint = .zero
    public var color:UIColor = .clear
    public var sarPoints:CGPoint? = .zero
    public var difPoint:CGPoint? = .zero
    public var deaPoint:CGPoint? = .zero
    public var cciPoints:CGPoint? = .zero
    public var trixPoints:CGPoint? = .zero
    public var sarUp:Bool = false
    public var maPoints:[String:CGPoint] = [:]
    public var emaPoints:[String:CGPoint] = [:]
    public var wmaPoints:[String:CGPoint] = [:]
    public var bollPoints:[String:CGPoint] = [:]
    public var volMAPoints:[String:CGPoint] = [:]
    public var kdjPoints:[String:CGPoint] = [:]
    public var rsiPoints:[String:CGPoint] = [:]
    public var wrPoints:[String:CGPoint] = [:]
    public var rocPoints:[String:CGPoint] = [:]
    public var stochRSIPoints:[String:CGPoint] = [:]
    public var obvPoints:[String:CGPoint] = [:]
    public var dmiPoints:[String:CGPoint] = [:]
    public var volPoints:[CGPoint] = []
    public var macdPoints:[CGPoint]? = []
    //MARK: Michael: 均价点
    public var averagePoint:CGPoint = .zero
    public init(){
        
    }
    
}
