//
//  FILE.swift
//  Ourbit
//
//  Created by pippen on 2024.
//

import UIKit

#if canImport(SwiftUI)
import SwiftUI

public struct KLineGesturePopSwiftUIView: UIViewRepresentable {

    // MARK: - Input Data Bindings / Properties
    // These properties will be provided by the parent SwiftUI view.
    // When they change, `updateUIView` will be called.

    /// The data model for the specific point on the K-line.
    /// Optional because there might be no specific point selected.
    var kLineModel: KLineModel?

    /// The price string corresponding to the kLineModel.
    /// Optional for the same reason as kLineModel.
    var price: String?

    /// Whether the pop view (crosshair indicators and info) should be visible.
    var isShowing: Bool

    /// The CGPoint where the crosshair intersection should be.
    /// Optional because it's only relevant when isShowing is true.
    var position: CGPoint?

    
    public init(
        kLineModel: KLineModel? = nil,
        price: String? = nil,
        isShowing: Bool,
        position: CGPoint? = nil
    ) {
        self.kLineModel = kLineModel
        self.price = price
        self.isShowing = isShowing
        self.position = position
    }

    // MARK: - UIViewRepresentable Methods

    /// Creates the underlying KLineGesturePopView instance.
    /// This is called only once when the view is first created.
    public func makeUIView(context: Context) -> KLineGesturePopView {
        let popView = KLineGesturePopView()
        // Initial setup (like setting default hidden state) is handled
        // within KLineGesturePopView's init and setupUI.
        // The first call to updateUIView will set the correct initial state.
        debugPrint("Making KLineGesturePopView") // For debugging
        return popView
    }

    /// Updates the KLineGesturePopView when the state changes in SwiftUI.
    /// This method is called whenever any of the @State/@Binding properties
    /// passed into this struct change.
    public func updateUIView(_ uiView: KLineGesturePopView, context: Context) {
        debugPrint("Updating KLineGesturePopView: isShowing=\(isShowing), position=\(String(describing: position)), hasModel=\(kLineModel != nil)") // For debugging

        // 1. Update Data:
        // Only call updateData if we have valid model and price information.
        // This matches the logic where the delegate only calls updatePopDataWith
        // when it has the necessary data.
        if let model = kLineModel, let p = price {
            uiView.updateData(kLineModel: model, price: p)
        }
        // Note: If kLineModel or price becomes nil, the existing data in the
        // KLineGesturePopView's labels will remain until updated again or the view is hidden.
        // If you need to clear the text when data is nil, you'd add an else clause here
        // or modify the KLineGesturePopView.updateData method.

        // 2. Update Visibility and Position:
        // Call the existing method that handles both showing/hiding and positioning.
        // KLineGesturePopView.showIndicatorViews already handles the logic
        // for isShow = false and point = nil.
        uiView.showIndicatorViews(isShow: isShowing, horizontalStatus: false, point: position)

        // 3. Update Theme: (Optional)
        // If the theme passed in is different from the view's current theme, update it.
        // You might need to expose the `theme` property on KLineGesturePopView
        // or compare properties if `Theme` isn't directly comparable.
        // For simplicity, we might just call it if the theme could change.
        // Assuming KLineGesturePopView has a way to check its current theme or if Theme is Equatable:
        // if uiView.theme != theme { // You might need to add 'var theme: Theme' to KLineGesturePopView
        //    uiView.changeTheme(theme)
        // }
        // Or, if theme changes are infrequent or performance impact is negligible:
        uiView.changeTheme()
    }

    // MARK: - Coordinator (Optional)
    // If KLineGesturePopView needed to communicate back to SwiftUI (e.g., handle
    // button taps within the pop view), you would implement a Coordinator class here.
    // For simply displaying data pushed *to* the view, it's not strictly necessary.
    // func makeCoordinator() -> Coordinator {
    //     Coordinator(self)
    // }
    // class Coordinator: NSObject {
    //     var parent: KLineGesturePopViewRepresentable
    //     init(_ parent: KLineGesturePopViewRepresentable) {
    //         self.parent = parent
    //     }
    //     // Add @objc methods here for target-action or delegate implementations
    // }

    // MARK: - Dismantle (Optional)
    // For cleanup when the view is removed.
    // static func dismantleUIView(_ uiView: KLineGesturePopView, coordinator: Coordinator?) {
    //    // Perform any necessary cleanup
    // }
}

#endif

open class KLineGesturePopView: UIView {
    public lazy var infoView: KLineTapInfoView = {
        let v = KLineTapInfoView()
        return v
    }()
    
    /// 价格
    public lazy var priceLabel: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 11, weight: UIFont.Weight.regular))
          .textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
          .bgColor(.clear)
          .textAlignment(.center)
        return lb
    }()
    
    /// 价格左箭头
    public lazy var priceLeftLabel: UILabel = {
        let lb = UILabel()
        lb.textFont(.systemFont(ofSize: 16))
          .textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
          .bgColor(.clear)
          .textAlignment(.center)
        return lb
    }()
    
    /// 价格右箭头
    public lazy var priceRightLabel: UILabel = {
        let lb = UILabel()
        lb.textFont(.systemFont(ofSize: 16))
          .textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
          .bgColor(.clear).textAlignment(.center)
        return lb
    }()
    
    /// 时间
    public lazy var timeLabel: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: UIFont.Weight.regular))
          .textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
          .bgColor(KLineConfig.themeManager.klineLibSubTextColor())
          .textAlignment(.center)
          .layer(radius: 2, borderWidth: 0, borderColor: .clear)
        return lb
    }()
    
    /// 横线
    public lazy var horizontalView: DottedLineView = {
        let v = DottedLineView()
        v.dotDirection = .horizontal
        return v
    }()
    
    /// 竖线
    public lazy var verticalView: DottedLineView = {
        let v = DottedLineView()
        v.dotDirection = .vertical
        return v
    }()
    
    
    public override init(frame: CGRect) {
        super.init(frame: frame)
        
        self.setupUI()
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    public func setupUI() {
        self.backgroundColor = .clear
        
        self.addSubview(self.horizontalView)
        self.horizontalView.snp.makeConstraints { make in
            make.top.equalTo(0)
            make.leading.trailing.equalTo(0)
            make.height.equalTo(1)
        }
        
        self.addSubview(self.verticalView)
        self.verticalView.snp.makeConstraints { make in
            make.top.equalTo(0)
            make.bottom.equalTo(0).priority(999)
            make.leading.equalTo(0)
            make.width.equalTo(1)
        }
        
        addSubview(timeLabel)
        timeLabel.snp.makeConstraints { make in
            make.bottom.equalTo(verticalView)
            make.height.equalTo(20)
            make.centerX.equalTo(verticalView).priority(.low)
            make.leading.greaterThanOrEqualTo(self)
            make.trailing.lessThanOrEqualTo(self)
        }
        
        self.addSubview(self.infoView)
        self.infoView.snp.makeConstraints { make in
            make.top.equalTo(0)
            make.leading.equalTo(4)
        }
        
        self.addSubview(self.priceLabel)
        self.priceLabel.snp.makeConstraints { make in
            make.centerY.equalTo(self.horizontalView)
            make.leading.equalTo(0)
            make.height.equalTo(16)
        }
        
        self.addSubview(self.priceLeftLabel)
        self.priceLeftLabel.snp.makeConstraints { make in
            make.centerY.equalTo(self.priceLabel).offset(-0.3)
            make.trailing.equalTo(self.priceLabel.snp.leading).offset(7)
        }
        
        self.addSubview(self.priceRightLabel)
        self.priceRightLabel.snp.makeConstraints { make in
            make.centerY.equalTo(self.priceLabel).offset(0.2)
            make.leading.equalTo(self.priceLabel.snp.trailing).offset(-7)
        }
        
        
        self.changeTheme()
    }
  
    public func updateData(kLineModel: KLineModel, price: String) {
        self.infoView.updateData(kLineModel: kLineModel)
        self.priceLabel.text = "\(ShowNumber(price, .fullDown(kLineModel.priceScale))) "
        let time = TimeInterval(kLineModel.timestamp)
        let date = Date(timeIntervalSince1970: time / 1000.0)
//        if UserDefaults.KlineSelectedTimeline == .second_1 {
            self.timeLabel.text(" " + date.stringWithFormat(format: ConstTools.dateFormatHMS) + " ")
//        } else {
            self.timeLabel.text(" " + date.stringWithFormat(format: ConstTools.dateFormatHM) + " ")
//        }
        
    }
    
    public func showIndicatorViews(isShow: Bool, horizontalStatus: Bool ,point: CGPoint?) {
        if KLineConfig.tapType == .pop {
            self.infoView.isHidden = false
        } else {
            self.infoView.isHidden = true
        }
        
        guard let p = point else {
            if !isShow {
                self.isHidden = !isShow
            }
            return
        }
        self.isHidden = !isShow
        if KLineConfig.infoViewSetSeparatelyHorizontalLine{
            self.horizontalView.isHidden = horizontalStatus
            self.priceLabel.isHidden = horizontalStatus
        }
        if p.x > CGFloat(self.width / 2.0) {
            self.infoView.snp.remakeConstraints { make in
                make.top.equalTo(0)
                make.leading.equalTo(4)
            }
            
            self.priceLabel.snp.remakeConstraints { make in
                make.centerY.equalTo(self.horizontalView)
                make.trailing.equalTo(0)
                make.height.equalTo(16)
            }
        } else {
            self.infoView.snp.remakeConstraints { make in
                make.top.equalTo(0)
                make.trailing.equalTo(-4)
            }
            
            self.priceLabel.snp.remakeConstraints { make in
                make.centerY.equalTo(self.horizontalView)
                make.leading.equalTo(0)
                make.height.equalTo(16)
            }
        }
        
        self.horizontalView.snp.updateConstraints { make in
            make.top.equalTo(p.y)
        }
        
        self.verticalView.snp.updateConstraints { make in
            make.leading.equalTo(p.x - 0.5)
        }
    }
    
    /// 主题改变
    public func changeTheme(_ theme: Theme = KlineLibThemeManager.currentTheme) {
        self.infoView.changeTheme(theme)
        self.priceLabel.bgColor(KLineConfig.themeManager.klineLibSubTextColor())
        self.priceLabel.textColor(KLineConfig.themeManager.klineLibDefaultWhiteColor())
        self.priceLeftLabel.textColor(KLineConfig.themeManager.klineLibSubTextColor())
        self.priceRightLabel.textColor(KLineConfig.themeManager.klineLibSubTextColor())
        self.timeLabel.textColor(KLineConfig.themeManager.klineLibDefaultWhiteColor())
        self.timeLabel.bgColor(KLineConfig.themeManager.klineLibSubTextColor())
        self.horizontalView.dotColor = KLineConfig.themeManager.klineLibSubTextColor()
        self.verticalView.dotColor = KLineConfig.themeManager.klineLibSubTextColor()
    }
}
