//
//  FILE.swift
//  Ourbit
//
//  Created by pippen on 2024.
//

import UIKit

// swiftlint:disable:next all
public let KlineGotoOrderRecordNotification = "noti.kline.goto.record"

open class KlineTapOrderView: UIView {
    /// 合约买订单
    public lazy var titleL: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .text("ob_order_direction_of_buy".BPLocalized())
            .bgColor(.clear)
        return lb
    }()
    
    public lazy var textL: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
            .bgColor(.clear)
        lb.textAlignment(.right)
        return lb
    }()
    
    public lazy var arrowL: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
           .textColor(KLineConfig.themeManager.klineLibSubTextColor())
           .bgColor(.clear)
        lb.textAlignment(.right)
        return lb
    }()
    
    public var isRightLayout = false
    
    public init(_ rightLayout: Bool = false) {
        super.init(frame: CGRect(x: 0, y: 0, width: 40, height: 14))
        self.isRightLayout = rightLayout
        setupUI()
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    public func setupUI() {
        addSubview(titleL)
        addSubview(textL)
        addSubview(arrowL)
        
        if isRightLayout == true {
            arrowL.snp.makeConstraints { make in
                make.centerY.equalTo(titleL)
                make.trailing.equalTo(-4)
                make.height.width.equalTo(8)
            }
            textL.snp.makeConstraints { make in
                make.centerY.equalTo(titleL)
                make.trailing.equalTo(arrowL.snp.leading).offset(-4)
            }
            titleL.snp.makeConstraints { make in
                make.trailing.equalTo(textL.snp.leading).offset(-8)
                make.leading.equalTo(4)
                make.bottom.top.equalToSuperview()
            }
        } else {
            titleL.snp.makeConstraints { make in
                make.leading.equalTo(4)
                make.bottom.top.equalToSuperview()
            }
            textL.snp.makeConstraints { make in
                make.centerY.equalTo(titleL)
                make.leading.equalTo(titleL.snp.trailing).offset(8)
            }
            arrowL.snp.makeConstraints { make in
                make.centerY.equalTo(titleL)
                make.leading.equalTo(textL.snp.trailing).offset(4)
                make.trailing.equalTo(-4)
                make.height.width.equalTo(8)
            }
        }
        
        let btn = UIButton()
        btn.addTarget(self, action: #selector(backgroundClick), for: .touchUpInside)
        btn.backgroundColor = UIColor.clear
        addSubview(btn)
        btn.snp.makeConstraints { make in
            make.leading.top.equalTo(titleL)
            make.trailing.equalTo(arrowL)
            make.height.equalTo(20)
        }
    }
    
    @objc func backgroundClick() {
#warning("到时需要看看 这个Btn是干什么的")
//        if let kLineVC = self.closestController() as? OBKLineHorizontalViewController {
//            kLineVC.littleBtnClicked()
//            OBNoti.post(OBKlineGotoOrderRecordNotification, true)
//        } else {
        CusstomNotification.post(KlineGotoOrderRecordNotification)
//        }
    }
}

open class KLineTapInfoView: UIView {
    /// 开
    public lazy var openLabel: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .textColor(KLineConfig.themeManager.klineLibSubTextColor())
            .text("ob_common_open".BPLocalized())
            .bgColor(.clear)
        return lb
    }()
    
    public lazy var open: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
            .bgColor(.clear)
        lb.textAlignment(.right)
        return lb
    }()
    
    /// 高
    public lazy var highLabel: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .textColor(KLineConfig.themeManager.klineLibSubTextColor())
            .text("ob_common_high".BPLocalized())
            .bgColor(.clear)
        return lb
    }()
    
    public lazy var high: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
            .bgColor(.clear)
        lb.textAlignment(.right)
        return lb
    }()
    
    /// 低
    public lazy var lowLabel: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .textColor(KLineConfig.themeManager.klineLibSubTextColor())
            .text("ob_common_low".BPLocalized())
            .bgColor(.clear)
        return lb
    }()
    
    public lazy var low: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
            .bgColor(.clear)
        lb.textAlignment(.right)
        return lb
    }()
    
    /// 收
    public lazy var closeLabel: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .textColor(KLineConfig.themeManager.klineLibSubTextColor())
            .text("ob_common_close".BPLocalized())
            .bgColor(.clear)
        return lb
    }()
    
    public lazy var close: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
            .bgColor(.clear)
        lb.textAlignment(.right)
        return lb
    }()
    
    /// 涨跌额
    public lazy var changeAmountLabel: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .textColor(KLineConfig.themeManager.klineLibSubTextColor())
            .text("ob_user_rise_fall_quantity".BPLocalized())
            .bgColor(.clear)
        return lb
    }()
    
    public lazy var changeAmount: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
            .bgColor(.clear)
        lb.textAlignment(.right)
        return lb
    }()
    
    /// 涨跌幅
    public lazy var changeRateLabel: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .textColor(KLineConfig.themeManager.klineLibSubTextColor())
            .text("ob_user_rise_fall_extent".BPLocalized())
            .bgColor(.clear)
        return lb
    }()
    
    public lazy var changeRate: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
            .bgColor(.clear)
        lb.textAlignment(.right)
        return lb
    }()
    
    /// 成交量
    public lazy var volumeLabel: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .textColor(KLineConfig.themeManager.klineLibSubTextColor())
            .text("ob_order_volume".BPLocalized())
            .bgColor(.clear)
        return lb
    }()
    
    public lazy var volume: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
            .bgColor(.clear)
        lb.textAlignment(.right)
        return lb
    }()
    
    /// 成交额
    public lazy var dealLabel: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .textColor(KLineConfig.themeManager.klineLibSubTextColor())
            .text("ob_kline_amount".BPLocalized())
            .bgColor(.clear)
        return lb
    }()
    
    public lazy var dealL: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
            .bgColor(.clear)
        lb.textAlignment(.right)
        return lb
    }()
    
    /// 振幅
    public lazy var rangeLabel: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .textColor(KLineConfig.themeManager.klineLibSubTextColor())
            .text("ob_common_range".BPLocalized())
            .bgColor(.clear)
        return lb
    }()
    
    public lazy var rangeL: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
            .bgColor(.clear)
        lb.textAlignment(.right)
        return lb
    }()
    
    /// 时间
    public lazy var timeLabel: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .textColor(KLineConfig.themeManager.klineLibSubTextColor())
            .text("ob_common_time".BPLocalized())
            .bgColor(.clear)
        return lb
    }()
    
    public lazy var time: UILabel = {
        let lb = UILabel()
        lb.textFont(UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular))
            .textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
            .bgColor(.clear)
        lb.textAlignment(.right)
        return lb
    }()
    
    /// 合约买
    public lazy var buyOrderV: KlineTapOrderView = {
        let v = KlineTapOrderView()
        addSubview(v)
        return v
    }()
    
    /// 合约卖
    public lazy var sellOrderV: KlineTapOrderView = {
        let v = KlineTapOrderView()
        addSubview(v)
        return v
    }()
    
    /// 合约强平订单
    public lazy var liquidationOrderV: KlineTapOrderView = {
        let v = KlineTapOrderView()
        v.titleL.text = "ob_kline_contract_liquidation_order_abbreviation".BPLocalized()
        addSubview(v)
        return v
    }()
    
    public var lastView: UIView?
    
    public let offsetTop = 2
    
    public override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    public func setupUI() {
        layer.cornerRadius = 4
        layer.masksToBounds = true
        
        addSubview(timeLabel)
        timeLabel.snp.makeConstraints { make in
            make.top.equalTo(4)
            make.leading.equalTo(4)
        }
        
        addSubview(time)
        time.snp.makeConstraints { make in
            make.centerY.equalTo(timeLabel)
            make.trailing.equalTo(-4)
            make.leading.equalTo(timeLabel.snp.trailing).offset(12)
        }
        
        addSubview(openLabel)
        openLabel.snp.makeConstraints { make in
            make.top.equalTo(time.snp.bottom).offset(offsetTop)
            make.leading.equalTo(4)
        }
        
        addSubview(open)
        open.snp.makeConstraints { make in
            make.centerY.equalTo(openLabel)
            make.trailing.equalTo(-4)
            make.leading.equalTo(openLabel.snp.trailing).offset(11)
        }
        
        addSubview(highLabel)
        highLabel.snp.makeConstraints { make in
            make.top.equalTo(openLabel.snp.bottom).offset(offsetTop)
            make.leading.equalTo(4)
        }
        
        addSubview(high)
        high.snp.makeConstraints { make in
            make.centerY.equalTo(highLabel)
            make.trailing.equalTo(-4)
            make.leading.equalTo(highLabel.snp.trailing).offset(12)
        }
        
        addSubview(lowLabel)
        lowLabel.snp.makeConstraints { make in
            make.top.equalTo(highLabel.snp.bottom).offset(offsetTop)
            make.leading.equalTo(4)
        }
        
        addSubview(low)
        low.snp.makeConstraints { make in
            make.centerY.equalTo(lowLabel)
            make.trailing.equalTo(-4)
            make.leading.equalTo(lowLabel.snp.trailing).offset(12)
        }
        
        addSubview(closeLabel)
        closeLabel.snp.makeConstraints { make in
            make.top.equalTo(lowLabel.snp.bottom).offset(offsetTop)
            make.leading.equalTo(4)
        }
        
        addSubview(close)
        close.snp.makeConstraints { make in
            make.centerY.equalTo(closeLabel)
            make.trailing.equalTo(-4)
            make.leading.equalTo(closeLabel.snp.trailing).offset(12)
        }
        
        addSubview(changeAmountLabel)
        changeAmountLabel.snp.makeConstraints { make in
            make.top.equalTo(closeLabel.snp.bottom).offset(offsetTop)
            make.leading.equalTo(4)
        }
        
        addSubview(changeAmount)
        changeAmount.snp.makeConstraints { make in
            make.centerY.equalTo(changeAmountLabel)
            make.trailing.equalTo(-4)
            make.leading.equalTo(changeAmountLabel.snp.trailing).offset(12)
        }
        
        addSubview(changeRateLabel)
        changeRateLabel.snp.makeConstraints { make in
            make.top.equalTo(changeAmountLabel.snp.bottom).offset(offsetTop)
            make.leading.equalTo(4)
        }
        
        addSubview(changeRate)
        changeRate.snp.makeConstraints { make in
            make.centerY.equalTo(changeRateLabel)
            make.trailing.equalTo(-4)
            make.leading.equalTo(changeRateLabel.snp.trailing).offset(12)
        }
        
        if needShowDealInfo() {
            addSubview(rangeLabel)
            rangeLabel.snp.makeConstraints { make in
                make.top.equalTo(changeRateLabel.snp.bottom).offset(offsetTop)
                make.leading.equalTo(4)
            }
            
            addSubview(rangeL)
            rangeL.snp.makeConstraints { make in
                make.centerY.equalTo(rangeLabel)
                make.trailing.equalTo(-4)
                make.leading.equalTo(rangeLabel.snp.trailing).offset(12)
            }
            
            addSubview(dealLabel)
            dealLabel.snp.makeConstraints { make in
                make.top.equalTo(rangeLabel.snp.bottom).offset(offsetTop)
                make.leading.equalTo(4)
            }
            
            addSubview(dealL)
            dealL.snp.makeConstraints { make in
                make.centerY.equalTo(dealLabel)
                make.trailing.equalTo(-4)
                make.leading.equalTo(dealLabel.snp.trailing).offset(12)
            }
            
            addSubview(volumeLabel)
            volumeLabel.snp.makeConstraints { make in
                make.top.equalTo(dealLabel.snp.bottom).offset(offsetTop)
                make.leading.equalTo(4)
            }
            
            addSubview(volume)
            volume.snp.makeConstraints { make in
                make.centerY.equalTo(volumeLabel)
                make.trailing.equalTo(-4)
                make.leading.equalTo(volumeLabel.snp.trailing).offset(12)
            }
        } else {
            addSubview(volumeLabel)
            volumeLabel.snp.makeConstraints { make in
                make.top.equalTo(changeRateLabel.snp.bottom).offset(offsetTop)
                make.leading.equalTo(4)
            }
            
            addSubview(volume)
            volume.snp.makeConstraints { make in
                make.centerY.equalTo(volumeLabel)
                make.trailing.equalTo(-4)
                make.leading.equalTo(volumeLabel.snp.trailing).offset(12)
            }
        }
        
        lastView = volumeLabel
    }

    public func updateData(kLineModel: KLineModel) {
        let time = TimeInterval(kLineModel.timestamp)
        let date = Date(timeIntervalSince1970: time / 1000.0)
//        if UserDefaults.KlineSelectedTimeline == .second_1 {
            self.time.text(date.stringWithFormat(format: ConstTools.dateFormatYMD))
//        } else {
            self.time.text(date.stringWithFormat(format: ConstTools.dateFormatYMDHMS))
//        }
        
        // 在K线开盘价配置切换时, 最终open数据会被处理成正确的开盘价
        // 首次交易价
        //  - 现货: earliestDealPrice
        //  - 合约: realOpen
        let priceScale = kLineModel.priceScale
        open.text(ShowNumber(kLineModel.open, .fullDown(priceScale)))
        high.text(ShowNumber(kLineModel.high, .fullDown(priceScale)))
        low.text(ShowNumber(kLineModel.low, .fullDown(priceScale)))
        close.text(ShowNumber(kLineModel.close, .fullDown(priceScale)))
        
        // 涨跌幅计算方式
        // 现货: （收盘价-开盘价）/ 开盘价 x 100
        // 合约: （本期收盘价-上期收盘价）/ 上期收盘价 x 100
        var color: UIColor
        var changeAmountStr: String
        var changeRateStr: String
        if needShowDealInfo() {
            // 合约
            color = KLineConfig.themeManager.klineLibDefaultTextColor()
            if !kLineModel.isFirstKline() {
                color = kLineModel.riseDownAmount() < 0.0
                ? KLineConfig.themeManager.candleRiseColor()
                : KLineConfig.themeManager.candleDeclineColor()
            }
            changeAmountStr = kLineModel.dispalyRiseDownAmount()
            changeRateStr = kLineModel.displayRiseDownRate()
        } else {
            // 现货
            let changeAmount = kLineModel.close - kLineModel.open
            color = changeAmount < 0.0
            ? KLineConfig.themeManager.candleRiseColor()
            : KLineConfig.themeManager.candleDeclineColor()
            let symbol: String = (changeAmount < 0.0 ? "" : "+")
            changeAmountStr = symbol + ShowNumber(kLineModel.close.sub(right: kLineModel.open), .fullDown(priceScale))
            if kLineModel.open == 0 {
                changeRateStr = "--"
            } else {
                changeRateStr = ShowPercent((changeAmount / kLineModel.open * 100.0).decimalValue,
                                              .fullDown(2),
                                              positivePrefix: "+",
                                              zeroPrefix: "+")
            }
        }
        
        self.changeAmount.text(changeAmountStr)
        self.changeRate.text(changeRateStr)
        self.changeAmount.textColor(color)
        self.changeRate.textColor(color)
        let volScale = kLineModel.quantityScale
        let amount = AccuracyManager.quantityNum(accuracy: volScale, quantity: kLineModel.volume)
        volume.text(amount)
        
        if needShowDealInfo() {
            // 成交额
            let deal = AccuracyManager.quantityNum(accuracy: 3, quantity: kLineModel.amount)
            dealL.text(deal)
            
            // 振幅
            rangeL.text(kLineModel.rangeAmount())
        }
        
        // 显示订单信息
        /*
        let viewModel = KLineOrderManager.shared
        var updateLastView = true
        var bottomView = lastView
        // 是否需要显示强平
        var showLqiInfo: String?
        if viewModel.needShowOrderLqiInfo(), let lqiInfo = viewModel.getOrderLqiInfo(kLineModel) {
            showLqiInfo = lqiInfo
        }
        // 订单买卖
        if viewModel.symbol != nil, let view = lastView, viewModel.needShowOrderBSInfo() {
            var v = view
            let buyInfo = viewModel.getOrderBuyInfo(kLineModel)
            let sellInfo = viewModel.getOrderSellInfo(kLineModel)
            
            if buyInfo != nil || sellInfo != nil {
                // 更新最后个view
                if needShowDealInfo() {
                    view.snp.remakeConstraints { make in
                        make.top.equalTo(dealLabel.snp.bottom).offset(offsetTop)
                        make.leading.equalTo(4)
                    }
                } else {
                    view.snp.remakeConstraints { make in
                        make.top.equalTo(changeRateLabel.snp.bottom).offset(offsetTop)
                        make.leading.equalTo(4)
                    }
                }
                updateLastView = false
            }
            
            if let str = buyInfo {
                if sellInfo != nil {
                    buyOrderV.snp.remakeConstraints { make in
                        make.top.equalTo(v.snp.bottom).offset(offsetTop)
                        make.trailing.leading.equalToSuperview()
                    }
                } else {
                    buyOrderV.snp.remakeConstraints { make in
                        make.top.equalTo(v.snp.bottom).offset(offsetTop)
                        make.trailing.leading.equalToSuperview()
                        if showLqiInfo == nil {
                            make.bottom.equalTo(-4)
                        }
                    }
                }
                v = buyOrderV
                
                buyOrderV.titleL.text = "ob_order_direction_of_buy".BPLocalized()
                buyOrderV.titleL.textColor = KLineConfig.themeManager.candleDeclineColor()
                buyOrderV.textL.textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
                buyOrderV.textL.text = str
                
                buyOrderV.isHidden = false
                bottomView = buyOrderV
            } else {
                buyOrderV.isHidden = true
                buyOrderV.snp.removeConstraints()
            }
            
            // 强平信息
            if let str = sellInfo {
                sellOrderV.snp.remakeConstraints { make in
                    make.top.equalTo(v.snp.bottom).offset(offsetTop)
                    make.trailing.leading.equalToSuperview()
                    if showLqiInfo == nil {
                        make.bottom.equalTo(-4)
                    }
                }
                
                sellOrderV.titleL.text = "ob_order_direction_of_sell".BPLocalized()
                sellOrderV.titleL.textColor = KLineConfig.themeManager.candleRiseColor()
                sellOrderV.textL.textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
                sellOrderV.textL.text = str
                
                sellOrderV.isHidden = false
                bottomView = sellOrderV
            } else {
                sellOrderV.isHidden = true
                sellOrderV.snp.removeConstraints()
            }
        }
        
        if let view = bottomView, let lqiInfo = showLqiInfo {
            if view == lastView {
                // 更新最后个view
                if needShowDealInfo() {
                    view.snp.remakeConstraints { make in
                        make.top.equalTo(dealLabel.snp.bottom).offset(offsetTop)
                        make.leading.equalTo(4)
                    }
                } else {
                    view.snp.remakeConstraints { make in
                        make.top.equalTo(changeRateLabel.snp.bottom).offset(offsetTop)
                        make.leading.equalTo(4)
                    }
                }
            }
            updateLastView = false
            liquidationOrderV.isHidden = false
            liquidationOrderV.titleL.textColor = KLineConfig.themeManager.orangeColor()
            liquidationOrderV.textL.textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
            liquidationOrderV.textL.text = lqiInfo
            liquidationOrderV.snp.remakeConstraints { make in
                make.top.equalTo(view.snp.bottom).offset(offsetTop)
                make.trailing.leading.equalToSuperview()
                make.bottom.equalTo(-4)
            }
        } else {
            liquidationOrderV.isHidden = true
        }
         */
        if true {
            buyOrderV.isHidden = true
            sellOrderV.isHidden = true
            
            if needShowDealInfo() {
                lastView?.snp.remakeConstraints { make in
                    make.top.equalTo(dealLabel.snp.bottom).offset(offsetTop)
                    make.leading.equalTo(4)
                    make.bottom.equalTo(-4)
                }
            } else {
                lastView?.snp.remakeConstraints { make in
                    make.top.equalTo(changeRateLabel.snp.bottom).offset(offsetTop)
                    make.leading.equalTo(4)
                    make.bottom.equalTo(-4)
                }
            }
        }
         
    }
    
    /// 主题改变
    public func changeTheme(_ theme: Theme = .light) {
        backgroundColor = KLineConfig.themeManager.fillBgColor()
        openLabel.textColor(KLineConfig.themeManager.klineLibSubTextColor())
        open.textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
        highLabel.textColor(KLineConfig.themeManager.klineLibSubTextColor())
        high.textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
        lowLabel.textColor(KLineConfig.themeManager.klineLibSubTextColor())
        low.textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
        closeLabel.textColor(KLineConfig.themeManager.klineLibSubTextColor())
        close.textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
        changeAmountLabel.textColor(KLineConfig.themeManager.klineLibSubTextColor())
        changeAmount.textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
        changeRateLabel.textColor(KLineConfig.themeManager.klineLibSubTextColor())
        changeRate.textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
        volumeLabel.textColor(KLineConfig.themeManager.klineLibSubTextColor())
        volume.textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
        timeLabel.textColor(KLineConfig.themeManager.klineLibSubTextColor())
        time.textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
        
        if needShowDealInfo() {
            rangeLabel.textColor(KLineConfig.themeManager.klineLibSubTextColor())
            rangeL.textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
            dealLabel.textColor(KLineConfig.themeManager.klineLibSubTextColor())
            dealL.textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
        }
    }
    
    /// 是否显示成交额信息
    public func needShowDealInfo() -> Bool { return false/* KLineManager.shared.tradeType == .contract*/ }
}
