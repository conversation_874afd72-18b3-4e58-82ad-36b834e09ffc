//
//  FILE.swift
//  Ourbit
//
//  Created by pippen on 2024.
//

import Foundation
import UIKit

let KLineDrawShapeKey = "KLineDrawShapeKey"
let KLineDrawShapeParamKey = "KLineDrawShapeParamKey"
let KLineDrawShapeHiddenSpotKey = "KLineDrawShapeHiddenSpotKey"

public enum KLineDrawShapeStatus: Int,Codable {
    /// 默认情况
    case none // swiftlint:disable:this all
    /// 选中
    case selected
    /// 绘制中
    case drawPoint
}

public enum KLineDrawStatus: Int {
    /// 默认情况
    case none
    /// 正在绘制
    case drawShape
    /// 可点击状态
    case tapShape
}

public enum KLineDrawShapeType: Int,Codable {
    /// 线段
    case segmentLine = 0
    /// 射线
    case radialLine = 1
    /// 垂直线
    case verticalLine = 2
    /// 价格线
    case priceLine = 3
    /// 水平线
    case horizontalLine = 4
    /// 平行通道
    case parallelChannel = 5
    
    /// 矩形
    case rectangle = 10
    /// 平行四边形
    case parallelogram = 11
    /// 斐波那契
    case fibonacci = 12
    /// 三浪
    case threeWaves = 13
    /// 五浪
    case fiveWaves = 14
    
    public func iconL() -> String {
        switch self {
        case .segmentLine:
            return "\u{e802}"
        case .radialLine:
            return "\u{e808}"
        case .verticalLine:
            return "\u{e80b}"
        case .priceLine:
            return "\u{e800}"
        case .horizontalLine:
            return "\u{e80d}"
        case .parallelogram:
            return "\u{e801}"
        case .rectangle:
            return "\u{e7fd}"
        case .parallelChannel:
            return "\u{e7ff}"
        case .fibonacci:
            return "\u{e82f}"
        case .threeWaves:
            return "\u{e831}"
        case .fiveWaves:
            return "\u{e832}"
        }
    }
    
    public var title:String{
        switch self {
        case .segmentLine:
            return "ob_chart_draw_type_trend_line"
        case .radialLine:
            return "ob_chart_draw_type_ray"
        case .verticalLine:
            return "ob_chart_draw_type_vertical_line"
        case .priceLine:
            return "ob_chart_draw_type_price_line"
        case .parallelChannel:
            return "ob_chart_draw_type_parallel"
        case .rectangle:
            return "ob_chart_draw_type_rectangle"
        case .parallelogram:
            return "ob_chart_draw_type_parallelogram"
        case .horizontalLine:
            return "ob_chart_draw_type_horizontal_line"
        case .fibonacci:
            return ""
        case .threeWaves:
            return "ob_chart_draw_type_three_waves"
        case .fiveWaves:
            return "ob_chart_draw_type_five_waves"
        }
    }
    
    public var selectedTitle:String {
        switch self {
        case .segmentLine:
            return "ob_chart_draw_type_trend_line_selected"
        case .radialLine:
            return "ob_chart_draw_type_ray_selected"
        case .verticalLine:
            return "ob_chart_draw_type_vertical_line_selected"
        case .priceLine:
            return "ob_chart_draw_type_price_line_selected"
        case .parallelChannel:
            return "ob_chart_draw_type_parallel_selected"
        case .rectangle:
            return "ob_chart_draw_type_rectangle_selected"
        case .parallelogram:
            return "ob_chart_draw_type_parallel_selected"
        case .horizontalLine:
            return "ob_chart_draw_type_horizontal_line_selected"
        case .fibonacci:
            return "ob_chart_draw_type_fibonacci_selected"
        case .threeWaves:
            return "ob_chart_draw_type_three_waves"
        case .fiveWaves:
            return "ob_chart_draw_type_five_waves"
        }
    }
    
    public func totalPointCount() -> Int {
        switch self {
        case .segmentLine, .radialLine, .rectangle, .verticalLine, .fibonacci:
            return 2
        case .priceLine, .horizontalLine:
            return 1
        case .parallelogram, .parallelChannel:
            return 3
        case .threeWaves:
            return 4
        case .fiveWaves:
            return 6
        }
    }
}

public struct KLineDrawParam: Codable {
    public var drawThemColor: String = "E4B10C"
    
    public var drawLineWidth: CGFloat = 1
    
    public var drawLineDashes: [CGFloat] = []
    
    public enum CodingKeys: String, CodingKey {
        case drawLineDashes = "drawLineDashes"
        case drawLineWidth = "drawLineWidth"
        case drawThemColor = "drawThemColor"
    }
    public init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        
        drawLineDashes = try values.decodeIfPresent([CGFloat].self, forKey: .drawLineDashes) ?? []
        drawLineWidth = try values.decodeIfPresent(CGFloat.self, forKey: .drawLineWidth) ?? 1.0
        drawThemColor = try values.decodeIfPresent(String.self, forKey: .drawThemColor) ?? KLineConfig.themeManager.orangeColor().toHex(includeAlpha: false)
    }
    public init(){
        
    }
    
}

open class KLineDrawViewModel {
    public static let shared = KLineDrawViewModel()
    
    public weak var contentView: KLineDrawView?
    ///用于替代下方传入controller 的logic
    ///仅仅需要实现 划线功能的地方需要实现 该协议 其他地方不需要
    public var notificationDelegate:KLineDrawManagerDelegate?
#warning("后面需要注意这个值什么时候赋值 否则会导致划线功能 显示不出线条")
    public var curSymbol: String?
    
//    public var futureType: ContractType?
    
    public var drawShapeType: KLineDrawShapeType?
    
    /// 需要绘制的K线数据
    // swiftlint:disable:next all
    public var needDrawKLineModels: [KLineModel]?

    /// 最大值
    public var maxValue: Double = 0.0
    /// 最小值
    public var minValue: Double = 0.0
    
    /// k线界面大小
    public var klineViewSize: CGSize {
        return self.contentView?.frame.size ?? .zero
    }
    
    /// 价格精度
    public var pricesScale: Int = 4
    
    /// 拖动点位
    public var dragPoint: CGPoint = .zero
    
    /// 当前选中Model
    public var currentDrawModel: KLineDrawModel?
    
    /// 所有绘制Model
    public var drawModelList: [KLineDrawModel] = []
    
    
    /// 绘制参数，包括颜色、大小
    public var drawParam = KLineDrawParam()
    
    /// 当前状态
    public var drawStatus: KLineDrawStatus = .none {
        didSet {
            // 清空之前选中的数据
            if oldValue == .drawShape {
                if let m = self.currentDrawModel {
                    if m.status == .drawPoint {
                        // 如果当前绘制中
                        self.drawModelList.removeAll(where: {$0.id == m.id})
                    } else {
                        // 更新当前形状状态
                        m.status = .none
                    }
                    self.reloadContentView()
                }
                // 移除当前绘制状态
                self.drawShapeType = nil
                // 隐藏tips
                self.p_hiddenDrawTips()
            }
            CusstomNotification.post(KLineDrawModeDidChangedNotification, drawStatus)
        }
    }
    
    /// 隐藏绘制结果
    public var hiddenDraw: Bool {
        get {
            let key = KLineDrawShapeHiddenSpotKey
            return UDTool.bool(key: key)
        }
        set {
            // 记录隐藏设置，区分现货和合约
            let key =  KLineDrawShapeHiddenSpotKey
            UDTool.setBool(value: newValue, forKey: key)
            
            self.contentView?.isHidden = newValue
            self.reloadContentView()
            if newValue == true {
                self.p_hiddenDrawTips()
            } else {
                self.p_updateDrawHeaderTips()
            }
        }
    }
    
    /// 是否连续绘制
    public var isRepeatDraw:Bool = false {
        didSet {
            self.p_showRepeatDrawTips()
        }
    }
    
    /// 是否在绘制过程中
    public var isDrawShape: Bool {
        if (currentDrawModel == nil && drawShapeType != nil) || currentDrawModel?.status == .drawPoint {
            return true
        }
        return false
    }
    
    public init() {
        // 读取画线设置
        self.loadDrawParam()
    }
    
    /// 更新Model Postion
    public func reloadDrawModelPostion(_ model: KLineDrawModel, _ pointList: [CGPoint]) {
        var postionList: [KLineDrawPostion] = []
        for point in pointList {
            if let postion = self.calculationModelPoint(point) { postionList.append(postion) }
        }
        model.postionList = postionList
    }
    
    public func p_drawShapeEnd() {
        guard let model = self.currentDrawModel, model.status == .selected else { return }
        // 更新设置界面，此时画完图形可以删除锁定
        //self.controller?.drawSettingView.reloadDrawModel()
        if let delegate = self.notificationDelegate{
            delegate.drawSettingViewReloadDrawModel()
        }
        
        // 保存数据
        self.saveDrawShape()
        
        if self.isRepeatDraw, let type = self.drawShapeType {
            // 如果连续绘制，并且有选择图形，继续绘制
            self.createNewDrawModel(type)
        } else {
            self.drawShapeType = nil
        }
    }
    
    public func p_dragMovePointEnd(_ model: KLineDrawModel) {
        model.updateStatus()
        model.dragType = .none
        // 线上tips
        self.p_updateDrawHeaderTips()
        // 转换坐标
        model.reloadData()
        self.reloadDrawModelPostion(model, model.keyPointList)
        self.p_drawShapeEnd()
    }
    
    /// 拖动
    public func drawLineTouchesMoved(_ pt: CGPoint, _ klinePoint: KLineDrawPostion?, _ state: UIGestureRecognizer.State) {
        guard let model = self.currentDrawModel, model.lockDraw == false else { return }
        
        defer {
            contentView?.reloadData()
        }
        
        switch state {
        case .began:
            
            if model.status == .selected, model.containSelectPoint(pt) == false {
                self.clearSelectDrawModel()
                return
            }
            
            // 如果在绘制中，则可以拖动
            if model.status == .drawPoint {
                // 拖动关键点
                model.dragType = .dragPoint
                // 更新关键点
                model.pointIndex = model.drawPointCount() - 1
            } else {
                if model.containControlPoint(pt) {
                    // 拖动关键点
                    model.dragType = .dragPoint
                    // 更新关键点
                    model.updateKeyPointIndex(pt)
                } else if model.containsPath(pt) {
                    // 移动
                    model.dragType = .moveShape
                    self.dragPoint = pt
                }
            }
        case .changed:
            // 移动位置
            switch model.dragType {
            case .moveShape:
                model.transformPoint = CGPoint(x: pt.x - dragPoint.x,
                                               y: pt.y - dragPoint.y)
            case .dragPoint:
                // 拖动关键点
                model.updateKeyPoint(pt)
                model.reloadData()
                
            case .none:
                break
            }
            
            // 价格点需要更新Postion
            if model.needUpdatePrice {
                var updatePrice = (model.dragType == .moveShape || model.status == .drawPoint)
                if !updatePrice, model.dragType == .dragPoint, model.drawShapeType == .fibonacci {
                    updatePrice = true
                }
                if updatePrice {
                    let transform = CGAffineTransform(translationX: model.transformPoint.x, y: model.transformPoint.y)
                    let list = model.keyPointList.map { $0.applying(transform) }
                    self.reloadDrawModelPostion(model, list)
                }
            }
        case .ended:
            self.p_dragMovePointEnd(model)
        default:
            break
        }
    }
    
    /// 触摸开始
    public func drawLineTouchesBegan(_ pt: CGPoint) {
        guard let type = drawShapeType, let klinePoint = self.calculationModelPoint(pt) else { return }
        
        if self.currentDrawModel == nil {
            // 画线
            let drawModel = KLineDrawModel(type: type, pt: pt, klinePoint: klinePoint)
            drawModel.fillColor = drawParam.drawThemColor
            drawModel.lineThickness = drawParam.drawLineWidth
            drawModel.lineDashes = drawParam.drawLineDashes
            self.drawModelList.append(drawModel)
            self.currentDrawModel = drawModel
        } else {
            // 更新关键点
            if self.currentDrawModel?.status == .drawPoint {
                self.currentDrawModel?.addKeyPoint(pt, klinePoint)
            }
        }
    }
    
    /// 点击事件
    public func drawLineTouchesEnded(_ pt: CGPoint) {
        // 如果没有画线，并且没有选择数据
        let selectModel: KLineDrawModel? = self.checkContainsPoint(pt)
        if self.currentDrawModel == nil, self.drawShapeType == nil, selectModel == nil {
            return
        }
        
        defer {
            self.p_updateDrawHeaderTips()
            self.reloadContentView()
        }
        
        // 如果当前选中了空白界面，移除当前选中结果
        if self.currentDrawModel != nil, self.currentDrawModel?.status == .selected, selectModel == nil {
            self.clearSelectDrawModel()
            return
        }
        
        self.drawLineTouchesBegan(pt)
        
        // 用户选中了图形
        if self.currentDrawModel == nil, let m = selectModel {
            // 选择了线段
            self.currentDrawModel = m
            self.drawStatus = .drawShape
        }
        
        // 点击情况，只会用绘制点
        if let drawModel = self.currentDrawModel {
            drawModel.updateStatus()
            
            // 图形画完，清理标识
            if drawModel.status == .selected {
                self.p_drawShapeEnd()
            } else if drawModel.status != .drawPoint {
                self.clearSelectDrawModel()
            }
        }
    }
    
    public func reloadKlineTimeData() {
        // 如果当前正在绘制，清空
        self.clearSelectDrawModel()
        self.drawShapeType = nil
        self.p_hiddenDrawTips()

        for m in self.drawModelList {
            m.reloadData()
        }
        
        CusstomNotification.post(KLineDrawModeDidLoadNotification)
        self.reloadContentView()
        if self.drawStatus == .drawShape {
            self.drawStatus = .tapShape
        }
    }
    
    /// app进入后台
    public func appDidEnterBackground() {
        guard let model = self.currentDrawModel, model.dragType != .none else { return }
        self.p_dragMovePointEnd(model)
        
        if self.isRepeatDraw == false {
            self.clearSelectDrawModel()
        }
        self.contentView?.reloadData()
        self.contentView?.closeMagnifierView()
    }
}

// MARK: - 数据变换
extension KLineDrawViewModel {
    /// 清除用户数据
    public func clear() {
        // 移除未完成数据
        if let m = self.currentDrawModel {
            if m.status == .drawPoint {
                self.drawModelList.removeAll(where: {$0.id == m.id})
            } else {
                m.status = .none
            }
            self.reloadContentView()
        }
        
        self.needDrawKLineModels = nil
        
        self.isRepeatDraw = false
        
        self.currentDrawModel = nil
        self.drawShapeType = nil
        self.dragPoint = .zero
        self.contentView = nil
        
    }
    
    /// 销毁对象
    public func destory() {
        self.clear()
    }
    
    /// 读取数据
    public func updateDrawSymbol(_ symbol: String?) {
        guard let s = symbol else { return }
        
        self.curSymbol = s
        
        
        self.loadDrawShape()
    }
    
    public func loadDrawShape() {
        guard let s = self.curSymbol else { return }
        
        // 获取老版本历史数据
        let key = "\(KLineDrawShapeKey)_\(s)"
        
        self.drawModelList.removeAll()
        let decoder = JSONDecoder()
        if let data = UDTool.value(key: key) as? Data{
            if let models = try? decoder.decode([KLineDrawModel].self, from: data){
                ///读取的时候将没个点设置为默认状态(保存的时候最后一条为select,到时加载的时候 最后一条线为编辑状态)
                models.forEach({$0.status = .none})
                self.drawModelList.append(contentsOf: models)
                print("读取当前划线缓存 成功！！\(models.count)")
            }
        }else{
            print("读取当前划线缓存类型 失败！！")
        }
        guard !self.drawModelList.isEmpty else { return }
        
        // 当前time line
        reloadKlineTimeData()
    }
    
    /// 保存数据
    public func saveDrawShape() {
        guard let s = self.curSymbol else {
            return
        }
        
        let key = "\(KLineDrawShapeKey)_\(s)"
        
        
        if self.drawModelList.isEmpty == true {
            UDTool.remove(key: key)
        } else {
            if let data = try? JSONEncoder().encode(self.drawModelList){
                UDTool.setValue(value: data, forKey: key)
                print("保存划线路径成功！")
            }else{
                print("保存划线路径失败！")
            }
        }
    }
    
    /// 保存设置
    public func saveDrawParam() {
        if let data = try? JSONEncoder().encode(self.drawParam){
            UDTool.setValue(value: data, forKey: KLineDrawShapeParamKey)
        }
        
    }
    
    /// 读取设置
    public func loadDrawParam() {
        guard let data = UDTool.value(key: KLineDrawShapeParamKey) as? Data,
              let param = try? JSONDecoder().decode(KLineDrawParam.self, from: data) else {
            return
        }
        self.drawParam = param
    }
    
    /// 清理当前选中数据
    public func clearSelectDrawModel() {
        if let m = currentDrawModel, m.status == .drawPoint {
            self.drawModelList.removeAll(where: {$0.id == m.id})
        }
        self.currentDrawModel?.status = .none
        self.currentDrawModel = nil
        
        if self.drawStatus == .drawShape {
            self.drawStatus = .tapShape
        }
        self.reloadContentView()
        notificationDelegate?.didClearCurrentDrawModel()
    }
    
    /// 更新当前选中数据
    public func selectDrawModel(_ model: KLineDrawModel) {
        self.drawStatus = .drawShape
        self.currentDrawModel = model
        self.reloadContentView()
        
        // 更新当前画线参数
        drawParam.drawLineDashes = model.lineDashes
        drawParam.drawThemColor = model.fillColor
        drawParam.drawLineWidth = model.lineThickness
    }
    
    /// 创建新的画线对象
    public func createNewDrawModel(_ type: KLineDrawShapeType) {
        // 删除当前没有画完数据
        if let m = currentDrawModel, m.status == .drawPoint {
            self.drawModelList.removeAll(where: {$0.id == m.id})
            self.reloadContentView()
        }
        self.drawStatus = .drawShape
        self.drawShapeType = type
        self.currentDrawModel = nil
        
        self.p_updateDrawHeaderTips()
    }
    
    /// 删除当前形状
    public func removeDrawModel(_ model: KLineDrawModel) {
        if self.currentDrawModel?.id == model.id {
            self.currentDrawModel = nil
        }
        self.drawModelList.removeAll(where: {$0.id == model.id})
        self.reloadContentView()
        self.drawStatus = .tapShape
        self.p_hiddenDrawTips()
        
        self.p_showDeleteSuccessTips()
        
        // 保存数据
        saveDrawShape()
    }
    
    /// 删除所有形状
    public func removeAlllDrawModel() {
        self.currentDrawModel = nil
        self.drawModelList.removeAll()
        self.reloadContentView()
        self.p_updateDrawHeaderTips()
        
        // 不是连续绘制中，不允许绘制
        if self.isRepeatDraw == false {
            self.drawStatus = .tapShape
            // 隐藏tips
            self.p_hiddenDrawTips()
        }
        
        // 保存数据
        saveDrawShape()
    }
    
    /// 更新画线设置
    public func updateDrawParam() {
        self.saveDrawShape()
        self.reloadContentView()
    }
    
    /// 更新内容view
    public func reloadContentView() {
        self.contentView?.reloadData()
    }
}

// MARK: - 坐标变换
extension KLineDrawViewModel {
    /// 二分方式查找最近的位置
    public func closestValue(_ arr: [Double], _ target: Double) -> Int {
        // If array has only 1 element, that element is the closest
        guard arr.count > 1 else { return 0 }

        // If the target is outside of the range of the array,
        // return the edges of the array
        // swiftlint:disable:next all
        guard arr.first! <= target else { return 0 }
        // swiftlint:disable:next all
        guard target <= arr.last! else { return arr.count - 1 }

        // Now some actual searching
        var left = 0
        var right = arr.count - 1

        while left < right {
            if left == right - 1 {
                return abs(arr[left] - target) <= abs(arr[right] - target) ? left : right
            }

            let middle = (left + right) / 2
            switch arr[middle] {
            case target:
                return middle
            case ..<target:
                left = middle
            default:
                right = middle
            }
        }

        return 0
    }
    
    /// 计算坐标，像素坐标->(时间,价格)
    public func calculationModelPoint(_ pt: CGPoint) -> KLineDrawPostion? {
        guard let models = self.needDrawKLineModels, models.isEmpty == false else { return nil }
        
        // 高度值
        let valueOffset = self.maxValue - self.minValue
        guard valueOffset > 0 else { return nil }
        
        let pointArray = models.map { Double($0.positionModel?.highPoint.x ?? 0) }
        
        let index = closestValue(pointArray, pt.x)
        
        guard index >= 0, index < models.count,
              let indexPostionModel = models[index].positionModel else { return nil }
        let offsetX = models[index].timestamp
        // 宽度值
        //MARK: Michael: 没搞明白为啥需要设这样的值 滑动到临界值时才不会变形
        let widthUnitValue = CGFloat(5 * 60 * 1000) / (KLineConfig.kLineGap + KLineConfig.kLineWidth)
        
        // 通过和子参考，计算出当前时间坐标
        let x = String(((pt.x - indexPostionModel.highPoint.x) * widthUnitValue) + offsetX)
        
        // 通过和价格参考，计算出当前价格坐标
        let minY = KLineConfig.kLineMainViewMinY
        let maxY = self.klineViewSize.height - KLineConfig.kLineMainViewMaxY

        let unitValue = valueOffset / Double(maxY - minY)
        let price = (maxY - pt.y) * unitValue + minValue
        
        let y: String = "\(price)"
        
        return KLineDrawPostion(x: x, y: y)
    }
    
    /// 计算坐标，(时间,价格)->像素坐标
    public func convertModelPoint(_ klineDrawPostion: KLineDrawPostion?) -> CGPoint? {
        guard let models = self.needDrawKLineModels,
              models.isEmpty == false,
              let drawPostion = klineDrawPostion  else { return nil }
        
        // 高度值
        let valueOffset = self.maxValue - self.minValue
        guard valueOffset > 0 else { return nil }
        
        let pointArray = models.map { $0.timestamp }
        
        let index = closestValue(pointArray, Double(drawPostion.x) ?? 0)
        
        guard index >= 0, index < models.count,
              let indexPostionModel = models[index].positionModel
               else { return nil }
        let offsetX = models[index].timestamp
        // 通过和价格参考，计算出当前价格坐标
        let minY = KLineConfig.kLineMainViewMinY
        let maxY = self.klineViewSize.height - KLineConfig.kLineMainViewMaxY
      
        let unitValue = valueOffset / Double(maxY - minY)
        //MARK: Michael: 没搞明白为啥需要设这样的值 滑动到临界值时才不会变形
        let currentTimeUnit = 5 * 60 * 1000
        
        // 宽度值
        let widthUnitValue = (KLineConfig.kLineWidth + KLineConfig.kLineGap) / CGFloat(currentTimeUnit)
    
        var point: CGPoint?
        if let value = Double(drawPostion.y), let time = Double(drawPostion.x) {
            let x = indexPostionModel.openPoint.x + CGFloat((time - offsetX)) * widthUnitValue
            let y = maxY - ((value - minValue) / unitValue)
            point = CGPoint(x: x, y: y)
        }
        return point
    }
    
    public func updateKLineModels(_ v: KLineDrawView) {
        guard self.curSymbol != nil else { return }
        self.contentView = v
        guard !self.drawModelList.isEmpty, let models = self.needDrawKLineModels, !models.isEmpty else { return }
        
        var update = false
        for drawModel in self.drawModelList {
            if drawModel.status == .drawPoint || drawModel.dragType != .none {
                continue
            }
            var pointList: [CGPoint] = []
            for postion in drawModel.postionList {
                if let point = convertModelPoint(postion) { pointList.append(point) }
            }
            drawModel.keyPointList = pointList
            drawModel.reloadData()
            update = true
        }
        guard update == true else { return }
        self.reloadContentView()
    }
}

// MARK: - 通知状态改变
// swiftlint:disable:next all
public let KLineDrawModeDidChangedNotification = "noti.kline.draw.didChanged"
// swiftlint:disable:next all
public let KLineDrawModeDidLoadNotification = "noti.kline.draw.didLoad"

extension KLineDrawViewModel {
    public func checkContainsPoint(_ pt: CGPoint) -> KLineDrawModel? {
        guard self.hiddenDraw == false, self.drawStatus == .tapShape else { return nil}
        
        for model in self.drawModelList {
            // swiftlint:disable:next all
            if model.status == .selected {
                model.status = .none
            }
        }
        for model in self.drawModelList {
            // swiftlint:disable:next all
            if model.containsPath(pt) {
                model.status = .selected
                return model
            }
        }
        return nil
    }
}

// MARK: - Tips
extension KLineDrawViewModel {
    private func p_updateDrawHeaderTips() {
        guard let delegate = self.notificationDelegate else { return }
        
        guard let type = self.drawShapeType else {
            delegate.closeHeaderTipsView()
            return
        }
        let totalCount = type.totalPointCount()
        let drawCount = self.currentDrawModel?.drawPointCount() ?? 0
        guard drawCount < totalCount else {
            delegate.closeHeaderTipsView()
            return
        }
        
        let title = type.selectedTitle.BPLocalized()
        let detail = FORMAT("ob_chart_drawing_guid_tips".BPLocalized(), String(totalCount))
        delegate.showHeaderTipsView(title:title, detail:"\(detail)(\(drawCount)/\((totalCount)))")
    }
    private func p_showRepeatDrawTips() {
        guard let delegate = self.notificationDelegate else { return }
        let title = self.isRepeatDraw ? "ob_chart_open_continuous_drawing" : "ob_chart_close_continuous_drawing"
        delegate.showCenterTipsView(title:title.BPLocalized())
    }
    
    private func p_showDrawCompleteTips() {
        guard let delegate = self.notificationDelegate else { return }
        delegate.showCenterTipsView(title: "ob_common_completed".BPLocalized())
    }
    
    private func p_showDeleteSuccessTips() {
        guard let delegate = self.notificationDelegate else { return }
        delegate.showCenterTipsView(title: "ob_delete_success".BPLocalized())
    }
    
    private func p_hiddenDrawTips() {
        guard let delegate = self.notificationDelegate else { return }
        delegate.closeHeaderTipsView()
        delegate.closeCenterTipsView()
    }
    
    public func showErrorPointTips() {
        guard let delegate = self.notificationDelegate else { return }
        delegate.showCenterTipsView(title: "ob_chart_drawing_click_out_tips".BPLocalized())
    }
}
