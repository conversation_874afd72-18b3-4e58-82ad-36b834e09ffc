//
//  KLineDrawManagerDelegate.swift
//  KLineDemo
//
//  Created by <PERSON> on 2025/3/26.
//

import Foundation

/// 用于通知划线功能相关事件
public protocol KLineDrawManagerDelegate{
    ///通知drawSettingView ReloadDrawModel
    func drawSettingViewReloadDrawModel()
    
    /// 用于通知关闭提示popup
    func closeHeaderTipsView()
    
    /// 用于通知显示提示popup
    func showHeaderTipsView(title:String,detail:String)
    
    /// 用于显示居中的提示popup
    func showCenterTipsView(title:String)
    //关闭
    func closeCenterTipsView()
    /// clear current draw model
    func didClearCurrentDrawModel()
}
