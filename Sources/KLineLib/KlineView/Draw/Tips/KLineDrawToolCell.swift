//
//  FILE.swift
//  Ourbit
//
//  Created by pippen on 2024.
//

import Foundation
import UIKit

open class KLineDrawlineView: UIView {
    /// 宽度
    public var lineWidth: CGFloat = 1 {
        didSet {
            self.setNeedsDisplay()
        }
    }
    
    /// 虚线
    public var lineDashes: [CGFloat] = [0, 0] {
        didSet {
            self.setNeedsDisplay()
        }
    }
    
    /// 颜色
    public var color: UIColor = KLineConfig.themeManager.textTertiaryColor() {
        didSet { self.setNeedsDisplay() }
    }

    public var offset: CGFloat = 0
    
    public init() {
        super.init(frame: .zero)
        self.backgroundColor = UIColor.clear
        // 不响应事件
        self.isUserInteractionEnabled = false
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    open override func layoutIfNeeded() {
        super.layoutIfNeeded()
        self.setNeedsDisplay()
    }
    
    open override func layoutSubviews() {
        super.layoutSubviews()
        self.setNeedsDisplay()
    }
    
    open override func draw(_ rect: CGRect) {
        super.draw(rect)
        
        guard let ctx = UIGraphicsGetCurrentContext() else { return }
        ctx.setStrokeColor(color.cgColor)
        ctx.setLineWidth(lineWidth)
        ctx.setLineDash(phase: 0, lengths: lineDashes)
        ctx.move(to: CGPoint(x: offset, y: self.bounds.size.height * 0.5))
        ctx.addLine(to: CGPoint(x: self.bounds.size.width - offset, y: self.bounds.size.height * 0.5))
        ctx.drawPath(using: .stroke)
    }
}

open class KLineDrawToolLineCell: UITableViewCell {
    open lazy var drawLineV: KLineDrawlineView = {
        let v = KLineDrawlineView()
        v.offset = 13
        return v
    }()
    
    public override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        p_setUp()
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    open func p_setUp() {
        selectionStyle = .none
        addSubview(drawLineV)
        drawLineV.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
}

public class KLineDrawFunctionCell: UITableViewCell {
    public lazy var statusL: UILabel = {
        let v = UILabel().textFont(.systemFont(ofSize: 5))
            .textColor(KLineConfig.themeManager.klineLibSubTextColor())
                         //.text(IconFont.Other.arrowDrawKLine) // 绘制图RTL也是放在右边故箭头使用rawValue而非flipsValue
        return v
    }()
    
    public lazy var iconL: UILabel = {
        let v = UILabel().textFont(.systemFont(ofSize: 22))
                         .textColor(KLineConfig.themeManager.klineLibSubTextColor())
                         .text("\u{e6db}")
        v.textAlignment = .center
        return v
    }()
    
    public lazy var lineView: UIView = {
        let v = UIView()
        v.backgroundColor = KLineConfig.themeManager.dividerColor()
        return v
    }()
    
    public override var isSelected: Bool {
        didSet {
            if isSelected {
                iconL.textColor = KLineConfig.themeManager.klineLibDefaultTextColor()
                statusL.textColor = KLineConfig.themeManager.klineLibDefaultTextColor()
                //statusL.text = IconFont.Other.arrowDrawKLine
            } else {
                iconL.textColor = KLineConfig.themeManager.klineLibSubTextColor()
                statusL.textColor = KLineConfig.themeManager.klineLibSubTextColor()
                //statusL.text = IconFont.Other.arrowDrawKLine
            }
        }
    }
    
    override public init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        p_setUp()
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    public func p_setUp() {
        selectionStyle = .none
        contentView.addSubview(iconL)
        iconL.snp.makeConstraints { make in
            make.width.height.equalTo(20)
            make.center.equalToSuperview()
        }
        contentView.addSubview(statusL)
        statusL.snp.makeConstraints { make in
            make.width.height.equalTo(5)
            make.right.equalTo(iconL.snp.left).offset(-5)
            make.centerY.equalTo(iconL)
        }
        contentView.addSubview(lineView)
        lineView.snp.makeConstraints { make in
            make.width.equalTo(20)
            make.height.equalTo(1)
            make.center.equalToSuperview()
        }
    }
}

public class KLineDrawToolCell: UITableViewCell {
    public lazy var textL: UILabel = {
        let v = UILabel().textFont(UIFont.systemFont(ofSize: 11, weight: .medium))
                         .textColor(KLineConfig.themeManager.klineLibSubTextColor())
                         .text("Line Segment")
        return v
    }()
    
    public lazy var iconL: UILabel = {
        let v = UILabel().textFont(.systemFont(ofSize: 22))
                         .textColor(KLineConfig.themeManager.klineLibSubTextColor())
                         .text("\u{e6db}")
        v.textAlignment = .center
        return v
    }()
    
    override public init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        p_setUp()
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    public func p_setUp() {
        selectionStyle = .none
        addSubview(textL)
        textL.snp.makeConstraints { make in
            make.leading.equalTo(34)
            make.centerY.equalToSuperview()
        }
        addSubview(iconL)
        iconL.snp.makeConstraints { make in
            make.width.height.equalTo(20)
            make.leading.equalToSuperview().offset(8)
            make.centerY.equalToSuperview()
        }
    }
}
