//
//  FILE.swift
//  Ourbit
//
//  Created by pippen on 2024.
//

import Foundation
import UIKit

#if canImport(SwiftUI)
import SwiftUI

public enum KLineDrawTips {
    public struct TitleDetail {
        let title: String
        let detail: String
        public init(title: String, detail: String) {
            self.title = title
            self.detail = detail
        }
    }
}

extension View {
    @available(iOS 15, *)
    public func kLineDrawHeaderTipsView(tips: Binding<KLineDrawTips.TitleDetail?>) -> some View {
        self.modifier(KLineDrawHeaderTipsSwiftUIView(tips: tips))
    }
    
    @available(iOS 15, *)
    public func kLineDrawCenterTipsView(tips: Binding<KLineDrawTips.TitleDetail?>) -> some View {
        self.modifier(KLineDrawCenterTipsSwiftUIView(tips: tips))
    }
}


@available(iOS 15, *)
struct KLineDrawHeaderTipsSwiftUIView: ViewModifier {
    @Binding var tips: KLineDrawTips.TitleDetail?
    init(tips: Binding<KLineDrawTips.TitleDetail?>) {
        self._tips = tips
    }
    func body(content: Content) -> some View {
        content
            .overlay(alignment: .top) {
                _KLineDrawHeaderTipsSwiftUIView(tips: $tips)
                    .fixedSize()
            }
    }
}

struct _KLineDrawHeaderTipsSwiftUIView: UIViewRepresentable {
    
    typealias UIViewType = KLineDrawHeaderTipsView
    @Binding var tips: KLineDrawTips.TitleDetail?
    init(tips: Binding<KLineDrawTips.TitleDetail?>) {
        self._tips = tips
    }
    func makeUIView(context: Context) -> KLineDrawHeaderTipsView {
        let view = KLineDrawHeaderTipsView()
        if let tips {
            view.titleL.text = tips.title
            view.detailL.text = tips.detail
            view.isHidden = false
        } else {
            view.isHidden = true
        }
        return view
    }
    func updateUIView(_ uiView: KLineDrawHeaderTipsView, context: Context) {
        if let tips  {
            uiView.titleL.text = tips.title
            uiView.detailL.text = tips.detail
            uiView.isHidden = false
        } else {
            uiView.isHidden = true
        }
    }
}

@available(iOS 15, *)
struct KLineDrawCenterTipsSwiftUIView: ViewModifier {
    @Binding var tips: KLineDrawTips.TitleDetail?
    init(tips: Binding<KLineDrawTips.TitleDetail?>) {
        self._tips = tips
    }
    func body(content: Content) -> some View {
        content
            .overlay(alignment: .center) {
                _KLineDrawCenterTipsSwiftUIView(tips: tips)
                    .fixedSize()
            }
    }
}

struct _KLineDrawCenterTipsSwiftUIView: UIViewRepresentable {
    typealias UIViewType = KLineDrawCenterTipsView
    var tips: KLineDrawTips.TitleDetail?
    func makeUIView(context: Context) -> KLineDrawCenterTipsView {
        let view = KLineDrawCenterTipsView()
        if let title = tips?.title {
            view.titleL.text = title
            view.isHidden = false
        } else {
            view.isHidden = true
        }
        return view
    }
    func updateUIView(_ uiView: KLineDrawCenterTipsView, context: Context) {
        if let title = tips?.title {
            uiView.titleL.text = title
            uiView.isHidden = false
        } else {
            uiView.isHidden = true
        }
    }
}

#endif

public class KLineDrawHeaderTipsView: UIView {
    public lazy var titleL: UILabel = {
        let v = UILabel().textFont(UIFont.systemFont(ofSize: 13, weight: .medium))
                         .textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
                         .text("Line Segment")
                         .textAlignment(.center)
        return v
    }()
    
    public lazy var detailL: UILabel = {
        let v = UILabel().textFont(UIFont.systemFont(ofSize: 12, weight: .medium))
                         .textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
                         .text("aaaaaaaa")
                         .textAlignment(.center)
        return v
    }()
    
    public init() {
        super.init(frame: .zero)
        p_setUp()
        changeTheme()
        CusstomNotification.observe(self, Noti.Data.klineThemeSkinDidChanged) { [weak self] _ in
            guard let self = self else { return }
            self.changeTheme()
        }
    }
    
    public required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    public func changeTheme(){
        self.backgroundColor = KLineConfig.themeManager.bgCard()
        self.titleL.textColor = KLineConfig.themeManager.klineLibDefaultTextColor()
        self.detailL.textColor = KLineConfig.themeManager.klineLibDefaultTextColor()
    }
    public func p_setUp() {
        self.backgroundColor = KLineConfig.themeManager.bgCard()
        
        self.cornerRadius = 4
        self.clipsToBounds = false
        self.setShadow(sColor: UIColor.black,
                       offset: CGSize.zero,
                       opacity: KlineLibThemeManager.currentTheme == .dark ? 0.2 : 0.12,
                       radius: 6)
        
        self.addSubview(titleL)
        self.addSubview(detailL)
        titleL.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(6)
            make.leading.greaterThanOrEqualTo(24)
            make.trailing.lessThanOrEqualTo(-24)
            make.centerX.equalToSuperview()
        }
        detailL.snp.makeConstraints { make in
            make.top.equalTo(titleL.snp.bottom).offset(2)
            make.leading.greaterThanOrEqualTo(24)
            make.trailing.lessThanOrEqualTo(-24)
            make.bottom.equalToSuperview().offset(-6)
            make.centerX.equalToSuperview()
        }
    }
    public override var intrinsicContentSize: CGSize {
        let titleSize = titleL.intrinsicContentSize
        let detailSize = detailL.intrinsicContentSize

        let contentHeight = 6 + titleSize.height + 2 + detailSize.height + 6

        let maxLabelWidth = max(titleSize.width, detailSize.width)

        // Add a small safety buffer (e.g., 1 or 2 points) beyond the minimum padding
        // Sometimes needed for rounding or subtle layout differences. Adjust if necessary.
        let horizontalPadding: CGFloat = 84
        let horizontalBuffer: CGFloat = 1 // Experiment with 1 or 2 if needed

        let contentWidth = horizontalPadding + maxLabelWidth + horizontalPadding + (horizontalBuffer * 2)

        return CGSize(width: max(0, contentWidth), height: max(0, contentHeight))
    }
    deinit{
        CusstomNotification.remove(self, Noti.Data.klineThemeSkinDidChanged)
    }
}
public class KLineDrawCenterTipsView: UIView {
    public lazy var titleL: UILabel = {
        let l = UILabel().textFont(.systemFont(ofSize: 14, weight: .medium))
            .textColor(KLineConfig.themeManager.klineLibDefaultTextReversalColor())
            .text("Line Segment")   
            .textAlignment(.center)
        return l
    }()
    
    public init() {
        super.init(frame: .zero)
        translatesAutoresizingMaskIntoConstraints = false
        _makeViews()
    }
    
    public required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    public func _makeViews() {
        backgroundColor = KLineConfig.themeManager.fillToastColor()
        cornerRadius = 4.0
        
        self.addSubview(titleL)
        titleL.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(8)
        }
    }
    
    public override var intrinsicContentSize: CGSize {
        let titleSize = titleL.intrinsicContentSize
        return CGSize(width: max(0, titleSize.width + 16), height: max(0, titleSize.height + 16))
    }
}
