//
//  FILE.swift
//  Ourbit
//
//  Created by pippen on 2024.
//

import Foundation
import UIKit

/// 记录(时间,价格)坐标
public struct KLineDrawPostion: Codable {
    public var x: String = ""
    
    public var y: String = ""
}

public enum KLineDrawModelDrag: Int,Codable {
    /// 默认情况
    case none // swiftlint:disable:this all
    /// 移动
    case moveShape
    /// 拖动点
    case dragPoint
}

open class KLineDrawModel: Codable {
    public var id = UUID().uuidString
    /// 价格坐标
    public var postionList: [KLineDrawPostion] = []
    
    /// 像素坐标
    public var keyPointList: [CGPoint] = []
    
    /// 画线点位
    public var drawPointList: [CGPoint] = []
    
    /// 当前选中Index
    public var pointIndex: Int? = nil
    
    /// 坐标转换，用于移动时候
    public var transformPoint = CGPoint.zero
    
    /// 画线设置
    public var lineThickness: CGFloat = 1
    
    /// 虚线
    public var lineDashes: [CGFloat] = []
    
    public var status: KLineDrawShapeStatus = .none
    
    public var fillColor:String = ""// = UIColor.red
    
    public var dragType: KLineDrawModelDrag = .none
    
    /// 是否锁定
    public var lockDraw = false
    
    public var drawShapeType: KLineDrawShapeType = .segmentLine
    
    /// 是否需要更新价格
    public var needUpdatePrice: Bool { drawShapeType == .priceLine || drawShapeType == .fibonacci }
    
    /// 平行通道拖动固定点
    public var pinPointIndex: Int?
    
    /// 斐波那契数列
    public let fibonacciValueList = ["0.0", "0.236", "0.382", "0.5", "0.618", "0.786", "1"]
    
    // swiftlint:disable:next all
    
    
    public init(type: KLineDrawShapeType, pt: CGPoint,klinePoint: KLineDrawPostion) {
        
        self.keyPointList.append(pt)
        self.postionList.append(klinePoint)
        self.drawShapeType = type
        if type == .horizontalLine || type == .priceLine {
            self.status = .selected
        } else {
            self.status = .drawPoint
        }
        self.pointIndex = 0
        self.reloadData()
    }
    
    public func containsPolygon(polygon: [CGPoint], test: CGPoint) -> Bool {
        // swiftlint:disable:next all
        var pJ = polygon.last!
        var contains = false
        for pI in polygon {
            // swiftlint:disable:next all
          if (((pI.y >= test.y) != (pJ.y >= test.y)) &&
              (test.x <= (pJ.x - pI.x) * (test.y - pI.y) / (pJ.y - pI.y) + pI.x)) {
              contains.toggle()
          }
          pJ = pI
        }
        return contains
    }
    
    public func distanceFromPoint(p: CGPoint, toLineSegment v: CGPoint, and w: CGPoint) -> CGFloat {
        let pv_dx = p.x - v.x
        let pv_dy = p.y - v.y
        let wv_dx = w.x - v.x
        let wv_dy = w.y - v.y

        let dot = pv_dx * wv_dx + pv_dy * wv_dy
        let len_sq = wv_dx * wv_dx + wv_dy * wv_dy
        let param = dot / len_sq

        var int_x, int_y: CGFloat /* intersection of normal to vw that goes through p */

        if param < 0 || (v.x == w.x && v.y == w.y) {
            int_x = v.x
            int_y = v.y
        } else if param > 1 {
            int_x = w.x
            int_y = w.y
        } else {
            int_x = v.x + param * wv_dx
            int_y = v.y + param * wv_dy
        }

        /* Components of normal */
        let dx = p.x - int_x
        let dy = p.y - int_y

        return sqrt(dx * dx + dy * dy)
    }
    
    public func containsSegment(start: CGPoint, end: CGPoint, test: CGPoint) -> Bool {
        return distanceFromPoint(p: test, toLineSegment: start, and: end) < 20
    }
    
    /// 是否包含Point
    public func containsPath(_ test: CGPoint) -> Bool {
        guard !drawPointList.isEmpty else { return false }
        
        let p1 = drawPointList[0]
        let p2 = drawPointList.count > 1 ? drawPointList[1] : p1
        
        switch drawShapeType {
        case .parallelogram, .rectangle, .parallelChannel, .fibonacci:
            return containsPolygon(polygon: drawPointList, test: test)
        case .threeWaves, .fiveWaves:
            if drawPointList.count > 1 {
                var endPt = p2
                var startPt = p1
                for i in 1..<drawPointList.count {
                    endPt = drawPointList[i]
                    if containsSegment(start: startPt, end: endPt, test: test) {
                        return true
                    }
                    startPt = endPt
                }
            } else {
                return containsSegment(start: p1, end: p2, test: test)
            }
        default:
            return containsSegment(start: p1, end: p2, test: test)
        }
        return false
    }
    /// 是否控制点，用于拖动
    public func containControlPoint(_ p: CGPoint) -> Bool {
        for point in self.keyPointList {
            let d = hypot(p.x - point.x, p.y - point.y)
            if d < 30 { return true }
        }
        return false
    }
    
    public func containSelectPoint(_ p: CGPoint) -> Bool {
        return self.containControlPoint(p) || self.containsPath(p)
    }
    
    public func drawPointCount() -> Int {
        return self.keyPointList.count
    }
    
    public func updateKeyPointIndex(_ p: CGPoint) {
        var i = 0
        for point in self.keyPointList {
            let d = hypot(p.x - point.x, p.y - point.y)
            if d < 30 {
                self.pointIndex = i
                return
            }
            i += 1
        }
    }
    
    /// reload数据
    public func reloadData() {
        guard !self.keyPointList.isEmpty else { return }
        // 将偏移设置到点位上
        let transform = CGAffineTransform(translationX: self.transformPoint.x, y: self.transformPoint.y)
        
        let pointList = self.keyPointList.map { $0.applying(transform) }
        self.keyPointList = pointList
        
        let p1 = pointList[0]
        let p2 = pointList.count > 1 ? pointList[1] : p1
        let p3 = pointList.count > 2 ? pointList[2] : p2

        let canvasWidth = KLineDrawViewModel.shared.klineViewSize.width
        
        switch drawShapeType {
        case .segmentLine:
            if pointList.count > 1 {
                self.drawPointList = [p1, p2]
            }
        case .radialLine:
            if pointList.count > 1 {
                // 计算斜率
                let k = (p2.x - p1.x) == 0 ? 0 : (p2.y - p1.y) / (p2.x - p1.x)
                let b = p2.y - k * p2.x
                var p3: CGPoint = .zero
                if p2.x >= p1.x {
                    p3.x = canvasWidth * 2
                    p3.y = p3.x * k + b
                } else {
                    p3.x = 0
                    p3.y = b
                }
                self.drawPointList = [p1, p3]
            }
        case .verticalLine:
            if pointList.count > 1 {
                self.drawPointList = [p1, p2]
            }
        case .priceLine:
            let p3 = CGPoint(x: canvasWidth * 2, y: p1.y)
            self.drawPointList = [p1, p3]
        case .horizontalLine:
            let p3 = CGPoint(x: canvasWidth * 2, y: p1.y)
            self.drawPointList = [CGPoint(x: -canvasWidth, y: p1.y), p3]
        case .parallelogram:
            if pointList.count == 2 {
                self.drawPointList = [p1, p2]
            } else if pointList.count > 2 {
                let p4 = CGPoint(x: p1.x + p3.x - p2.x, y: p1.y + p3.y - p2.y)
                self.drawPointList = [p1, p2, p3, p4]
            }
        case .rectangle:
            if pointList.count > 1 {
                self.drawPointList.removeAll()
                self.drawPointList.append(p1)
                self.drawPointList.append(CGPoint(x: p1.x, y: p2.y))
                self.drawPointList.append(p2)
                self.drawPointList.append(CGPoint(x: p2.x, y: p1.y))
            }
        case .parallelChannel:
            if pointList.count == 2 {
                self.drawPointList = [p1, p2]
            } else if pointList.count > 2 {
                self.p_reloadParallelChannel()
            }
        case .fibonacci:
            if pointList.count > 1 {
                self.drawPointList.removeAll()
                self.drawPointList.append(p1)
                self.drawPointList.append(CGPoint(x: p1.x, y: p2.y))
                self.drawPointList.append(p2)
                self.drawPointList.append(CGPoint(x: p2.x, y: p1.y))
            }
        case .threeWaves:
            self.drawPointList = pointList
        case .fiveWaves:
            self.drawPointList = pointList
        }
        // 重置偏移
        self.transformPoint = CGPoint.zero
    }
    
    /// 更新关键点坐标
    public func updateKeyPoint(_ pt: CGPoint) {
        guard let index = self.pointIndex else { return }
        guard self.status == .drawPoint || self.dragType == .dragPoint else { return }
        
        if self.drawShapeType == .parallelChannel {
            self.p_updateParallelChannelKeyPoint(pt)
        } else {
            if index >= 0, index < self.keyPointList.count {
                self.keyPointList[index] = pt
            } else {
                //LoggerTool.warning(tag: LogType.kline, "[Draw] index out array \(index) \(self.keyPointList)")
            }

            if self.drawShapeType == .verticalLine {
                self.keyPointList = self.keyPointList.map { CGPoint(x: pt.x, y: $0.y) }
            }
        }
        
        // 画点数量完成
        if self.drawPointCount() == self.drawShapeType.totalPointCount() {
            self.status = .selected
        } else {
            self.status = .drawPoint
        }
    }
    
    public func addKeyPoint(_ pt: CGPoint, _ postion: KLineDrawPostion) {
        guard self.drawPointCount() < self.drawShapeType.totalPointCount() else { return }
        
        // 平行通道需要计算第三个点
        if self.drawShapeType == .parallelChannel {
            self.p_addParallelChannelKeyPoint(pt, postion)
        } else if self.drawShapeType == .verticalLine {
            // 垂直线
            if !self.keyPointList.isEmpty {
                let p = CGPoint(x: self.keyPointList[0].x, y: pt.y)
                let vp = KLineDrawPostion(x: self.postionList[0].x, y: postion.y)
                self.keyPointList.append(p)
                self.postionList.append(vp)
            } else {
                self.keyPointList.append(pt)
                self.postionList.append(postion)
            }
        } else {
            self.keyPointList.append(pt)
            self.postionList.append(postion)
        }
        self.reloadData()
    }
    
    /// 更新当前状态
    public func updateStatus() {
        // 画点数量完成
        if self.drawPointCount() == self.drawShapeType.totalPointCount() {
            self.status = .selected
        } else {
            self.status = .drawPoint
        }
    }
    
//    func mapping(mapper: HelpingMapper) {
//        mapper.exclude(property: &keyPointList)
//        mapper.exclude(property: &pointIndex)
//        mapper.exclude(property: &transformPoint)
//        mapper.exclude(property: &drawPointList)
//        mapper.exclude(property: &pinPointIndex)
//        mapper.exclude(property: &status)
//        
//        mapper <<< drawShapeType <-- EnumTransform()
//        mapper <<< fillColor <-- HexColorTransform()
//        
//        // swiftlint:disable all
//        mapper <<< lineDashes <-- TransformOf(fromJSON: { (value) -> [CGFloat]? in
//            if let f = value {
//                return f.map{ CGFloat($0) }
//            }
//            return nil
//        }, toJSON: { (value) -> [Float]? in
//            if let f = value {
//                return f.map{ Float($0) }
//            }
//            return nil
//        })
//        
//        mapper <<< lineThickness <-- TransformOf(fromJSON: { (value) -> CGFloat? in
//            if let f = value {
//                return CGFloat(f)
//            }
//            return nil
//        }, toJSON: { (value) -> Float? in
//            if let f = value {
//                return Float(f)
//            }
//            return nil
//        })
//        // swiftlint:enable all
//    }
}

// MARK: - 多边形处理
extension KLineDrawModel {
    /// 添加平行通道点
    private func p_addParallelChannelKeyPoint(_ pt: CGPoint, _ postion: KLineDrawPostion) {
        // 最后一个点需要转换
        if self.drawPointCount() == self.drawShapeType.totalPointCount() - 1 {
            let p1 = self.keyPointList[0]
            let p2 = self.keyPointList[1]
            
            // 计算斜率
            let k = (p2.x - p1.x) == 0 ? 0 : (p2.y - p1.y) / (p2.x - p1.x)
            let b = pt.y - k * pt.x
            let x = (p1.x + p2.x) * 0.5
            
            // 计算关键点
            let keyPoint = CGPoint(x: x, y: x * k + b)
            self.keyPointList.append(keyPoint)
            
            // 计算对应价格坐标
            if let keyPostion = KLineDrawViewModel.shared.calculationModelPoint(keyPoint) {
                self.postionList.append(keyPostion)
            }
        } else {
            self.keyPointList.append(pt)
            self.postionList.append(postion)
        }
    }
    
    /// 更新平行通道关键点
    private func p_updateParallelChannelKeyPoint(_ pt: CGPoint) {
        guard let index = self.pointIndex,
              index >= 0,
              index < self.keyPointList.count else {
            // swiftlint:disable:next all
//            OBLogger.warning(tag: OBLogType.kline, "[Draw] channel index out array \(index) \(self.keyPointList)")
            return
        }
        if index != self.drawShapeType.totalPointCount() - 1 {
            self.keyPointList[index] = pt
            if index == 0 {
                self.pinPointIndex = 2
            } else {
                self.pinPointIndex = 3
            }
        } else {
            self.keyPointList[index] = CGPoint(x: self.keyPointList[index].x, y: pt.y)
        }
    }
    
    private func p_reloadParallelChannel() {
        guard keyPointList.count == self.drawShapeType.totalPointCount() else { return }
        
        let p1 = keyPointList[0]
        let p2 = keyPointList[1]
        
        // 计算斜率
        let k = (p2.x - p1.x) == 0 ? 0 : (p2.y - p1.y) / (p2.x - p1.x)
        
        // 根据固定点，计算常量
        var b: CGFloat = 0
        if let pin = self.pinPointIndex, pin < drawPointList.count {
            b = drawPointList[pin].y - drawPointList[pin].x * k
        } else {
            b = keyPointList[2].y - keyPointList[2].x * k
        }
        
        let x = p2.x * 0.5 + p1.x * 0.5
        let cPoint = CGPoint(x: x, y: x * k + b)
        let p3 = CGPoint(x: p2.x, y: p2.x * k + b)
        let p4 = CGPoint(x: p1.x, y: p1.x * k + b)
        
        self.keyPointList[2] = cPoint
        self.drawPointList = [p1, p2, p3, p4]
        
        self.pinPointIndex = nil
    }
}

// MARK: - 画形状
extension KLineDrawModel {
    /// 线段
    public func linePath(from start: CGPoint,
                  to end: CGPoint) -> UIBezierPath {
        let vector = CGPoint(x: end.x - start.x, y: end.y - start.y)
        if vector == CGPoint.zero {
            return UIBezierPath(roundedRect: CGRect(origin: start, size: .zero), cornerRadius: 0)
        }
        
        let path = UIBezierPath()

        path.move(to: CGPoint(x: start.x, y: start.y))
        path.addLine(to: CGPoint(x: end.x, y: end.y))
        
        return path
    }
    
    /// 连续线段
    public func linePath(list: [CGPoint]) -> UIBezierPath {
        guard let start = list.first, list.count > 1 else {
            return UIBezierPath()
        }
    
        let path = UIBezierPath()

        path.move(to: CGPoint(x: start.x, y: start.y))
        for i in 1..<list.count {
            let pt = CGPoint(x: list[i].x, y: list[i].y)
            path.addLine(to: pt)
            path.move(to: pt)
        }
        return path
    }

    /// 多边形
    // swiftlint:disable:next all
    public func p_polyPath(_ pointList: [CGPoint]?) -> UIBezierPath? {
        guard let list = pointList else { return nil }
        let path = UIBezierPath()
        for p in list {
            if path.isEmpty {
                path.move(to: p)
            } else {
                path.addLine(to: p)
            }
        }
        path.close()
        return path
    }
    
    /// 画平行通道
    public  func p_drawParallelogramPath(_ ctx: CGContext, _ transform: CGAffineTransform) {
        if self.keyPointList.count == 2 {
            // 绘制线段
            let path = UIBezierPath()
            path.move(to: self.keyPointList[0])
            path.addLine(to: self.keyPointList[1])
            let color = UIColor(self.fillColor)
            color.set()
            path.stroke()
        } else if self.keyPointList.count == 3 {
            let pointList = self.drawPointList.map { $0.applying(transform) }
            let p1 = pointList[0]
            let p2 = pointList[1]
            let p3 = pointList[2]
            let p4 = pointList[3]
            
            // 填充
            let fillPath = UIBezierPath()
            fillPath.move(to: p1)
            fillPath.addLine(to: p2)
            fillPath.addLine(to: p3)
            fillPath.addLine(to: p4)
            let color = UIColor(self.fillColor)
            color.withAlphaComponent(0.2).setFill()
            fillPath.fill()
            
            // 画线
            let stroke = UIBezierPath()
            stroke.move(to: p1)
            stroke.addLine(to: p2)
            stroke.move(to: p3)
            stroke.addLine(to: p4)
            
            color.setStroke()
            stroke.setLineDash(self.lineDashes, count: self.lineDashes.count, phase: 0)
            stroke.lineWidth = self.lineThickness
            stroke.stroke()
            
            // 中间分割线
            let midStroke = UIBezierPath()
            midStroke.setLineDash([2, 2], count: 2, phase: 0)
            midStroke.move(to: CGPoint(x: p1.x, y: (p1.y + p4.y) * 0.5))
            midStroke.addLine(to: CGPoint(x: p2.x, y: (p2.y + p3.y) * 0.5))
            color.setStroke()
            midStroke.stroke()
        }
    }
    
    /// 画斐波那契
    public  func p_drawFibonacciPath(_ list: [CGPoint]) {
        guard list.count == 4, postionList.count == 2 else { return }
        
        let attributes: [NSAttributedString.Key: Any] = [
            .foregroundColor: self.fillColor.hexColor(),
            .font: UIFont.monospacedDigitSystemFont(ofSize: 10, weight: .regular)
        ]
        
        let height = abs(list[2].y - list[0].y)
        let width = abs(list[2].x - list[0].x)
        let y = max(list[2].y, list[0].y)
        let x = min(list[2].x, list[0].x)
        
        let priceHeight = abs(postionList[1].y.doubleValue() - postionList[0].y.doubleValue())
        let priceStart = min(postionList[1].y.doubleValue(), postionList[0].y.doubleValue())
        
        // 填充
        let fillPath = UIBezierPath(rect: CGRect(x: x, y: y - height, width: width, height: height))
        let color = UIColor(self.fillColor)
        color.withAlphaComponent(0.2).setFill()
        fillPath.fill()
        
        // 绘制线段
        let stokePath = UIBezierPath()
        color.setStroke()
        stokePath.setLineDash(self.lineDashes, count: self.lineDashes.count, phase: 0)
        stokePath.lineWidth = self.lineThickness
        for str in fibonacciValueList {
            let value = str.doubleValue()
            stokePath.move(to: CGPoint(x: x, y: y - value * height))
            stokePath.addLine(to: CGPoint(x: x + width, y: y - value * height))
            
            // 绘制值
            let showPrice = ShowNumber("\(priceStart + value * priceHeight)",
                                         .fullDown(KLineDrawViewModel.shared.pricesScale))
            let displayString = "\(str)(\(showPrice))"
            let attributeStr = NSAttributedString(string: displayString, attributes: attributes)
            let textSize = attributeStr.size()
            let pt = CGPoint(x: x - 4 - textSize.width, y: y - value * height - textSize.height * 0.5)
            attributeStr.draw(at: pt)
        }
        stokePath.stroke()
    }
    
    public func drawShape(_ ctx: CGContext, _ transform: CGAffineTransform) {
        guard !drawPointList.isEmpty else { return }
        
        let pointList = drawPointList.map { $0.applying(transform) }
        let p1 = pointList[0]
        let p2 = pointList.count > 1 ? pointList[1] : p1
        
        var drawPath: UIBezierPath?
        switch drawShapeType {
        case .parallelogram, .rectangle:
            drawPath = p_polyPath(pointList)
        case .parallelChannel:
            p_drawParallelogramPath(ctx, transform)
        case .fibonacci:
            p_drawFibonacciPath(pointList)
        case .threeWaves, .fiveWaves:
            drawPath = linePath(list: pointList)
        default:
            drawPath = linePath(from: p1, to: p2)
        }
        
        guard let path = drawPath else { return }
        path.setLineDash(lineDashes, count: lineDashes.count, phase: 0)
        path.lineWidth = lineThickness
        let color = UIColor(self.fillColor)
        color.set()
        path.stroke()
    }
}
