//
//  OBKLineDrawToolBoxType.swift
//  KLineDemo
//
//  Created by <PERSON> on 2025/3/25.
//

import Foundation
// 定义 OBKLineDrawToolBoxType 枚举
/// 功能类型
public enum KLineDrawToolBoxType: Int {
    /// 画线
    case drawLine
    /// 画形状
    case drawShape
    /// 连续画线
    case repeatDraw
    /// 分割线
    case divider
    /// 显示
    case showDraw
    /// 隐藏
    case hideDraw
    /// 清理
    case clean
    /// 分享
    case share
    /// 退出
    case exit
    /// 斐波那契
    case fibonacci
    /// 波浪
    case drawWave
    
    public func iconL() -> String {
        switch self {
        case .drawLine:
            return "\u{e802}"
        case .drawShape:
            return "\u{e7fd}"
        case .repeatDraw:
            return "\u{e7f6}"
        case .divider:
            return ""
        case .showDraw:
            return "\u{e80c}"
        case .hideDraw:
            return "\u{e7fb}"
        case .clean:
            return "\u{e7f4}"
        case .share:
            return "\u{e807}"
        case .exit:
            return "\u{e806}"
        case .fibonacci:
            return "\u{e82f}"
        case .drawWave:
            return "\u{e831}"
        }
    }
}
