//
//  FILE.swift
//  Ourbit
//
//  Created by pippen on 2024.
//

import Foundation
import UIKit

/// 画线设置类型
public enum KLineDrawSettingType: Int {
    /// 颜色
    case lineColor
    /// 粗细
    case lineWidth
    /// 类型
    case lineType
    /// 锁定
    case lineLock
    /// 删除
    case lineDelete
}

open class KLineDrawSettingView: UIView {
    /// 锁被锁图片
    let lockIcon: String = "\u{e7f9}"
    let unLockIcon: String = "\u{e80a}"
    
    let lockTag = 1
    let unLockTag = 0
    
    /// 线段颜色
    public lazy var colorList: [UIColor] = {
        return KLineConfig.themeManager.drawLineColorList()
    }()
    
    /// 线段粗细
    public lazy var lineWithList: [CGFloat] = {
        return [0.5, 1, 1.5, 2]
    }()
    
    /// 线段类型
    public lazy var lineTypeList: [[CGFloat]] = {
        return [[3, 1], [2, 2], [1, 1], [0, 0]]
    }()
    
    /// 列表数据
    public var dataList: [Any] = []
    
    /// 当前设置类型
    public var currentType: KLineDrawSettingType?
    
    public lazy var collection: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumLineSpacing = 10
        layout.minimumInteritemSpacing = 10
        layout.sectionInset = UIEdgeInsets(top: 10, left: 10, bottom: 10, right: 10 )
        layout.itemSize = CGSize(width: 20, height: 20)
        layout.scrollDirection = .vertical
        let collection = UICollectionView(frame: CGRect.zero, collectionViewLayout: layout)
        collection.register(UICollectionViewCell.self, forCellWithReuseIdentifier: self.description)
        collection.delegate = self
        collection.dataSource = self
        collection.bounces = false
        collection.showsVerticalScrollIndicator = false
        collection.showsHorizontalScrollIndicator = false
        collection.backgroundColor = UIColor.black
        collection.layer.cornerRadius = 4
        collection.backgroundColor = KLineConfig.themeManager.dividerColor()
        // 阴影
        collection.clipsToBounds = false
        collection.setShadow(sColor: UIColor.black,
                             offset: CGSize.zero,
                             opacity: KlineLibThemeManager.currentTheme == .dark ? 0.2 : 0.12,
                             radius: 4)
        return collection
    }()
    
    public lazy var tableView: UITableView = {
        let v = UITableView()
        v.delegate = self
        v.dataSource = self
        v.separatorStyle = .none
        v.showsVerticalScrollIndicator = false
        v.showsHorizontalScrollIndicator = false
        v.backgroundColor = UIColor.black
        v.contentInset = UIEdgeInsets(top: 4, left: 0, bottom: 4, right: 0)
        v.layer.cornerRadius = 4
        v.backgroundColor = KLineConfig.themeManager.dividerColor()
        // 阴影
        v.clipsToBounds = false
        v.setShadow(sColor: UIColor.black,
                    offset: CGSize.zero,
                    opacity: KlineLibThemeManager.currentTheme == .dark ? 0.2 : 0.12,
                    radius: 4)
        return v
    }()
    
    public lazy var backView: UIButton = {
        let v = UIButton()
        v.backgroundColor = UIColor.clear
        v.addTarget(self, action: #selector(backgroundClick), for: .touchUpInside)
        return v
    }()
    
    /// 颜色设置
    public lazy var colorSetL: UILabel = {
        let v = UILabel()
        v.cornerRadius = 4.0
        return v
    }()
    
    /// 宽度设置
    public lazy var widthLineV: KLineDrawlineView = {
        let v = KLineDrawlineView()
        return v
    }()
    
    /// 线段类型设置
    public lazy var typeLineV: KLineDrawlineView = {
        let v = KLineDrawlineView()
        return v
    }()
    
    /// 锁定设置
    public lazy var lockL: UILabel = {
        let v = UILabel().textFont(.systemFont(ofSize: 20))
                         .textColor(KLineConfig.themeManager.klineLibSubTextColor())
                         .text(unLockIcon)
        v.highlightedTextColor = KLineConfig.themeManager.klineLibDefaultTextColor()
        return v
    }()
    
    /// 删除功能
    public lazy var deleteL: UILabel = {
        let v = UILabel().textFont(.systemFont(ofSize: 20))
                         .textColor(KLineConfig.themeManager.klineLibSubTextColor())
                         .text("\u{e7f8}")
        v.highlightedTextColor = KLineConfig.themeManager.klineLibDefaultTextColor()
        return v
    }()
    
    /// 锁定点击按钮
    public var lockBtn: UIButton?
    /// 删除点击按钮
    public var deleteBtn: UIButton?
    
    public var currentIconL: UILabel?
    
    public var currentDrawlineView: KLineDrawlineView?

    public init() {
        super.init(frame: CGRect.zero)
        self.backgroundColor = KLineConfig.themeManager.dividerColor()
        self.cornerRadius = 4
        
        let iconL = UILabel().textFont(.systemFont(ofSize: 20))
                             .textColor(KLineConfig.themeManager.klineLibSubTextColor())
                             .text("\u{e7fa}")
        self.addSubview(iconL)
        iconL.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalToSuperview().offset(20)
        }
        
        let list: [KLineDrawSettingType] = [.lineColor, .lineWidth, .lineType, .lineLock, .lineDelete]
        var left = 54
        for item in list {
            let btn = UIButton(frame: CGRect.zero)
            btn.addTarget(self, action: #selector(buttonClick(_ :)), for: .touchUpInside)
            btn.tag = item.rawValue
            self.addSubview(btn)
            
            var buttonW = 50
            var x = 0
            
            switch item {
            case .lineColor:
                p_setUpLineColor(btn)
            case .lineWidth:
                p_setUpLineWidth(btn)
            case .lineType:
                p_setUpLineType(btn)
            case .lineLock:
                buttonW = 40
                x = 2
                lockBtn = btn
                p_setUpLineLock(btn)
            case .lineDelete:
                buttonW = 40
                x = 2
                deleteBtn = btn
                p_setUpLineDelete(btn)
            }
            
            btn.snp.makeConstraints { make in
                make.width.equalTo(buttonW)
                make.height.equalToSuperview()
                make.centerY.equalToSuperview()
                make.leading.equalToSuperview().offset(left + x).priority(.medium)
                make.trailing.lessThanOrEqualTo(self).offset(0)
            }
            
            left = left + buttonW + x
        }
        
        // 更新设置
        self.p_reloadColorSet()
        self.p_reloadLineTypeSet()
        self.p_reloadLineWidthSet()
        
        self.deleteBtn?.isEnabled = false
        self.lockBtn?.isEnabled = false
        
        // 阴影
        self.clipsToBounds = false
        self.setShadow(sColor: UIColor.black,
                       offset: CGSize.zero,
                       opacity: KlineLibThemeManager.currentTheme == .dark ? 0.2 : 0.12,
                       radius: 4)
    }
    
    /// 设置线颜色
    public func p_setUpLineColor(_ v: UIView) {
        v.addSubview(colorSetL)
        colorSetL.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.centerX.equalToSuperview().offset(-6)
            make.width.height.equalTo(20)
        }
        
        let iconL = UILabel().textFont(.systemFont(ofSize: 9))
                             .textColor(KLineConfig.themeManager.klineLibSubTextColor())
                             .text("\u{e7f5}")
        iconL.tag = 999
        v.addSubview(iconL)
        iconL.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(colorSetL.snp.trailing).offset(3)
        }
    }
    
    /// 设置线宽度
    public func p_setUpLineWidth(_ v: UIView) {
        widthLineV.tag = 99
        v.addSubview(widthLineV)
        widthLineV.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.centerX.equalToSuperview().offset(-6)
            make.width.height.equalTo(20)
        }
        
        let iconL = UILabel().textFont(.systemFont(ofSize: 9))
                             .textColor(KLineConfig.themeManager.klineLibSubTextColor())
                             .text("\u{e7f5}")
        iconL.tag = 999
        v.addSubview(iconL)
        iconL.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(widthLineV.snp.trailing).offset(3)
        }
    }
    
    /// 设置线类型
    public func p_setUpLineType(_ v: UIView) {
        typeLineV.tag = 99
        v.addSubview(typeLineV)
        typeLineV.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.centerX.equalToSuperview().offset(-6)
            make.width.height.equalTo(20)
        }
        
        let iconL = UILabel().textFont(.systemFont(ofSize: 9))
                             .textColor(KLineConfig.themeManager.klineLibSubTextColor())
                             .text("\u{e713}")
        iconL.tag = 999
        v.addSubview(iconL)
        iconL.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(typeLineV.snp.trailing).offset(3)
        }
    }
    
    /// 设置锁定
    public func p_setUpLineLock(_ v: UIView) {
        v.addSubview(lockL)
        lockL.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
    }
    
    /// 设置删除
    public func p_setUpLineDelete(_ v: UIView) {
        v.addSubview(deleteL)
        deleteL.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc public func buttonClick(_ sender: UIButton) {
        let type = KLineDrawSettingType(rawValue: sender.tag) ?? .lineColor
        
        self.backgroundClick()
        
        if type != .lineLock, type != .lineDelete {
            guard let superV = self.superview else { return }
            
            // 图标展开
            if let iconL = sender.viewWithTag(999) as? UILabel {
                iconL.text("\u{e809}")
                iconL.textColor(KLineConfig.themeManager.klineLibDefaultTextColor())
                currentIconL = iconL
            }
            
            if let drawlineView = sender.viewWithTag(99) as? KLineDrawlineView {
                drawlineView.color = KLineConfig.themeManager.klineLibDefaultTextColor()
                currentDrawlineView = drawlineView
            }
            
            for v in self.backView.subviews {
                v.removeFromSuperview()
            }
            if self.backView.superview != nil { self.backView.removeFromSuperview() }
            
            superV.addSubview(self.backView)
            superV.bringSubviewToFront(self)
            self.backView.snp.remakeConstraints { make in
                make.edges.equalToSuperview()
            }
        }
        
        self.currentType = type
        
        switch type {
        case .lineColor:
            p_showLineColor(sender)
        case .lineWidth:
            p_showLineWidth(sender)
        case .lineType:
            p_showLineType(sender)
        case .lineLock:
            p_updateLockState()
        case .lineDelete:
            p_deleteDrawModel()
        }
    }
    
    @objc public func backgroundClick() {
        if let iconL = self.currentIconL {
            iconL.textColor(KLineConfig.themeManager.klineLibSubTextColor())
                 .text("\u{e713}")
            self.currentIconL = nil
        }
        
        if let drawlineView = self.currentDrawlineView {
            drawlineView.color = KLineConfig.themeManager.klineLibSubTextColor()
            self.currentDrawlineView = nil
        }
        
        self.p_reloadSelectButton()
        if let drawModel = KLineDrawViewModel.shared.currentDrawModel, drawModel.lockDraw == true {
            self.lockL.text = lockIcon
            self.lockL.isHighlighted = true
        } else {
            self.lockL.text = unLockIcon
            self.lockL.isHighlighted = false
        }
        self.currentType = nil
        self.backView.removeFromSuperview()
    }
    
    public func p_showLineColor(_ sender: UIButton) {
        self.backView.addSubview(self.collection)
        self.collection.snp.remakeConstraints { make in
            make.width.equalTo(100)
            make.height.equalTo(70)
            make.centerX.equalTo(sender.snp.centerX)
            if self.frame.origin.y < 100 {
                make.top.equalTo(sender.snp.bottom).offset(2)
            } else {
                make.bottom.equalTo(sender.snp.top).offset(-2)
            }
        }
    }
    
    public func p_showLineWidth(_ sender: UIButton) {
        self.backView.addSubview(self.tableView)
        self.tableView.snp.remakeConstraints { make in
            make.width.equalTo(50)
            make.height.equalTo(104)
            make.centerX.equalTo(sender.snp.centerX)
            if self.frame.origin.y < 132 {
                make.top.equalTo(sender.snp.bottom).offset(2)
            } else {
                make.bottom.equalTo(sender.snp.top).offset(-2)
            }
        }
        self.dataList = self.lineWithList
        self.tableView.reloadData()
    }
    
    public func p_showLineType(_ sender: UIButton) {
        self.backView.addSubview(self.tableView)
        self.tableView.snp.remakeConstraints { make in
            make.width.equalTo(50)
            make.height.equalTo(104)
            make.centerX.equalTo(sender.snp.centerX)
            if self.frame.origin.y < 132 {
                make.top.equalTo(sender.snp.bottom).offset(2)
            } else {
                make.bottom.equalTo(sender.snp.top).offset(-2)
            }
        }
        
        self.dataList = self.lineTypeList
        self.tableView.reloadData()
    }
    
    /// 锁/解锁画线
    public func p_updateLockState() {
        guard let drawModel = KLineDrawViewModel.shared.currentDrawModel else { return }
        if lockL.tag == unLockTag {
            lockL.text = lockIcon
            lockL.tag = lockTag
            lockL.isHighlighted = true
            drawModel.lockDraw = true
        } else {
            lockL.tag = unLockTag
            lockL.text = unLockIcon
            lockL.isHighlighted = false
            drawModel.lockDraw = false
        }
        
        self.p_updateModelSetting()
    }
    
    /// 删除画线
    public func p_deleteDrawModel() {
        guard let drawModel = KLineDrawViewModel.shared.currentDrawModel else { return }
        self.backgroundClick()
        KLineDrawViewModel.shared.removeDrawModel(drawModel)
        
        self.p_reloadSelectButton()
        self.isHidden = true
    }
    
    public func reset() {
        guard currentType != nil else { return }
        self.backgroundClick()
    }
}

// MARK: 更新设置
extension KLineDrawSettingView {
    /// 更新设置颜色
    public func p_reloadColorSet() {
        colorSetL.backgroundColor = KLineDrawViewModel.shared.drawParam.drawThemColor.hexColor()
        p_updateModelSetting()
    }
    
    /// 更新设置线段宽度
    public func p_reloadLineWidthSet() {
        widthLineV.lineWidth = KLineDrawViewModel.shared.drawParam.drawLineWidth
        p_updateModelSetting()
    }
    
    /// 更新设置线段类型设置
    public func p_reloadLineTypeSet() {
        typeLineV.lineDashes = KLineDrawViewModel.shared.drawParam.drawLineDashes
        p_updateModelSetting()
    }
    
    /// 更新需要选择线按钮
    public func p_reloadSelectButton() {
        if KLineDrawViewModel.shared.currentDrawModel != nil {
            self.lockBtn?.isEnabled = true
            self.deleteBtn?.isEnabled = true
            self.deleteL.isHighlighted = true
        } else {
            self.lockBtn?.isEnabled = false
            self.deleteBtn?.isEnabled = false
            self.lockL.isHighlighted = false
            self.deleteL.isHighlighted = false
            self.lockL.tag = unLockTag
            self.lockL.text = unLockIcon
        }
    }
    
    /// 获取当前选中的设置
    public func p_reloadDataFromModel(_ model: KLineDrawModel) {
        colorSetL.backgroundColor = UIColor(model.fillColor)
        widthLineV.lineWidth = model.lineThickness
        typeLineV.lineDashes = model.lineDashes
        if model.lockDraw {
            lockL.text = lockIcon
            lockL.isHighlighted = true
        } else {
            lockL.text = unLockIcon
            lockL.isHighlighted = false
        }
    }
    
    /// 更新当前线段设置
    public func p_updateModelSetting() {
        guard let model = KLineDrawViewModel.shared.currentDrawModel else { return }
        if let c = colorSetL.backgroundColor {
            model.fillColor = c.toHex() ?? ""
        }
        model.lineThickness = widthLineV.lineWidth
        model.lineDashes = typeLineV.lineDashes
        model.lockDraw = lockL.tag == 1
        KLineDrawViewModel.shared.updateDrawParam()
    }
    
    /// 更新数据
    public func reloadDrawModel() {
        let drawModel = KLineDrawViewModel.shared.currentDrawModel
        if let model = drawModel {
            self.p_reloadDataFromModel(model)
        } else {
            self.backgroundClick()
        }
        self.p_reloadSelectButton()
    }
}

// MARK: 数据选择
extension KLineDrawSettingView: UICollectionViewDelegate, UICollectionViewDataSource {
    public func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return colorList.count
    }
    
    // swiftlint:disable:next all
    public func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: self.description, for: indexPath)
        cell.backgroundColor = colorList[indexPath.item]
        cell.cornerRadius = 4.0
        return cell
    }
    
    public func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        self.backgroundClick()
        KLineDrawViewModel.shared.drawParam.drawThemColor = self.colorList[indexPath.item].toHex(includeAlpha: false)
        self.p_reloadColorSet()
        
        KLineDrawViewModel.shared.saveDrawParam()
    }
}

extension KLineDrawSettingView: UITableViewDataSource, UITableViewDelegate {
    public func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return self.dataList.count
    }
    
    public func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 24
    }
    
    public func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        func defaultCell() -> KLineDrawToolLineCell {
            return KLineDrawToolLineCell(style: .default, reuseIdentifier: self.description)
        }
        // swiftlint:disable:next all
        let cell = tableView.dequeueReusableCell(withIdentifier: self.description) as? KLineDrawToolLineCell ?? defaultCell()
        cell.backgroundColor = UIColor.clear
        if let item = dataList[indexPath.row] as? CGFloat {
            cell.drawLineV.lineWidth = item
            cell.drawLineV.lineDashes = [0, 0]
        }
        if let item = dataList[indexPath.row] as? [CGFloat] {
            cell.drawLineV.lineWidth = 1
            cell.drawLineV.lineDashes = item
        }
        return cell
    }
    
    public func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        self.backgroundClick()
        if let item = dataList[indexPath.row] as? CGFloat {
            KLineDrawViewModel.shared.drawParam.drawLineWidth = item
            p_reloadLineWidthSet()
        }
        if let item = dataList[indexPath.row] as? [CGFloat] {
            KLineDrawViewModel.shared.drawParam.drawLineDashes = item
            p_reloadLineTypeSet()
        }
    }
}
