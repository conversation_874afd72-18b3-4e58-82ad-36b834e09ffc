//
//  FILE.swift
//  Ourbit
//
//  Created by pippen on 2024.
//

import Foundation
import UIKit

#if canImport(SwiftUI)
import SwiftUI

public struct KLineDrawToolSwiftUIView: UIViewRepresentable {
    @Binding public var selectedTool: KLineDrawToolBoxType?
    @Binding public var selectedShape: KLineDrawShapeType?
    
    private let view = KLineDrawToolView()
    
    public init(selectedTool: Binding<KLineDrawToolBoxType?>, selectedShape: Binding<KLineDrawShapeType?>) {
        self._selectedTool = selectedTool
        self._selectedShape = selectedShape
    }
    
    public func makeUIView(context: Context) -> KLineDrawToolView {
        view.delegate = context.coordinator
        return view
    }
    
    public func updateUIView(_ uiView: KLineDrawToolView, context: Context) {
        // 同步外部状态变化
        uiView.reloadDrawShapeStatus()
    }
    
    public func makeCoordinator() -> Coordinator {
        Coordinator(parent: self)
    }
    
    public class Coordinator: NSObject, KLineDrawToolViewDelegate {
        let parent: KLineDrawToolSwiftUIView
        
        init(parent: KLineDrawToolSwiftUIView) {
            self.parent = parent
        }
        
        public func drawShapeDidChange(_ type: KLineDrawShapeType) {
            parent.selectedShape = type
        }
        
        public func drawToolDidSelected(_ type: KLineDrawToolBoxType) {
            parent.selectedTool = type
        }
    }
}

public struct KLineDrawToolPopSwiftUIView: UIViewRepresentable {
    @Binding public var isPresented: Bool
    public let options: [KLineDrawToolModel]
    public let onSelect: (KLineDrawToolModel) -> Void
    
    private let popView = KLineDrawToolPopView()
    
    public init(isPresented: Binding<Bool>, options: [KLineDrawToolModel], onSelect: @escaping (KLineDrawToolModel) -> Void) {
        self._isPresented = isPresented
        self.options = options
        self.onSelect = onSelect
    }
    
    public func makeUIView(context: Context) -> KLineDrawToolPopView {
        popView.changeBlock = { model in
            if let model = model {
                onSelect(model)
            }
            isPresented = false
        }
        return popView
    }
    
    public func updateUIView(_ uiView: KLineDrawToolPopView, context: Context) {
        if isPresented {
            // 计算显示位置（示例逻辑，需根据实际布局调整）
            let superview = UIApplication.shared.windows.first?.rootViewController?.view
            uiView.showPopView(options, superview, 0)
        } else {
            uiView.closePopView(nil)
        }
    }
}


#endif

public protocol KLineDrawToolViewDelegate: NSObjectProtocol {
    /// 功能点击
    func drawShapeDidChange(_ type: KLineDrawShapeType)
    
    /// 画线工具选中
    func drawToolDidSelected(_ type: KLineDrawToolBoxType)
}

open class KLineDrawToolView: UIView {
    /// 功能
    public lazy var tableView: UITableView = {
        let v = UITableView()
        v.delegate = self
        v.dataSource = self
        v.separatorStyle = .none
        v.showsVerticalScrollIndicator = false
        v.showsHorizontalScrollIndicator = false
        v.isScrollEnabled = false
        v.layer.cornerRadius = 4
        v.backgroundColor = UIColor.clear
        return v
    }()
    
    /// 弹窗选择窗口
    public lazy var popView: KLineDrawToolPopView = {
        let v = KLineDrawToolPopView()
        v.changeBlock = { [weak self] model in
            guard let self = self,
                  let popModel = self.popToolModel,
                  let indexPath = self.popIndexPath else { return }
            var reloadIndex: [IndexPath] = [indexPath]
            // 更新选中图形
            if let m = model {
                self.popToolModel?.drawShape = m.drawShape
                var i = 0
                for toolModel in self.dataList {
                    if toolModel.toolBox == .hideDraw {
                        toolModel.toolBox = .showDraw
                        reloadIndex.append(IndexPath(row: i, section: 0))
                        break
                    }
                    i += 1
                }
                self.delegate?.drawShapeDidChange(m.drawShape)
            }
            self.popToolModel?.isSelected = false
            self.tableView.reloadRows(at: reloadIndex, with: .none)
            // 重置数据
            self.popIndexPath = nil
            self.popToolModel = nil
        }
        return v
    }()
    
    /// 功能区
    public var dataList: [KLineDrawToolModel] = []
    
    /// 当前弹窗Model
    public var popToolModel: KLineDrawToolModel?
    
    public var popIndexPath: IndexPath?
    
    /// tips的位置
    public var tipsTop: CGFloat {
        return cellHeight * 2.5
    }
    
    public var dividerHeight: CGFloat = 10
    public var cellHeight: CGFloat = 20
    
    /// 代理
    public weak var delegate: KLineDrawToolViewDelegate?
    
    public init() {
        super.init(frame: CGRect.zero)
        
        let showType: KLineDrawToolBoxType = KLineDrawViewModel.shared.hiddenDraw ? .hideDraw : .showDraw
        self.dataList = [
            KLineDrawToolModel(.drawLine),
            KLineDrawToolModel(.drawShape, .rectangle),
            KLineDrawToolModel(.drawWave, .threeWaves),
            KLineDrawToolModel(.repeatDraw),
            KLineDrawToolModel(.fibonacci),
            KLineDrawToolModel(.divider),
            KLineDrawToolModel(showType),
            KLineDrawToolModel(.clean),
            KLineDrawToolModel(.share),
            KLineDrawToolModel(.exit)
        ]
        
        self.backgroundColor = UIColor.clear
        self.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        CusstomNotification.observe(self, Noti.Data.klineThemeSkinDidChanged) { [weak self] _ in
            guard let self = self else { return }
            self.backgroundColor = KLineConfig.themeManager.bgCard()
        }
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    open override func layoutSubviews() {
        super.layoutSubviews()
        let h = self.frame.height / CGFloat(self.dataList.count)
        if h < 10 {
            cellHeight = h
            dividerHeight = h
        } else {
            dividerHeight = 10
            cellHeight = (self.frame.height - dividerHeight) / CGFloat(self.dataList.count - 1)
        }
        self.tableView.reloadData()
    }
    
    /// 更新画线状态
    public func reloadDrawShapeStatus() {
        var indexPath: [IndexPath] = []
        var row = 0
        for model in self.dataList {
            if model.toolBox == .drawLine
                || model.toolBox == .drawShape
                || model.toolBox == .fibonacci
                || model.toolBox == .drawWave {
                indexPath.append(IndexPath(row: row, section: 0))
            }
            row += 1
        }
        tableView.reloadRows(at: indexPath, with: .none)
    }
    deinit{
        CusstomNotification.remove(self, Noti.Data.klineThemeSkinDidChanged)
    }
}

extension KLineDrawToolView: UITableViewDataSource, UITableViewDelegate {
    public func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return self.dataList.count
    }
    
    public func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        let model = dataList[indexPath.row]
        if model.toolBox == .divider {
            return dividerHeight
        }
        return cellHeight
    }
    
    public func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        func defaultCell() -> KLineDrawFunctionCell {
            return KLineDrawFunctionCell(style: .default, reuseIdentifier: self.description)
        }
        // swiftlint:disable:next all
        let cell = tableView.dequeueReusableCell(withIdentifier: self.description) as? KLineDrawFunctionCell ?? defaultCell()
        let toolBoxModel = self.dataList[indexPath.row]
        cell.iconL.text = toolBoxModel.iconL
        cell.lineView.isHidden = toolBoxModel.toolBox != .divider
        cell.statusL.isHidden = (toolBoxModel.toolBox != .drawLine
                                 && toolBoxModel.toolBox != .drawShape
                                 && toolBoxModel.toolBox != .drawWave)
        cell.isSelected = toolBoxModel.isSelected
        
        // 画线，画形状
        if toolBoxModel.toolBox == .drawLine
            || toolBoxModel.toolBox == .drawShape
            || toolBoxModel.toolBox == .drawWave {
            // 如果选中了画线类型，也需要高亮
            if cell.isSelected == false, let curShape = KLineDrawViewModel.shared.drawShapeType {
                cell.isSelected = toolBoxModel.drawShape == curShape
            }
        } else if toolBoxModel.toolBox == .fibonacci {
            cell.isSelected = (KLineDrawViewModel.shared.drawShapeType == .fibonacci)
        }
        cell.backgroundColor = UIColor.clear
        return cell
    }
    
    public func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        let toolBoxModel = self.dataList[indexPath.row]
        let toolType = toolBoxModel.toolBox
        
        defer {
            tableView.reloadRows(at: [indexPath], with: .none)
        }
        
        // 有子项的，弹窗
        if let list = toolBoxModel.childList {
            toolBoxModel.isSelected = true
            self.popToolModel = toolBoxModel
            self.popIndexPath = indexPath
            let cellHeight = self.frame.height / CGFloat(self.dataList.count) * CGFloat(indexPath.row)
            self.popView.showPopView(list, self.superview, cellHeight)
            return
        }
        
        // 清空数据
        if let path = self.popIndexPath {
            self.popToolModel?.isSelected = false
            tableView.reloadRows(at: [path], with: .none)
        }
        self.popIndexPath = nil
        self.popToolModel = nil
        
        if toolType == .hideDraw {
            toolBoxModel.toolBox = .showDraw
        } else if toolType == .showDraw {
            toolBoxModel.toolBox = .hideDraw
        } else if toolType == .repeatDraw {
            toolBoxModel.isSelected = !KLineDrawViewModel.shared.isRepeatDraw
        }
        
        self.delegate?.drawToolDidSelected(toolBoxModel.toolBox)
    }
}

open class KLineDrawToolPopView: UIView {
    public lazy var popTableView: UITableView = {
        let v = UITableView()
        v.delegate = self
        v.dataSource = self
        v.separatorStyle = .none
        v.showsVerticalScrollIndicator = false
        v.showsHorizontalScrollIndicator = false
        v.isScrollEnabled = false
        v.layer.cornerRadius = 4
        v.backgroundColor = KLineConfig.themeManager.dividerColor()
        return v
    }()
    
    /// 弹窗数据
    // swiftlint:disable:next all
    public var popDataList: [KLineDrawToolModel]?
    
    // swiftlint:disable:next all
    public var changeBlock: ((KLineDrawToolModel?) -> Void)?
    
    public init() {
        super.init(frame: .zero)
        self.cornerRadius = 4
        self.addTapGestureRecognizer { [weak self] _ in
            self?.closePopView(nil)
        }
        changeTheme()
        CusstomNotification.observe(self, Noti.Data.klineThemeSkinDidChanged) { [weak self] _ in
            guard let self = self else { return }
            self.changeTheme()
        }
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    private func changeTheme(){
        popTableView.backgroundColor = KLineConfig.themeManager.dividerColor()
    }
    public func showPopView(_ dataList: [KLineDrawToolModel], _ superView: UIView?, _ cellHieght: CGFloat) {
        guard let v = superView else { return }
        if self.superview != nil { self.removeFromSuperview() }
        if self.popTableView.superview != nil { self.popTableView.removeFromSuperview() }
        
        self.popDataList = dataList
        
        var width: CGFloat = 34
        let attributes = [NSAttributedString.Key.font: UIFont.systemFont(ofSize: 11, weight: .medium)]
        for model in dataList {
            let option = NSStringDrawingOptions.usesLineFragmentOrigin
            let rect = model.title.boundingRect(with: size,
                                                options: option,
                                                attributes: attributes,
                                                context: nil)
            width = max(width, rect.size.width + 34)
        }
        width += 8.0
        
        v.addSubview(self)
        self.backgroundColor = UIColor.clear
        self.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
        }
        v.addSubview(self.popTableView)
        self.popTableView.snp.remakeConstraints { make in
            make.trailing.equalToSuperview().offset(-60)
            make.top.equalToSuperview().offset(59 + cellHieght)
            make.width.equalTo(width)
            make.height.equalTo(32 * (self.popDataList?.count ?? 0))
        }
        self.popTableView.reloadData()
    }
    
    public func closePopView(_ model: KLineDrawToolModel?) {
        self.changeBlock?(model)
        self.popTableView.removeFromSuperview()
        self.removeFromSuperview()
    }
    deinit{
        CusstomNotification.remove(self, Noti.Data.klineThemeSkinDidChanged)
    }
}

extension KLineDrawToolPopView: UITableViewDataSource, UITableViewDelegate {
    public func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int { return popDataList?.count ?? 0
    }
    
    public func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat { return 32 }
    
    public func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        func defaultCell() -> KLineDrawToolCell {
            return KLineDrawToolCell(style: .default, reuseIdentifier: self.description)
        }
        // swiftlint:disable:next all
        let cell = tableView.dequeueReusableCell(withIdentifier: self.description) as? KLineDrawToolCell ?? defaultCell()
        cell.backgroundColor = UIColor.clear
        guard let toolModel = self.popDataList?[indexPath.row] else { return cell }
        cell.textL.text = toolModel.title
        cell.iconL.text = toolModel.iconL
        return cell
    }
    
    public func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        guard let toolModel = self.popDataList?[indexPath.row] else { return }
        toolModel.isSelected = false
        self.closePopView(toolModel)
    }
}
