//
//  OBKLineDrawToolModel.swift
//  KLineDemo
//
//  Created by <PERSON> on 2025/3/25.
//

import Foundation
open class KLineDrawToolModel: NSObject {
    /// 工具箱
    public var toolBox: KLineDrawToolBoxType = .drawLine
    
    /// 画的形状
    public var drawShape: KLineDrawShapeType = .segmentLine
    
    /// 子项
    // swiftlint:disable:next all
    public var childList: [KLineDrawToolModel]?
    
    /// icon
    public var iconL: String {
        if toolBox == .drawLine || toolBox == .drawShape || toolBox == .drawWave {
            return drawShape.iconL()
        }
        return toolBox.iconL()
    }
    
    /// 标题
    public var title: String {
        if toolBox == .drawLine || toolBox == .drawShape || toolBox == .drawWave  {
            return drawShape.title.BPLocalized()
        }
        return ""
    }
    
    public var isSelected = false
    
    public init(_ toolBox: KLineDrawToolBoxType, _ type: KLineDrawShapeType = .segmentLine, _ child: Bool = false) {
        super.init()
        
        self.toolBox = toolBox
        self.drawShape = type
        
        guard !child else { return }
        
        // swiftlint:disable:next all
        var shapeTypeList: [KLineDrawShapeType]?
        if toolBox == .drawLine {
            // 线段
            shapeTypeList = [.segmentLine, .horizontalLine, .radialLine, .verticalLine, .priceLine, .parallelChannel]
        } else if toolBox == .drawShape {
            // 形状
            shapeTypeList = [.rectangle, .parallelogram]
        } else if toolBox == .drawWave {
            // 形状
            shapeTypeList = [.threeWaves, .fiveWaves]
        }
           
        guard let shapeType = shapeTypeList else {
            return
        }
        
        self.childList = []
        for type in shapeType {
            let model = KLineDrawToolModel(toolBox, type, true)
            self.childList?.append(model)
        }
    }
}
