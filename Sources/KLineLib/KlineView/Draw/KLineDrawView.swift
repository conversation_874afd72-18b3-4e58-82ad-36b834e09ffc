//
//  FILE.swift
//  Ourbit
//
//  Created by pippen on 2024.
//

import UIKit

open class KLineDrawView: UIView {
    public let drawViewModel = KLineDrawViewModel.shared
    
    /// 放大镜
    public lazy var magnifierView: KlineMagnifierView = {
        let v = KlineMagnifierView()
        v.target = self.superview
        return v
    }()
    
    /// 事件
    public lazy var tapGesture = UITapGestureRecognizer(target: self, action: #selector(tapGestureHandler(_:)))
    public lazy var draggedGesture = UIPanGestureRecognizer(target: self, action: #selector(draggedHandler(_:)))
    
    /// 是否启用事件
    public var enableEvent = false {
        didSet {
            tapGesture.isEnabled = enableEvent
            draggedGesture.isEnabled = enableEvent
        }
    }
    
    public init() {
        super.init(frame: CGRect.zero)
        self.backgroundColor = UIColor.clear
        
        addGestureRecognizer(tapGesture)
        addGestureRecognizer(draggedGesture)
        
        tapGesture.isEnabled = false
        draggedGesture.isEnabled = false
        
        // 画图对象发生改变
        CusstomNotification.observe(self, Noti.Data.klineDrawModeDidChanged) { [weak self] notify in
            guard let draw = notify.object as? Bool, let self = self else { return }
                 
            if draw == false {
                self.magnifierView.isHidden = true
            }
        }
    }
    
    deinit {
        CusstomNotification.remove(self, Noti.Data.klineDrawModeDidChanged)
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    /// 防止CALayer动画影响
    open override func action(for layer: CALayer, forKey event: String) -> CAAction? { return nil }
    
    /// 点击事件
    @objc func tapGestureHandler(_ sender: UITapGestureRecognizer) {
        let pt = sender.location(in: self)
        drawViewModel.drawLineTouchesEnded(pt)
    }
    
    /// 拖动事件
    @objc func draggedHandler(_ sender: UIPanGestureRecognizer) {
        var pt = sender.location(in: self)
        var pointModel: KLineDrawPostion?
        if sender.state == .ended {
            pointModel = drawViewModel.calculationModelPoint(pt)
        }
        
        // 过滤异常点
        pt.x = max(pt.x, 0)
        pt.x = min(pt.x, self.frame.size.width)
        pt.y = max(pt.y, 0)
        pt.y = min(pt.y, self.frame.size.height)
        
        if sender.state == .began {
            drawViewModel.drawLineTouchesBegan(pt)
        }
        
        drawViewModel.drawLineTouchesMoved(pt, pointModel, sender.state)
        
        if sender.state == .ended {
            if magnifierView.superview != nil { magnifierView.removeFromSuperview() }
            magnifierView.isHidden = true
            return
        }
        
        guard drawViewModel.currentDrawModel != nil else { return }
        self.magnifierView.isHidden = false
        if magnifierView.superview == nil {
            // swiftlint:disable:next all
            self.superview!.addSubview(magnifierView)
        }
        // 关闭动画
        CATransaction.begin()
        CATransaction.setDisableActions(true)
        if pt.x < self.center.x - 5 {
            magnifierView.origin = CGPoint(x: self.width - magnifierView.width - 5, y: 5)
        } else if pt.x > self.center.x + 5 {
            magnifierView.origin = CGPoint(x: 5, y: 5)
        }
        CATransaction.commit()
        magnifierView.targetPoint = pt
    }
    
    /// 更新K线
    // swiftlint:disable:next all
    public func updateKLineModels(_ models: [KLineModel]?) {
        drawViewModel.needDrawKLineModels = models
        drawViewModel.updateKLineModels(self)
    }
    
    /// 绘制数据
    open override func draw(_ rect: CGRect) {
        guard let ctx = UIGraphicsGetCurrentContext(),
              let models = drawViewModel.needDrawKLineModels,
              !models.isEmpty,
              drawViewModel.klineViewSize != .zero,
              drawViewModel.hiddenDraw == false else { return }
        
        for model in drawViewModel.drawModelList {
            let transform = CGAffineTransform(translationX: model.transformPoint.x, y: model.transformPoint.y)
            
            // 绘制形状
            model.drawShape(ctx, transform)
            
            // 绘制价格
            if model.drawShapeType == .priceLine,
               let price = model.postionList.first?.y,
               let point = model.keyPointList.first {
                let attributes: [NSAttributedString.Key: Any] = [
                    NSAttributedString.Key.foregroundColor: UIColor(model.fillColor),
                    NSAttributedString.Key.font:UIFont.monospacedDigitSystemFont(ofSize: 10, weight: UIFont.Weight.regular)
                ]
                
                let applyPoint = point.applying(transform)
                let showPrice = ShowNumber(price, .fullDown(drawViewModel.pricesScale))
                let attributeStr = NSAttributedString(string: showPrice, attributes: attributes)
                let pt = CGPoint(x: applyPoint.x, y: applyPoint.y - 14)
                attributeStr.draw(at: pt)
            }
            
            // 绘制关键点
            if model.status != .none {
                for point in model.keyPointList {
                    let pt = point.applying(transform)
                    let color = UIColor(model.fillColor)
                    drawKeyPoint(pt, ctx, color.withAlphaComponent(0.2), color)
                }
            }
        }
    }
    
    /// 画关键点
    public func drawKeyPoint(_ pt: CGPoint, _ ctx: CGContext, _ color1: UIColor, _ color2: UIColor) {
        ctx.saveGState()
        ctx.setFillColor(color1.cgColor)
        ctx.addArc(center: pt, radius: 8, startAngle: 0, endAngle: Double.pi * 2, clockwise: true)
        ctx.drawPath(using: .fill)
        
        ctx.setFillColor(color2.cgColor)
        ctx.addArc(center: pt, radius: 3.5, startAngle: 0, endAngle: Double.pi * 2, clockwise: true)
        ctx.drawPath(using: .fill)
        ctx.restoreGState()
    }
}

extension KLineDrawView {
    /// 更新视图
    public  func reloadData() { self.setNeedsDisplay() }
    
    /// 关闭放大镜
    public func closeMagnifierView() {
        if magnifierView.superview != nil { magnifierView.removeFromSuperview() }
        magnifierView.isHidden = true
    }
}
