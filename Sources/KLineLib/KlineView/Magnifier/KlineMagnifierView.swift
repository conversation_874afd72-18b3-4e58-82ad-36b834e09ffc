//
//  FILE.swift
//  Ourbit
//
//  Created by pippen on 2024.
//

import UIKit

open class KlineMagnifierView: UIView {
    private weak var contentLayer: CALayer?
    
    public var target: UIView?
    
    public var scale: CGFloat = 2
    
    public var targetPoint: CGPoint = .zero {
        didSet {
            self.layer.setNeedsDisplay()
        }
    }

    public init() {
        super.init(frame: CGRect(x: 0, y: 0, width: 90, height: 90))
        self.backgroundColor = KLineConfig.themeManager.bgPrimary()
        self.cornerRadius = self.frame.size.width * 0.5
        self.borderWidth = 1
        self.borderColor = KLineConfig.themeManager.textTertiaryColor()
        self.layer.contentsScale = UIScreen.main.scale
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    /// 防止CALayer动画影响
    open override func action(for layer: CALayer, forKey event: String) -> CAAction? { return nil }
    
    open override func draw(_ layer: CALayer, in ctx: CGContext) {
        super.draw(layer, in: ctx)
        
        guard let tg = self.target else { return }
        ctx.translateBy(x: self.frame.size.width * 0.5, y: self.frame.size.height * 0.5)
        ctx.scaleBy(x: scale, y: scale)
        ctx.translateBy(x: -1 * targetPoint.x, y: -1 * targetPoint.y)

        // 绘制放大镜内容时候，可能触发到当前View重绘，移除当前View避免循环调用
        if self.superview != nil { self.removeFromSuperview() }
        tg.layer.render(in: ctx)
        tg.addSubview(self)
    }
}
