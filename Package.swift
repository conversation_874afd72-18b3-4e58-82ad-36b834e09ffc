// swift-tools-version: 6.0
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    defaultLocalization:"zh-<PERSON>",
    platforms: [
        .iOS(.v13),
        .macOS(.v10_15)
    ],
    products: [
        .library(
            name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
            targets: ["K<PERSON><PERSON><PERSON><PERSON>"]),
    ],
    dependencies: [
        .package(url: "https://github.com/SnapKit/SnapKit.git", from: "5.7.1")
    ],
    targets: [
        // Targets are the basic building blocks of a package, defining a module or a test suite.
        // Targets can depend on other targets in this package and products from dependencies.
        .target(
            name: "<PERSON><PERSON>ine<PERSON>ib",
            dependencies: [
                .product(name: "<PERSON>nap<PERSON><PERSON>", package: "SnapKit")
            ],
            resources: [
                .process("Resources")
            ]
        ),

    ],
    swiftLanguageModes: [.v5]
)
